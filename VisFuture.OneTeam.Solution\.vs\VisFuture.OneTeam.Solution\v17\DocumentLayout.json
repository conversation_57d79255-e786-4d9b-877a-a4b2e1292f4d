{"Version": 1, "WorkspaceRootPath": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{CB1098F8-C9D3-4578-A79C-143CF5AAE5CB}|Visfuture.OneTeam.Ticket.DataAccess\\Visfuture.OneTeam.Ticket.DataAccess.csproj|d:\\repo\\one team\\visfuture.oneteam.solution\\visfuture.oneteam.ticket.dataaccess\\entities\\ticket.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{CB1098F8-C9D3-4578-A79C-143CF5AAE5CB}|Visfuture.OneTeam.Ticket.DataAccess\\Visfuture.OneTeam.Ticket.DataAccess.csproj|solutionrelative:visfuture.oneteam.ticket.dataaccess\\entities\\ticket.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{43E1B322-DFCB-4F56-9745-573CB2DFE226}|Visfuture.OneTeam.Ticket.BusinessLogic\\Visfuture.OneTeam.Ticket.BusinessLogic.csproj|d:\\repo\\one team\\visfuture.oneteam.solution\\visfuture.oneteam.ticket.businesslogic\\dtos\\ticketdto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{43E1B322-DFCB-4F56-9745-573CB2DFE226}|Visfuture.OneTeam.Ticket.BusinessLogic\\Visfuture.OneTeam.Ticket.BusinessLogic.csproj|solutionrelative:visfuture.oneteam.ticket.businesslogic\\dtos\\ticketdto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{2893E565-1C88-43C6-80CB-4B48AE612436}|Visfuture.OneTeam.Project.BusinessLogic\\Visfuture.OneTeam.Project.BusinessLogic.csproj|d:\\repo\\one team\\visfuture.oneteam.solution\\visfuture.oneteam.project.businesslogic\\projectmanager.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{2893E565-1C88-43C6-80CB-4B48AE612436}|Visfuture.OneTeam.Project.BusinessLogic\\Visfuture.OneTeam.Project.BusinessLogic.csproj|solutionrelative:visfuture.oneteam.project.businesslogic\\projectmanager.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{CB1098F8-C9D3-4578-A79C-143CF5AAE5CB}|Visfuture.OneTeam.Ticket.DataAccess\\Visfuture.OneTeam.Ticket.DataAccess.csproj|d:\\repo\\one team\\visfuture.oneteam.solution\\visfuture.oneteam.ticket.dataaccess\\dbcontext\\appdatacontext.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{CB1098F8-C9D3-4578-A79C-143CF5AAE5CB}|Visfuture.OneTeam.Ticket.DataAccess\\Visfuture.OneTeam.Ticket.DataAccess.csproj|solutionrelative:visfuture.oneteam.ticket.dataaccess\\dbcontext\\appdatacontext.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{D1B41377-544D-4565-8704-AA49A0C3C356}|Visfuture.OneTeam.Project.DataAccess\\Visfuture.OneTeam.Project.DataAccess.csproj|d:\\repo\\one team\\visfuture.oneteam.solution\\visfuture.oneteam.project.dataaccess\\dbcontext\\appdatacontext.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{D1B41377-544D-4565-8704-AA49A0C3C356}|Visfuture.OneTeam.Project.DataAccess\\Visfuture.OneTeam.Project.DataAccess.csproj|solutionrelative:visfuture.oneteam.project.dataaccess\\dbcontext\\appdatacontext.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{D1B41377-544D-4565-8704-AA49A0C3C356}|Visfuture.OneTeam.Project.DataAccess\\Visfuture.OneTeam.Project.DataAccess.csproj|d:\\repo\\one team\\visfuture.oneteam.solution\\visfuture.oneteam.project.dataaccess\\dbcontext\\basebizappdatacontext.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{D1B41377-544D-4565-8704-AA49A0C3C356}|Visfuture.OneTeam.Project.DataAccess\\Visfuture.OneTeam.Project.DataAccess.csproj|solutionrelative:visfuture.oneteam.project.dataaccess\\dbcontext\\basebizappdatacontext.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{D1B41377-544D-4565-8704-AA49A0C3C356}|Visfuture.OneTeam.Project.DataAccess\\Visfuture.OneTeam.Project.DataAccess.csproj|d:\\repo\\one team\\visfuture.oneteam.solution\\visfuture.oneteam.project.dataaccess\\entities\\projecttimelog.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{D1B41377-544D-4565-8704-AA49A0C3C356}|Visfuture.OneTeam.Project.DataAccess\\Visfuture.OneTeam.Project.DataAccess.csproj|solutionrelative:visfuture.oneteam.project.dataaccess\\entities\\projecttimelog.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{43E1B322-DFCB-4F56-9745-573CB2DFE226}|Visfuture.OneTeam.Ticket.BusinessLogic\\Visfuture.OneTeam.Ticket.BusinessLogic.csproj|d:\\repo\\one team\\visfuture.oneteam.solution\\visfuture.oneteam.ticket.businesslogic\\listitemdtos\\ticketlistitemdto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{43E1B322-DFCB-4F56-9745-573CB2DFE226}|Visfuture.OneTeam.Ticket.BusinessLogic\\Visfuture.OneTeam.Ticket.BusinessLogic.csproj|solutionrelative:visfuture.oneteam.ticket.businesslogic\\listitemdtos\\ticketlistitemdto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F703BE53-D592-4693-B671-FE607EE2BDB8}|Visfuture.OneTeam.BaseBiz.BusinessLogic\\Visfuture.OneTeam.BaseBiz.BusinessLogic.csproj|d:\\repo\\one team\\visfuture.oneteam.solution\\visfuture.oneteam.basebiz.businesslogic\\accountmanager.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F703BE53-D592-4693-B671-FE607EE2BDB8}|Visfuture.OneTeam.BaseBiz.BusinessLogic\\Visfuture.OneTeam.BaseBiz.BusinessLogic.csproj|solutionrelative:visfuture.oneteam.basebiz.businesslogic\\accountmanager.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{CB1098F8-C9D3-4578-A79C-143CF5AAE5CB}|Visfuture.OneTeam.Ticket.DataAccess\\Visfuture.OneTeam.Ticket.DataAccess.csproj|d:\\repo\\one team\\visfuture.oneteam.solution\\visfuture.oneteam.ticket.dataaccess\\entities\\ticketreview.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{CB1098F8-C9D3-4578-A79C-143CF5AAE5CB}|Visfuture.OneTeam.Ticket.DataAccess\\Visfuture.OneTeam.Ticket.DataAccess.csproj|solutionrelative:visfuture.oneteam.ticket.dataaccess\\entities\\ticketreview.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Ticket.cs", "DocumentMoniker": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Ticket.DataAccess\\Entities\\Ticket.cs", "RelativeDocumentMoniker": "Visfuture.OneTeam.Ticket.DataAccess\\Entities\\Ticket.cs", "ToolTip": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Ticket.DataAccess\\Entities\\Ticket.cs", "RelativeToolTip": "Visfuture.OneTeam.Ticket.DataAccess\\Entities\\Ticket.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAkwDAAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T17:50:57.811Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ProjectManager.cs", "DocumentMoniker": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.BusinessLogic\\ProjectManager.cs", "RelativeDocumentMoniker": "Visfuture.OneTeam.Project.BusinessLogic\\ProjectManager.cs", "ToolTip": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.BusinessLogic\\ProjectManager.cs", "RelativeToolTip": "Visfuture.OneTeam.Project.BusinessLogic\\ProjectManager.cs", "ViewState": "AgIAADIBAAAAAAAAAAAuwD8BAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T12:34:03.342Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "ProjectTimeLog.cs", "DocumentMoniker": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.DataAccess\\Entities\\ProjectTimeLog.cs", "RelativeDocumentMoniker": "Visfuture.OneTeam.Project.DataAccess\\Entities\\ProjectTimeLog.cs", "ToolTip": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.DataAccess\\Entities\\ProjectTimeLog.cs", "RelativeToolTip": "Visfuture.OneTeam.Project.DataAccess\\Entities\\ProjectTimeLog.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAYwBQAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T12:39:22.694Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "BaseBizAppDataContext.cs", "DocumentMoniker": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.DataAccess\\DbContext\\BaseBizAppDataContext.cs", "RelativeDocumentMoniker": "Visfuture.OneTeam.Project.DataAccess\\DbContext\\BaseBizAppDataContext.cs", "ToolTip": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.DataAccess\\DbContext\\BaseBizAppDataContext.cs", "RelativeToolTip": "Visfuture.OneTeam.Project.DataAccess\\DbContext\\BaseBizAppDataContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T14:19:43.115Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "AppDataContext.cs", "DocumentMoniker": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.DataAccess\\DbContext\\AppDataContext.cs", "RelativeDocumentMoniker": "Visfuture.OneTeam.Project.DataAccess\\DbContext\\AppDataContext.cs", "ToolTip": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.DataAccess\\DbContext\\AppDataContext.cs", "RelativeToolTip": "Visfuture.OneTeam.Project.DataAccess\\DbContext\\AppDataContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T19:10:27.412Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "AppDataContext.cs", "DocumentMoniker": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Ticket.DataAccess\\DbContext\\AppDataContext.cs", "RelativeDocumentMoniker": "Visfuture.OneTeam.Ticket.DataAccess\\DbContext\\AppDataContext.cs", "ToolTip": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Ticket.DataAccess\\DbContext\\AppDataContext.cs", "RelativeToolTip": "Visfuture.OneTeam.Ticket.DataAccess\\DbContext\\AppDataContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAHkAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T18:53:03.34Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "TicketDto.cs", "DocumentMoniker": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Ticket.BusinessLogic\\DTOs\\TicketDto.cs", "RelativeDocumentMoniker": "Visfuture.OneTeam.Ticket.BusinessLogic\\DTOs\\TicketDto.cs", "ToolTip": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Ticket.BusinessLogic\\DTOs\\TicketDto.cs", "RelativeToolTip": "Visfuture.OneTeam.Ticket.BusinessLogic\\DTOs\\TicketDto.cs", "ViewState": "AgIAABsAAAAAAAAAAAAowDAAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T18:51:50.72Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "TicketListItemDto.cs", "DocumentMoniker": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Ticket.BusinessLogic\\ListItemDTOs\\TicketListItemDto.cs", "RelativeDocumentMoniker": "Visfuture.OneTeam.Ticket.BusinessLogic\\ListItemDTOs\\TicketListItemDto.cs", "ToolTip": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Ticket.BusinessLogic\\ListItemDTOs\\TicketListItemDto.cs", "RelativeToolTip": "Visfuture.OneTeam.Ticket.BusinessLogic\\ListItemDTOs\\TicketListItemDto.cs", "ViewState": "AgIAADAAAAAAAAAAAAAuwDAAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T18:52:47.102Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "AccountManager.cs", "DocumentMoniker": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.BaseBiz.BusinessLogic\\AccountManager.cs", "RelativeDocumentMoniker": "Visfuture.OneTeam.BaseBiz.BusinessLogic\\AccountManager.cs", "ToolTip": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.BaseBiz.BusinessLogic\\AccountManager.cs", "RelativeToolTip": "Visfuture.OneTeam.BaseBiz.BusinessLogic\\AccountManager.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAMAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T18:52:08.904Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "TicketReview.cs", "DocumentMoniker": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Ticket.DataAccess\\Entities\\TicketReview.cs", "RelativeDocumentMoniker": "Visfuture.OneTeam.Ticket.DataAccess\\Entities\\TicketReview.cs", "ToolTip": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Ticket.DataAccess\\Entities\\TicketReview.cs", "RelativeToolTip": "Visfuture.OneTeam.Ticket.DataAccess\\Entities\\TicketReview.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T18:47:26.368Z"}]}]}]}