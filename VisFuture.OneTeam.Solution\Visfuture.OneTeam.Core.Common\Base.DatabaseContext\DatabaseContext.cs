﻿using System.Data;
using System.Data.Common;
using System.Reflection;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Visfuture.OneTeam.Core.Common.Extensions;


namespace Visfuture.OneTeam.Core.Common.Base.DatabaseContext;

public class DatabaseContext<TContext> : DbContext where TContext : DbContext
{
    public DatabaseContext(DbContextOptions<TContext> options)
        : base(options)
    {
        Database.EnsureCreated();
    }

    // Example of using the extension methods within AppDbContext
    public async Task<List<CustomDTO>> GetCustomDataAsync()
    {
        const string sql = "SELECT Id, Name, Value FROM CustomView WHERE Value > @minValue";
        return await this.SqlQueryAsync(sql,
            reader => new CustomDTO
            {
                Id = reader.GetInt32(0),
                Name = reader.GetString(1),
                Value = reader.GetDecimal(2)
            },
            new SqlParameter("@minValue", 100));
    }

    // Example DTO for custom queries
    public class CustomDTO
    {
        public int Id { get; set; }
        public string Name { get; set; } = default!;
        public decimal Value { get; set; }
    }

    // Add additional methods to the appdbcontext
    public async Task<IList<F>> ExecuteQueryAsync<F>(string query, SqlParameter[] parameters, CancellationToken cancellationToken = default) where F : new()
    {
        DataTable dt = new();
        DbConnection conn = Database.GetDbConnection();
        try
        {
            if (conn.State != ConnectionState.Open)
            {
                conn.Open();
            }
            using DbCommand command = conn.CreateCommand();
            if (parameters != null)
            {
                int i = 0;
                foreach (var item in parameters)
                {
                    command.Parameters.Add(item);
                    if (i > 0)
                    {
                        query += ",";
                    }
                    else
                    {
                        query += " ";
                    }
                    query += item.ParameterName;
                    i++;
                }
            }
            command.CommandText = query;
            DbDataReader reader = await command.ExecuteReaderAsync(cancellationToken);
            dt.Load(reader);
            reader.Close();
            reader.Dispose();
        }
        finally
        {
            conn.Close();
        }
        IList<F> tList = [];
        Type type = typeof(F);
        string tempName = "";
        foreach (DataRow dr in dt.Rows)
        {
            F f = new F();
            PropertyInfo[] propertys = f.GetType().GetProperties();
            foreach (PropertyInfo pi in propertys)
            {
                tempName = pi.Name;
                if (dt.Columns.Contains(tempName))
                {
                    if (!pi.CanWrite) continue;
                    object value = dr[tempName];
                    if (value != DBNull.Value)
                        pi.SetValue(f, value, null);
                }
            }
            tList.Add(f);
        }
        return tList;
    }

    public async Task<int> ExecuteNonQueryAsync(string sql, SqlParameter[] parameters)
    {
        if (parameters != null)
        {
            int i = 0;
            foreach (var item in parameters)
            {
                if (i > 0)
                {
                    sql += ",";
                }
                else
                {
                    sql += " ";
                }
                sql += item.ParameterName;
                i++;
            }
            return await Database.ExecuteSqlRawAsync(sql, parameters);
        }
        return await Database.ExecuteSqlRawAsync(sql);
    }


    public int Save()
    {
        return SaveChanges();
    }

    public async Task<int> SaveAsync(CancellationToken cancellationToken = default)
    {
        return await SaveChangesAsync(cancellationToken);
    }

    public void Delete(object entity)
    {
        var dbEntityEntry = Entry(entity);
        if (dbEntityEntry.State != EntityState.Deleted)
        {
            dbEntityEntry.State = EntityState.Deleted;
        }
        else
        {
            Attach(entity);
            Remove(entity);
        }
    }

    public override int SaveChanges()
    {
        return base.SaveChanges();
    }

    public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default)
    {
        return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
    }

}
