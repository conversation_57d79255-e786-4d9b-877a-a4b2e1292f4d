﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;

public class AccessResourceListItemDto : TenantBaseDto
{
    public string Name { get; set; } = string.Empty;

    public string ResourceCode { get; set; } = null!;

    public string? Description { get; set; }

    public bool IsActive { get; set; }

    public bool IsPublic { get; set; }

    public string SubType { get; set; } = null!;

    public Guid? SuperiorId { get; set; }

    public string? SystemId { get; set; }

    public string? ModuleId { get; set; }
}