﻿using System.Text.Json;
using Visfuture.OneTeam.Core.Common.Helpers;

namespace Visfuture.OneTeam.Core.Common.Extensions;

public static class ResultExtensions
{
    public static T Match<T>(
       this Result result,
       Func<T> onSuccess,
       Func<Error, T> onFailure)
    {
        return result.IsSuccess ? onSuccess() : onFailure(result.Error);
    }

    public static TResult Match<T, TResult>(
        this Result<T> result,
        Func<T, TResult> onSuccess,
        Func<Error, TResult> onFailure) where T : class
    {
        return result.IsSuccess ? onSuccess(result.Data!) : onFailure(result.Error);
    }



    //--------------------------------------------------------------------------------
    // Testing the extension method
    public class User(string name, int age)
    {
        public string Name { get; set; } = name ?? throw new ArgumentNullException(nameof(name));
        public int Age { get; set; } = age;
    }

    public static void Test()
    {
        // return from services
        var generic = Result<User>.Success(new User("Alice", 30));
        var result = Result.Success();
        var failed = Result.Failure(new Error("12335", "333"));

        // Convert results to a valid API Response in the controller
        var generic_msg = generic.Match(
            onSuccess: data => JsonSerializer.Serialize(data),  // Results.Ok(data)
            onFailure: error => $"Error: {error}");             // Results.BadRequest(error)

        var result_msg = result.Match(
            onSuccess: () => $"Success",                        // Results.NoContent() || other appropriate https codes; for success without data
            onFailure: error => $"Error: {error}");             // Results.BadRequest(error)

        Console.WriteLine(generic_msg); // Outputs: Success: Success data
        Console.WriteLine(result_msg); // Outputs: Success: Success data
    }
}
