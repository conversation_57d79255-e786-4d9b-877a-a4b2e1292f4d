2025-05-02 09:29:18.603 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-05-02 09:29:20.698 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:29:20.702 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 7.1856 ms
2025-05-02 09:29:20.710 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 2110.1823ms
2025-05-02 09:29:20.725 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 95
2025-05-02 09:29:20.731 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:29:20.739 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:29:20.748 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:42:56.125 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-02 09:42:56.728 -04:00 [INF] Now listening on: http://localhost:5275
2025-05-02 09:42:56.834 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-02 09:42:56.840 -04:00 [INF] Hosting environment: Development
2025-05-02 09:42:56.863 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-05-02 09:43:02.884 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-05-02 09:43:02.957 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:43:02.962 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 38.6979 ms
2025-05-02 09:43:02.978 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 102.0937ms
2025-05-02 09:43:02.985 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 95
2025-05-02 09:43:02.992 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:43:03.082 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:43:03.110 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:43:04.640 -04:00 [INF] Executed DbCommand (102ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:43:04.684 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:43:05.024 -04:00 [INF] Executed DbCommand (98ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Code], [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-05-02 09:43:05.052 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:43:05.068 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 1952.948ms
2025-05-02 09:43:05.073 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:43:05.076 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 2083.8322 ms
2025-05-02 09:43:05.084 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 2098.7342ms
2025-05-02 09:43:05.098 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 90
2025-05-02 09:43:05.103 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:43:05.106 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:43:05.108 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:43:05.156 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:43:05.183 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:43:05.355 -04:00 [INF] Executed DbCommand (49ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Code], [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 09:43:05.361 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:43:05.363 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 252.2307ms
2025-05-02 09:43:05.365 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:43:05.367 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 264.6895 ms
2025-05-02 09:43:05.371 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 272.671ms
2025-05-02 09:44:41.901 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 09:44:41.905 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:44:41.906 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.2628 ms
2025-05-02 09:44:41.909 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 9.0467ms
2025-05-02 09:44:41.921 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-05-02 09:44:41.924 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:44:41.927 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:44:41.933 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:44:42.023 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:44:42.052 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:44:42.169 -04:00 [INF] Executed DbCommand (81ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 09:44:42.176 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:44:42.185 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 248.3018ms
2025-05-02 09:44:42.188 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:44:42.190 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 265.9532 ms
2025-05-02 09:44:42.192 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 271.4909ms
2025-05-02 09:44:52.247 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 09:44:52.252 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:44:52.254 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.0477 ms
2025-05-02 09:44:52.258 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 10.4913ms
2025-05-02 09:44:52.289 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 90
2025-05-02 09:44:52.293 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:44:52.296 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:44:52.297 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:44:52.321 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:44:52.350 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:44:52.458 -04:00 [INF] Executed DbCommand (96ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 09:44:52.463 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:44:52.466 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 166.3493ms
2025-05-02 09:44:52.468 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:44:52.470 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 176.8240 ms
2025-05-02 09:44:52.474 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 184.872ms
2025-05-02 09:44:53.607 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-05-02 09:44:53.611 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:44:53.612 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:44:53.614 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:44:53.639 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:44:53.664 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:44:53.772 -04:00 [INF] Executed DbCommand (103ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 09:44:53.778 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:44:53.780 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 162.9469ms
2025-05-02 09:44:53.782 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:44:53.783 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 172.4878 ms
2025-05-02 09:44:53.786 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 179.0902ms
2025-05-02 09:45:00.810 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 09:45:00.815 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:45:00.816 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.5992 ms
2025-05-02 09:45:00.819 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 9.0686ms
2025-05-02 09:45:00.824 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 90
2025-05-02 09:45:00.827 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:45:00.829 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:45:00.830 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:45:00.860 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:45:00.888 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:45:00.988 -04:00 [INF] Executed DbCommand (90ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 09:45:00.997 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:45:00.999 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 166.7118ms
2025-05-02 09:45:01.002 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:45:01.004 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 176.5390 ms
2025-05-02 09:45:01.007 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 183.0242ms
2025-05-02 09:45:02.196 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-05-02 09:45:02.200 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:45:02.201 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:45:02.204 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:45:02.229 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:45:02.258 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:45:02.355 -04:00 [INF] Executed DbCommand (92ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 09:45:02.365 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:45:02.368 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 161.5892ms
2025-05-02 09:45:02.371 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:45:02.373 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 173.6773 ms
2025-05-02 09:45:02.376 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 180.1262ms
2025-05-02 09:45:15.490 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 09:45:15.494 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:45:15.495 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.1434 ms
2025-05-02 09:45:15.497 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 6.6949ms
2025-05-02 09:45:15.502 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 90
2025-05-02 09:45:15.505 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:45:15.509 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:45:15.510 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:45:15.535 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:45:15.562 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:45:15.645 -04:00 [INF] Executed DbCommand (78ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 09:45:15.650 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:45:15.652 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 138.4887ms
2025-05-02 09:45:15.655 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:45:15.657 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 151.7608 ms
2025-05-02 09:45:15.659 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 156.8762ms
2025-05-02 09:45:18.003 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-05-02 09:45:18.006 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:45:18.008 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:45:18.009 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:45:18.034 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:45:18.060 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:45:18.154 -04:00 [INF] Executed DbCommand (89ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 09:45:18.161 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:45:18.163 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 149.9411ms
2025-05-02 09:45:18.164 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:45:18.168 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 161.4355 ms
2025-05-02 09:45:18.174 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 171.3542ms
2025-05-02 09:51:46.134 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 09:51:46.152 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:51:46.154 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.5838 ms
2025-05-02 09:51:46.156 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 22.132ms
2025-05-02 09:51:46.171 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-05-02 09:51:46.175 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:51:46.177 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:51:46.181 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:51:46.419 -04:00 [INF] Executed DbCommand (79ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:51:46.457 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:51:46.557 -04:00 [INF] Executed DbCommand (90ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 09:51:46.562 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:51:46.566 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 380.9049ms
2025-05-02 09:51:46.570 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:51:46.572 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 397.1639 ms
2025-05-02 09:51:46.576 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 404.8274ms
2025-05-02 09:51:49.722 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 09:51:49.726 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:51:49.727 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:51:49.730 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:51:49.797 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:51:49.834 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:51:49.937 -04:00 [INF] Executed DbCommand (79ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 09:51:49.943 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:51:49.949 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 214.4014ms
2025-05-02 09:51:49.953 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:51:49.955 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 229.0276 ms
2025-05-02 09:51:49.957 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 235.3995ms
2025-05-02 09:51:53.525 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - application/json; charset=utf-8 0
2025-05-02 09:51:53.598 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:51:53.611 -04:00 [INF] Route matched with {action = "GetSequenceNoByNameExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoByNameExt(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:51:53.644 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:51:53.686 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:51:53.833 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[@__name_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[CreateAt], [s].[CreateBy], [s].[CurrentNo], [s].[Length], [s].[Name], [s].[Prefix], [s].[Suffix], [s].[TenantId], [s].[UpdateAt], [s].[UpdateBy]
FROM [SequenceNo] AS [s]
WHERE [s].[Name] = @__name_0
2025-05-02 09:51:53.866 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:51:53.881 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api) in 264.3584ms
2025-05-02 09:51:53.885 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:51:53.889 -04:00 [INF] HTTP GET /External/SequenceNoByName responded 200 in 350.8449 ms
2025-05-02 09:51:53.895 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - 200 null application/json; charset=utf-8 371.9788ms
2025-05-02 09:51:54.093 -04:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5275/External/UpdateSequenceNo - application/json; charset=utf-8 321
2025-05-02 09:51:54.108 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.UpdateSequenceNoExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:51:54.121 -04:00 [INF] Route matched with {action = "UpdateSequenceNoExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateSequenceNoExt(Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:51:54.148 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:51:54.180 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:51:54.516 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[@p10='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (Size = 50), @p2='?' (DbType = Int64), @p3='?' (DbType = Int16), @p4='?' (Size = 255), @p5='?' (Size = 50), @p6='?' (Size = 50), @p7='?' (DbType = Guid), @p8='?' (DbType = DateTime2), @p9='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [SequenceNo] SET [CreateAt] = @p0, [CreateBy] = @p1, [CurrentNo] = @p2, [Length] = @p3, [Name] = @p4, [Prefix] = @p5, [Suffix] = @p6, [TenantId] = @p7, [UpdateAt] = @p8, [UpdateBy] = @p9
OUTPUT 1
WHERE [Id] = @p10;
2025-05-02 09:51:54.542 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-02 09:51:54.547 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.UpdateSequenceNoExt (Visfuture.OneTeam.BaseBiz.Api) in 420.4098ms
2025-05-02 09:51:54.552 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.UpdateSequenceNoExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:51:54.555 -04:00 [INF] HTTP PUT /External/UpdateSequenceNo responded 200 in 451.5888 ms
2025-05-02 09:51:54.560 -04:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5275/External/UpdateSequenceNo - 200 null application/json; charset=utf-8 467.5896ms
2025-05-02 09:51:55.198 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 09:51:55.206 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:51:55.208 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.6595 ms
2025-05-02 09:51:55.213 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 15.2966ms
2025-05-02 09:51:55.219 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-05-02 09:51:55.223 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:51:55.225 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:51:55.227 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:51:55.251 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:51:55.279 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:51:55.313 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 09:51:55.323 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:51:55.329 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 100.0301ms
2025-05-02 09:51:55.334 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:51:55.338 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 114.8530 ms
2025-05-02 09:51:55.345 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 125.5019ms
2025-05-02 09:52:10.751 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 09:52:10.755 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:52:10.756 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 0.9548 ms
2025-05-02 09:52:10.758 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 7.276ms
2025-05-02 09:52:10.763 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 09:52:10.768 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:52:10.770 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:52:10.771 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:52:10.794 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:52:10.827 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:52:10.920 -04:00 [INF] Executed DbCommand (82ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 09:52:10.935 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:52:10.943 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 169.526ms
2025-05-02 09:52:10.948 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:52:10.950 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 181.8431 ms
2025-05-02 09:52:10.954 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 191.1716ms
2025-05-02 09:52:22.363 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 09:52:22.370 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:52:22.371 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.7790 ms
2025-05-02 09:52:22.376 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 13.5133ms
2025-05-02 09:52:22.381 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 09:52:22.384 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:52:22.385 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:52:22.387 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:52:22.411 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:52:22.440 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:52:22.522 -04:00 [INF] Executed DbCommand (74ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 09:52:22.536 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:52:22.540 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 150.3745ms
2025-05-02 09:52:22.547 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:52:22.550 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 166.8411 ms
2025-05-02 09:52:22.560 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 179.0806ms
2025-05-02 09:54:19.373 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 09:54:19.386 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:54:19.388 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.5078 ms
2025-05-02 09:54:19.391 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 21.0626ms
2025-05-02 09:54:19.397 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-05-02 09:54:19.409 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:54:19.410 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:54:19.412 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:54:19.437 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:54:19.463 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:54:19.490 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 09:54:19.494 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:54:19.496 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 80.6914ms
2025-05-02 09:54:19.499 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:54:19.500 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 91.9729 ms
2025-05-02 09:54:19.504 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 106.9633ms
2025-05-02 09:57:33.318 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 09:57:33.329 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:57:33.330 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.3425 ms
2025-05-02 09:57:33.332 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 14.2803ms
2025-05-02 09:57:33.346 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-05-02 09:57:33.350 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:57:33.351 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:57:33.353 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:57:33.376 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:57:33.403 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:57:33.432 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 09:57:33.436 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:57:33.438 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 82.8482ms
2025-05-02 09:57:33.441 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:57:33.443 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 93.6207 ms
2025-05-02 09:57:33.446 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 99.9587ms
2025-05-02 09:57:34.852 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 09:57:34.857 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:57:34.858 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:57:34.859 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:57:34.882 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:57:34.912 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:57:34.998 -04:00 [INF] Executed DbCommand (81ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 09:57:35.001 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:57:35.004 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 142.4851ms
2025-05-02 09:57:35.007 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:57:35.010 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 153.2924 ms
2025-05-02 09:57:35.012 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 160.5046ms
2025-05-02 09:57:52.868 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 09:57:52.872 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:57:52.873 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.4521 ms
2025-05-02 09:57:52.876 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 8.3966ms
2025-05-02 09:57:52.884 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 09:57:52.891 -04:00 [INF] CORS policy execution successful.
2025-05-02 09:57:52.893 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:57:52.895 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 09:57:52.920 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 09:57:52.948 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 09:57:53.034 -04:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 09:57:53.037 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 09:57:53.040 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 142.4091ms
2025-05-02 09:57:53.042 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 09:57:53.045 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 153.8052 ms
2025-05-02 09:57:53.048 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 166.4569ms
2025-05-02 10:03:48.641 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-02 10:03:48.971 -04:00 [INF] Now listening on: http://localhost:5275
2025-05-02 10:03:49.019 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-02 10:03:49.021 -04:00 [INF] Hosting environment: Development
2025-05-02 10:03:49.022 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-05-02 10:03:57.578 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 10:03:57.649 -04:00 [INF] CORS policy execution successful.
2025-05-02 10:03:57.655 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 42.8281 ms
2025-05-02 10:03:57.683 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 112.2044ms
2025-05-02 10:03:57.729 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-05-02 10:03:57.738 -04:00 [INF] CORS policy execution successful.
2025-05-02 10:03:57.865 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 10:03:57.901 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 10:03:59.444 -04:00 [INF] Executed DbCommand (94ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 10:03:59.488 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 10:03:59.877 -04:00 [INF] Executed DbCommand (60ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 10:03:59.913 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 10:03:59.942 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 2033.0624ms
2025-05-02 10:03:59.947 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 10:03:59.951 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 2214.6020 ms
2025-05-02 10:03:59.960 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 2231.6487ms
2025-05-02 10:04:00.658 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 10:04:00.664 -04:00 [INF] CORS policy execution successful.
2025-05-02 10:04:00.668 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 10:04:00.671 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 10:04:00.708 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 10:04:00.733 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 10:04:00.833 -04:00 [INF] Executed DbCommand (83ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 10:04:00.838 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 10:04:00.845 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 170.2706ms
2025-05-02 10:04:00.847 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 10:04:00.861 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 197.9822 ms
2025-05-02 10:04:00.866 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 207.9714ms
2025-05-02 10:07:35.901 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 10:07:35.906 -04:00 [INF] CORS policy execution successful.
2025-05-02 10:07:35.908 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.4015 ms
2025-05-02 10:07:35.910 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 11.968ms
2025-05-02 10:07:35.919 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 10:07:35.923 -04:00 [INF] CORS policy execution successful.
2025-05-02 10:07:35.925 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 10:07:35.927 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 10:07:35.997 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 10:07:36.030 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 10:07:36.128 -04:00 [INF] Executed DbCommand (89ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 10:07:36.135 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 10:07:36.139 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 210.1168ms
2025-05-02 10:07:36.142 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 10:07:36.145 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 222.0339 ms
2025-05-02 10:07:36.147 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 230.0193ms
2025-05-02 11:01:44.648 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 11:01:44.662 -04:00 [INF] CORS policy execution successful.
2025-05-02 11:01:44.666 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 5.5516 ms
2025-05-02 11:01:44.671 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 24.7382ms
2025-05-02 11:01:44.691 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 11:01:44.700 -04:00 [INF] CORS policy execution successful.
2025-05-02 11:01:44.706 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 11:01:44.712 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 11:01:45.007 -04:00 [INF] Executed DbCommand (90ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 11:01:45.038 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 11:01:45.144 -04:00 [INF] Executed DbCommand (91ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 11:01:45.155 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 11:01:45.170 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 453.9247ms
2025-05-02 11:01:45.178 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 11:01:45.183 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 483.4584 ms
2025-05-02 11:01:45.194 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 503.8107ms
2025-05-02 11:02:05.692 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 11:02:05.706 -04:00 [INF] CORS policy execution successful.
2025-05-02 11:02:05.709 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.8232 ms
2025-05-02 11:02:05.714 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 23.3872ms
2025-05-02 11:02:05.722 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-05-02 11:02:05.732 -04:00 [INF] CORS policy execution successful.
2025-05-02 11:02:05.733 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 11:02:05.735 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 11:02:05.763 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 11:02:05.796 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 11:02:05.846 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 11:02:05.850 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 11:02:05.853 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 114.6338ms
2025-05-02 11:02:05.856 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 11:02:05.858 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 126.3602 ms
2025-05-02 11:02:05.864 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 141.4594ms
2025-05-02 11:02:09.191 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 11:02:09.198 -04:00 [INF] CORS policy execution successful.
2025-05-02 11:02:09.200 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 11:02:09.202 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 11:02:09.228 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 11:02:09.255 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 11:02:09.352 -04:00 [INF] Executed DbCommand (90ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 11:02:09.359 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 11:02:09.365 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 160.4435ms
2025-05-02 11:02:09.371 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 11:02:09.374 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 176.1559 ms
2025-05-02 11:02:09.377 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 185.585ms
2025-05-02 14:03:37.287 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-02 14:03:37.616 -04:00 [INF] Now listening on: http://localhost:5275
2025-05-02 14:03:37.720 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-02 14:03:37.723 -04:00 [INF] Hosting environment: Development
2025-05-02 14:03:37.725 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-05-02 14:03:51.079 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:03:51.129 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:03:51.136 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 29.9809 ms
2025-05-02 14:03:51.156 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 83.517ms
2025-05-02 14:03:51.165 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-05-02 14:03:51.171 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:03:52.664 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:03:52.687 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:03:55.053 -04:00 [INF] Executed DbCommand (110ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:03:55.095 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:03:55.430 -04:00 [INF] Executed DbCommand (81ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 14:03:55.453 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:03:55.471 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 2776.1425ms
2025-05-02 14:03:55.475 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:03:55.478 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 4307.5026 ms
2025-05-02 14:03:55.486 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 4320.9605ms
2025-05-02 14:03:57.250 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:03:57.255 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:03:57.257 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.0419 ms
2025-05-02 14:03:57.260 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 10.567ms
2025-05-02 14:03:57.265 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 14:03:57.269 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:03:57.271 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:03:57.273 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:03:57.348 -04:00 [INF] Executed DbCommand (65ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:03:57.431 -04:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:03:57.524 -04:00 [INF] Executed DbCommand (82ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 14:03:57.534 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:03:57.538 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 262.6013ms
2025-05-02 14:03:57.540 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:03:57.542 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 272.9046 ms
2025-05-02 14:03:57.546 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 281.1368ms
2025-05-02 14:06:02.413 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:06:02.417 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:06:02.419 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.4100 ms
2025-05-02 14:06:02.421 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 10.1035ms
2025-05-02 14:06:02.426 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-05-02 14:06:02.440 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:06:02.443 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:06:02.445 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:06:02.525 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:06:02.551 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:06:02.590 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 14:06:02.596 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:06:02.598 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 150.1418ms
2025-05-02 14:06:02.599 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:06:02.601 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 161.2733 ms
2025-05-02 14:06:02.603 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 176.9812ms
2025-05-02 14:06:03.928 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 14:06:03.933 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:06:03.934 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:06:03.936 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:06:03.962 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:06:03.988 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:06:04.095 -04:00 [INF] Executed DbCommand (100ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 14:06:04.099 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:06:04.102 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 161.7254ms
2025-05-02 14:06:04.105 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:06:04.106 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 173.9063 ms
2025-05-02 14:06:04.110 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 181.4197ms
2025-05-02 14:06:07.618 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:06:07.621 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:06:07.622 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.2336 ms
2025-05-02 14:06:07.624 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 6.5644ms
2025-05-02 14:06:07.628 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 14:06:07.632 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:06:07.633 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:06:07.635 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:06:07.685 -04:00 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:06:07.778 -04:00 [INF] Executed DbCommand (89ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:06:07.909 -04:00 [INF] Executed DbCommand (126ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 14:06:07.914 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:06:07.917 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 280.0894ms
2025-05-02 14:06:07.921 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:06:07.924 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 291.8384 ms
2025-05-02 14:06:07.927 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 298.7524ms
2025-05-02 14:21:34.697 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - null null
2025-05-02 14:21:34.716 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:34.719 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 6.6171 ms
2025-05-02 14:21:34.723 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - 204 null null 28.5991ms
2025-05-02 14:21:34.728 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - application/json null
2025-05-02 14:21:34.762 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:34.767 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:34.815 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:21:35.104 -04:00 [INF] Executed DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:21:35.136 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:21:35.413 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-05-02 14:21:35.506 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:21:35.538 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 715.8714ms
2025-05-02 14:21:35.542 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:35.543 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 783.5035 ms
2025-05-02 14:21:35.547 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - 200 null application/json; charset=utf-8 819.196ms
2025-05-02 14:21:35.552 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-05-02 14:21:35.557 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:35.558 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.9547 ms
2025-05-02 14:21:35.562 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 9.2095ms
2025-05-02 14:21:35.567 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 85
2025-05-02 14:21:35.574 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:35.576 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:35.586 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:21:35.613 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:21:35.646 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:21:35.729 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-05-02 14:21:35.761 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:21:35.775 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 184.33ms
2025-05-02 14:21:35.779 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:35.782 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 209.0694 ms
2025-05-02 14:21:35.785 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 218.39ms
2025-05-02 14:21:35.796 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:21:35.801 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:35.804 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.5959 ms
2025-05-02 14:21:35.809 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 12.4848ms
2025-05-02 14:21:35.818 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-05-02 14:21:35.826 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:35.829 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:35.831 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:21:35.858 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:21:35.889 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:21:35.991 -04:00 [INF] Executed DbCommand (87ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 14:21:35.994 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:21:35.997 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 161.4471ms
2025-05-02 14:21:36.001 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:36.003 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 178.0626 ms
2025-05-02 14:21:36.008 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 190.4705ms
2025-05-02 14:21:39.890 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - null null
2025-05-02 14:21:39.895 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:39.896 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.6379 ms
2025-05-02 14:21:39.900 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - 204 null null 9.6465ms
2025-05-02 14:21:39.906 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - application/json null
2025-05-02 14:21:39.910 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 14:21:39.911 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:39.919 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:39.920 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:39.921 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:39.922 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:21:39.923 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:21:39.952 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:21:39.989 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:21:40.022 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-05-02 14:21:40.026 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:21:40.029 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 104.2833ms
2025-05-02 14:21:40.032 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:40.036 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 125.7232 ms
2025-05-02 14:21:40.040 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - 200 null application/json; charset=utf-8 133.9289ms
2025-05-02 14:21:40.057 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-05-02 14:21:40.064 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:40.065 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:40.067 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:21:40.088 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:21:40.101 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:21:40.125 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:21:40.134 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:21:40.158 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 14:21:40.162 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:21:40.164 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 236.5138ms
2025-05-02 14:21:40.165 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-05-02 14:21:40.166 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:40.170 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:21:40.171 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 251.9926 ms
2025-05-02 14:21:40.173 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 100.2406ms
2025-05-02 14:21:40.176 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 266.1394ms
2025-05-02 14:21:40.179 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:40.185 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 121.9217 ms
2025-05-02 14:21:40.188 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 131.456ms
2025-05-02 14:21:46.583 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:21:46.590 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:46.592 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.7800 ms
2025-05-02 14:21:46.597 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 13.7457ms
2025-05-02 14:21:46.602 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 90
2025-05-02 14:21:46.607 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:46.608 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:46.611 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:21:46.640 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:21:46.664 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:21:46.751 -04:00 [INF] Executed DbCommand (80ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 14:21:46.760 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:21:46.766 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 150.471ms
2025-05-02 14:21:46.769 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:46.770 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 163.6008 ms
2025-05-02 14:21:46.774 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 171.6644ms
2025-05-02 14:21:52.524 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - null null
2025-05-02 14:21:52.529 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:52.530 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.5464 ms
2025-05-02 14:21:52.533 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - 204 null null 8.6205ms
2025-05-02 14:21:52.538 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - application/json null
2025-05-02 14:21:52.540 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:21:52.542 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:52.546 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:52.547 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:52.548 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.9693 ms
2025-05-02 14:21:52.549 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:21:52.552 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 12.0262ms
2025-05-02 14:21:52.560 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 14:21:52.574 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:52.577 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:52.579 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:21:52.584 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:21:52.610 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:21:52.626 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:21:52.644 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:21:52.662 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-05-02 14:21:52.682 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:21:52.684 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 14:21:52.685 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 130.8783ms
2025-05-02 14:21:52.694 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:21:52.696 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:52.701 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 116.0081ms
2025-05-02 14:21:52.703 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 161.2806 ms
2025-05-02 14:21:52.707 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:52.711 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - 200 null application/json; charset=utf-8 172.6905ms
2025-05-02 14:21:52.712 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 137.6612 ms
2025-05-02 14:21:52.719 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 159.998ms
2025-05-02 14:21:52.732 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-05-02 14:21:52.735 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:52.737 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.8571 ms
2025-05-02 14:21:52.741 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 9.7958ms
2025-05-02 14:21:52.747 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-05-02 14:21:52.751 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:52.752 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:52.755 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:21:52.787 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:21:52.817 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:21:52.855 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-05-02 14:21:52.863 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:21:52.867 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 108.8662ms
2025-05-02 14:21:52.870 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:52.872 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 120.7296 ms
2025-05-02 14:21:52.875 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 128.0767ms
2025-05-02 14:21:53.096 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-05-02 14:21:53.099 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:53.102 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:53.105 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:21:53.133 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:21:53.161 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:21:53.189 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 14:21:53.192 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:21:53.194 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 87.0328ms
2025-05-02 14:21:53.196 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:53.198 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 98.3328 ms
2025-05-02 14:21:53.201 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 105.6855ms
2025-05-02 14:21:57.303 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 14:21:57.314 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:21:57.316 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:57.317 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:21:57.345 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:21:57.407 -04:00 [INF] Executed DbCommand (56ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:21:57.435 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 14:21:57.440 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:21:57.444 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 122.4437ms
2025-05-02 14:21:57.446 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:21:57.448 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 134.7648 ms
2025-05-02 14:21:57.452 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 148.4123ms
2025-05-02 14:22:27.496 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:22:27.521 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:22:27.524 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 3.0442 ms
2025-05-02 14:22:27.530 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 34.2133ms
2025-05-02 14:22:27.546 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 168
2025-05-02 14:22:27.550 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:22:27.552 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:22:27.554 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:22:27.584 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:22:27.614 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:22:27.646 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 14:22:27.653 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:22:27.657 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 98.1446ms
2025-05-02 14:22:27.659 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:22:27.661 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 111.1352 ms
2025-05-02 14:22:27.666 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 119.8744ms
2025-05-02 14:22:43.820 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - null null
2025-05-02 14:22:43.825 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:22:43.828 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 3.0087 ms
2025-05-02 14:22:43.833 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - 204 null null 12.8979ms
2025-05-02 14:22:43.839 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - application/json null
2025-05-02 14:22:43.844 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:22:43.846 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:22:43.848 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:22:43.877 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:22:43.911 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:22:43.950 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-05-02 14:22:43.960 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:22:43.964 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 112.4585ms
2025-05-02 14:22:43.966 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:22:43.969 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 124.4091 ms
2025-05-02 14:22:43.974 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - 200 null application/json; charset=utf-8 134.8226ms
2025-05-02 14:22:43.978 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-05-02 14:22:43.982 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:22:43.983 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.5670 ms
2025-05-02 14:22:43.986 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 7.9102ms
2025-05-02 14:22:43.993 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 85
2025-05-02 14:22:43.998 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:22:43.999 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:22:44.000 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:22:44.024 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:22:44.055 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:22:44.084 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-05-02 14:22:44.099 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:22:44.102 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 99.0058ms
2025-05-02 14:22:44.105 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:22:44.107 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 109.3231 ms
2025-05-02 14:22:44.109 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 116.4381ms
2025-05-02 14:22:44.115 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:22:44.120 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:22:44.122 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.8463 ms
2025-05-02 14:22:44.126 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 10.4501ms
2025-05-02 14:22:44.131 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-05-02 14:22:44.135 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:22:44.136 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:22:44.140 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:22:44.222 -04:00 [INF] Executed DbCommand (77ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:22:44.258 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:22:44.357 -04:00 [INF] Executed DbCommand (90ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 14:22:44.366 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:22:44.369 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 225.0931ms
2025-05-02 14:22:44.374 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:22:44.377 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 242.1429 ms
2025-05-02 14:22:44.381 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 250.2849ms
2025-05-02 14:22:46.767 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 168
2025-05-02 14:22:46.774 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:22:46.777 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:22:46.780 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:22:46.812 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:22:46.844 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:22:46.878 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 14:22:46.884 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:22:46.888 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 105.6798ms
2025-05-02 14:22:46.895 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:22:46.897 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 123.2833 ms
2025-05-02 14:22:46.902 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 135.0176ms
2025-05-02 14:23:02.920 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:23:02.931 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:23:02.934 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 3.4209 ms
2025-05-02 14:23:02.940 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 20.1255ms
2025-05-02 14:23:02.945 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-05-02 14:23:02.947 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:23:02.948 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:23:02.950 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:23:02.973 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:23:03.001 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:23:03.031 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 14:23:03.041 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:23:03.044 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 91.0684ms
2025-05-02 14:23:03.047 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:23:03.050 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 102.9209 ms
2025-05-02 14:23:03.055 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 110.3482ms
2025-05-02 14:23:06.665 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-05-02 14:23:06.671 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:23:06.673 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:23:06.675 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:23:06.701 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:23:06.758 -04:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:23:06.790 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 14:23:06.796 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:23:06.799 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 120.8762ms
2025-05-02 14:23:06.801 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:23:06.805 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 133.2386 ms
2025-05-02 14:23:06.808 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 142.7128ms
2025-05-02 14:23:11.910 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:23:11.916 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:23:11.919 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.3758 ms
2025-05-02 14:23:11.923 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 12.7137ms
2025-05-02 14:23:11.929 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 14:23:11.935 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:23:11.937 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:23:11.939 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:23:11.988 -04:00 [INF] Executed DbCommand (46ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:23:12.047 -04:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:23:12.077 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 14:23:12.085 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:23:12.089 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 147.0458ms
2025-05-02 14:23:12.091 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:23:12.093 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 157.7943 ms
2025-05-02 14:23:12.098 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 168.9341ms
2025-05-02 14:23:22.046 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:23:22.055 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:23:22.058 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.8198 ms
2025-05-02 14:23:22.064 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 17.7589ms
2025-05-02 14:23:22.071 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-05-02 14:23:22.076 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:23:22.079 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:23:22.081 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:23:22.105 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:23:22.131 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:23:22.164 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 14:23:22.168 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:23:22.170 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 86.3416ms
2025-05-02 14:23:22.173 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:23:22.174 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 97.5320 ms
2025-05-02 14:23:22.176 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 105.3156ms
2025-05-02 14:27:57.863 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:27:57.868 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:27:57.870 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.1772 ms
2025-05-02 14:27:57.873 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 10.0223ms
2025-05-02 14:27:57.879 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-05-02 14:27:57.883 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:27:57.884 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:27:57.886 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:27:57.913 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:27:57.942 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:27:57.973 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 14:27:57.977 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:27:57.978 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 87.3424ms
2025-05-02 14:27:57.979 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:27:57.981 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 98.3563 ms
2025-05-02 14:27:57.983 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 104.1939ms
2025-05-02 14:28:00.529 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-05-02 14:28:00.533 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:28:00.534 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:28:00.536 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:28:00.566 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:28:00.609 -04:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:28:00.635 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-05-02 14:28:00.640 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:28:00.642 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 104.6529ms
2025-05-02 14:28:00.645 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:28:00.646 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 113.4050 ms
2025-05-02 14:28:00.649 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 120.0362ms
2025-05-02 14:29:25.792 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-02 14:29:25.810 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:29:25.812 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.8179 ms
2025-05-02 14:29:25.815 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 24.6361ms
2025-05-02 14:29:25.841 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-05-02 14:29:25.848 -04:00 [INF] CORS policy execution successful.
2025-05-02 14:29:25.849 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:29:25.851 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-02 14:29:25.875 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-02 14:29:25.901 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-02 14:29:25.930 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-02 14:29:25.934 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-02 14:29:25.936 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 82.093ms
2025-05-02 14:29:25.940 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-02 14:29:25.942 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 94.8063 ms
2025-05-02 14:29:25.947 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 105.8096ms
