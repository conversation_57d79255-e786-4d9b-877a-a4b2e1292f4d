﻿namespace Visfuture.OneTeam.BaseBiz.Api.CustomAttributes;

using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
public class CustomAuthorizeAttribute(string roles = "", string permissions = "") : Attribute, IAsyncAuthorizationFilter
{
    private readonly string[] _roles = !string.IsNullOrWhiteSpace(roles)
            ? roles.Split(',').Select(r => r.Trim()).ToArray()
            : [];
    private readonly string[] _permissions = !string.IsNullOrWhiteSpace(permissions)
            ? permissions.Split(',').Select(p => p.Trim()).ToArray()
            : [];

    public string Permissions { get; } = permissions;

    public void OnAuthorization(AuthorizationFilterContext context)
    {
        // Check for AllowAnonymous attribute
        var allowAnonymous = context.ActionDescriptor.EndpointMetadata
            .Any(em => em.GetType() == typeof(AllowAnonymousAttribute));

        if (allowAnonymous)
            return;

        // Check if user is authenticated
        var user = context.HttpContext.User;
        if (!user.Identity?.IsAuthenticated ?? true)
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        // If no roles or permissions specified, just require authentication
        if (_roles.Length == 0 && _permissions.Length == 0)
            return;

        var hasRole = _roles.Length != 0 && _roles.Any(role =>
            user.IsInRole(role) || user.HasClaim(ClaimTypes.Role, role));

        var hasPermission = _permissions.Length != 0 && _permissions.Any(permission =>
            user.HasClaim("Permission", permission));

        // If roles and permissions are specified, user must have at least one of each
        if (_roles.Length != 0 && _permissions.Length != 0)
        {
            if (!hasRole || !hasPermission)
            {
                context.Result = new ForbidResult();
                return;
            }
        }
        // If only roles are specified, user must have at least one role
        else if (_roles.Length != 0 && !hasRole)
        {
            context.Result = new ForbidResult();
            return;
        }
        // If only permissions are specified, user must have at least one permission
        else if (_permissions.Length != 0 && !hasPermission)
        {
            context.Result = new ForbidResult();
            return;
        }
    }

    public Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        throw new NotImplementedException();
    }
}

// Use this one