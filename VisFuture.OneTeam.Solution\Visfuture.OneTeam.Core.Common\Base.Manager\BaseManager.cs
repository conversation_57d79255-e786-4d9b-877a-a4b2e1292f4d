﻿using Microsoft.AspNetCore.Http;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.Core.Common.Base.Manager;

public abstract class BaseManager(IHttpContextAccessor httpContextAccessor)
{
    protected readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

    public static DateTime CurrentDateTime => DateTime.UtcNow;

    public Operator GetCurrentUser()
    {
        HttpContext context = _httpContextAccessor.HttpContext!;
        var claims = context.User.Claims;

        string userIdStr = claims.FirstOrDefault(c => c.Type == "UserId")?.Value ?? throw new UnauthorizedAccessException("Missing UserId claim.");
        string userName = claims.FirstOrDefault(c => c.Type == "UserName")?.Value ?? "Unknown";
        string? tenantIdStr = claims.FirstOrDefault(c => c.Type == "TenantId")?.Value;

        return new Operator
        {
            Id = Guid.Parse(userIdStr),
            EmployeeName = userName,
            TenantId = Guid.TryParse(tenantIdStr, out var parsedTenantId)
            ? parsedTenantId
            : new Guid("4E369EE8-EDF1-41F5-B478-6707ADBB6D9D") // fallback/default tenant
        };
    }
}