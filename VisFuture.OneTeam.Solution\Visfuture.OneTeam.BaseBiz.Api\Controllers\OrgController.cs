using Microsoft.AspNetCore.Mvc;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.Api.Controllers;

public class OrgController(IOrgService orgService) : BaseController<OrgController>
{
    #region Employee

    [HttpPost("EmployeeList")]
    public async Task<IActionResult> GetEmployeeList([FromBody] BaseQuery<EmployeeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<EmployeeListItemDto> result =
            await orgService.QueryEmployeeAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("EmployeeDetails")]
    public async Task<IActionResult> GetEmployeeDetails(Guid employeeId, CancellationToken cancellationToken = default)
    {
        EntityResponse<EmployeeDto> result = await orgService.GetEmployeeByIdAsync(employeeId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddEmployee")]
    public async Task<IActionResult> AddEmployee([FromBody] EmployeeDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.AddEmployeeAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateEmployee")]
    public async Task<IActionResult> UpdateEmployee([FromBody] EmployeeDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.UpdateEmployeeAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteEmployee")]
    public async Task<IActionResult> DeleteEmployee(Guid employeeId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.DeleteEmployeeAsync(employeeId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddEmployeeList")]
    public async Task<IActionResult> AddEmployeeList([FromBody] BaseQuery<EmployeeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.AddEmployeesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateEmployeeList")]
    public async Task<IActionResult> UpdateEmployeeList([FromBody] BaseQuery<EmployeeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.UpdateEmployeesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteEmployeeList")]
    public async Task<IActionResult> DeleteEmployeeList([FromBody] BaseQuery<EmployeeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.DeleteEmployeesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportEmployeeList")]
    public async Task<IActionResult> ExportEmployeeExcel([FromBody] BaseQuery<EmployeeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await orgService.ExportEmployeeExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportEmployeeList")]
    public async Task<IActionResult> ImportEmployeeExcel(B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.ImportEmployeeExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region GlobalAdmin

    [HttpPost("GlobalAdminList")]
    public async Task<IActionResult> GetGlobalAdminList([FromBody] BaseQuery<GlobalAdminDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<GlobalAdminListItemDto> result =
            await orgService.QueryGlobalAdminAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("GlobalAdminDetails")]
    public async Task<IActionResult> GetGlobalAdminDetails(Guid globalAdminId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<GlobalAdminDto> result =
            await orgService.GetGlobalAdminByIdAsync(globalAdminId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddGlobalAdmin")]
    public async Task<IActionResult> AddGlobalAdmin([FromBody] GlobalAdminDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.AddGlobalAdminAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateGlobalAdmin")]
    public async Task<IActionResult> UpdateGlobalAdmin([FromBody] GlobalAdminDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.UpdateGlobalAdminAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteGlobalAdmin")]
    public async Task<IActionResult> DeleteGlobalAdmin(Guid globalAdminId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.DeleteGlobalAdminAsync(globalAdminId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddGlobalAdminList")]
    public async Task<IActionResult> AddGlobalAdminList([FromBody] BaseQuery<GlobalAdminDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.AddGlobalAdminsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateGlobalAdminList")]
    public async Task<IActionResult> UpdateGlobalAdminList([FromBody] BaseQuery<GlobalAdminDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.UpdateGlobalAdminsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteGlobalAdminList")]
    public async Task<IActionResult> DeleteGlobalAdminList([FromBody] BaseQuery<GlobalAdminDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.DeleteGlobalAdminsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportGlobalAdminList")]
    public async Task<IActionResult> ExportGlobalAdminExcel([FromBody] BaseQuery<GlobalAdminDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await orgService.ExportGlobalAdminExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportGlobalAdminList")]
    public async Task<IActionResult> ImportGlobalAdminExcel(B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.ImportGlobalAdminExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region OrganizationEmployee

    [HttpPost("EmployeeListByOrganizationList")]
    public async Task<IActionResult> GetEmployeeListByOrganization([FromBody] BaseQuery<EmployeeDto> request,
    CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<EmployeeListItemDto> result =
            await orgService.QueryEmployeesByOrganizationAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("OrganizationEmployeeList")]
    public async Task<IActionResult> GetOrganizationEmployeeList([FromBody] BaseQuery<OrganizationEmployeeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<OrganizationEmployeeListItemDto> result =
            await orgService.QueryOrganizationEmployeeAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("OrganizationEmployeeDetails")]
    public async Task<IActionResult> GetOrganizationEmployeeDetails(Guid organizationEmployeeId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<OrganizationEmployeeDto> result =
            await orgService.GetOrganizationEmployeeByIdAsync(organizationEmployeeId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddOrganizationEmployee")]
    public async Task<IActionResult> AddOrganizationEmployee([FromBody] OrganizationEmployeeDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.AddOrganizationEmployeeAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateOrganizationEmployee")]
    public async Task<IActionResult> UpdateOrganizationEmployee([FromBody] OrganizationEmployeeDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.UpdateOrganizationEmployeeAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteOrganizationEmployee")]
    public async Task<IActionResult> DeleteOrganizationEmployee(Guid organizationEmployeeId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result =
            await orgService.DeleteOrganizationEmployeeAsync(organizationEmployeeId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddOrganizationEmployeeList")]
    public async Task<IActionResult> AddOrganizationEmployeeList([FromBody] BaseQuery<OrganizationEmployeeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.AddOrganizationEmployeesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateOrganizationEmployeeList")]
    public async Task<IActionResult> UpdateOrganizationEmployeeList(
        [FromBody] BaseQuery<OrganizationEmployeeDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.UpdateOrganizationEmployeesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteOrganizationEmployeeList")]
    public async Task<IActionResult> DeleteOrganizationEmployeeList(
        [FromBody] BaseQuery<OrganizationEmployeeDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.DeleteOrganizationEmployeesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportOrganizationEmployeeList")]
    public async Task<IActionResult> ExportOrganizationEmployeeExcel(
        [FromBody] BaseQuery<OrganizationEmployeeDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await orgService.ExportOrganizationEmployeeExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportOrganizationEmployeeList")]
    public async Task<IActionResult> ImportOrganizationEmployeeExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.ImportOrganizationEmployeeExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region OrganizationHierarchy

    [HttpPost("OrganizationHierarchyList")]
    public async Task<IActionResult> GetOrganizationHierarchyList(
        [FromBody] BaseQuery<OrganizationHierarchyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<OrganizationHierarchyListItemDto> result =
            await orgService.QueryOrganizationHierarchyAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("OrganizationHierarchyDetails")]
    public async Task<IActionResult> GetOrganizationHierarchyDetails(Guid organizationHierarchyId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<OrganizationHierarchyDto> result =
            await orgService.GetOrganizationHierarchyByIdAsync(organizationHierarchyId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddOrganizationHierarchy")]
    public async Task<IActionResult> AddOrganizationHierarchy([FromBody] OrganizationHierarchyDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.AddOrganizationHierarchyAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateOrganizationHierarchy")]
    public async Task<IActionResult> UpdateOrganizationHierarchy([FromBody] OrganizationHierarchyDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.UpdateOrganizationHierarchyAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteOrganizationHierarchy")]
    public async Task<IActionResult> DeleteOrganizationHierarchy(Guid organizationHierarchyId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result =
            await orgService.DeleteOrganizationHierarchyAsync(organizationHierarchyId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddOrganizationHierarchyList")]
    public async Task<IActionResult> AddOrganizationHierarchyList(
        [FromBody] BaseQuery<OrganizationHierarchyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.AddOrganizationHierarchiesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateOrganizationHierarchyList")]
    public async Task<IActionResult> UpdateOrganizationHierarchyList(
        [FromBody] BaseQuery<OrganizationHierarchyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.UpdateOrganizationHierarchiesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteOrganizationHierarchyList")]
    public async Task<IActionResult> DeleteOrganizationHierarchyList(
        [FromBody] BaseQuery<OrganizationHierarchyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.DeleteOrganizationHierarchiesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportOrganizationHierarchyList")]
    public async Task<IActionResult> ExportOrganizationHierarchyExcel(
        [FromBody] BaseQuery<OrganizationHierarchyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await orgService.ExportOrganizationHierarchyExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportOrganizationHierarchyList")]
    public async Task<IActionResult> ImportOrganizationHierarchyExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.ImportOrganizationHierarchyExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region Tenant

    [HttpPost("TenantList")]
    public async Task<IActionResult> GetTenantList([FromBody] BaseQuery<TenantDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<TenantListItemDto> result = await orgService.QueryTenantAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("TenantDetails")]
    public async Task<IActionResult> GetTenantDetails(Guid tenantId, CancellationToken cancellationToken = default)
    {
        EntityResponse<TenantDto> result = await orgService.GetTenantByIdAsync(tenantId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddTenant")]
    public async Task<IActionResult> AddTenant([FromBody] TenantDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.AddTenantAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTenant")]
    public async Task<IActionResult> UpdateTenant([FromBody] TenantDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.UpdateTenantAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTenant")]
    public async Task<IActionResult> DeleteTenant(Guid tenantId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.DeleteTenantAsync(tenantId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddTenantList")]
    public async Task<IActionResult> AddTenantList([FromBody] BaseQuery<TenantDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.AddTenantsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateTenantList")]
    public async Task<IActionResult> UpdateTenantList([FromBody] BaseQuery<TenantDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.UpdateTenantsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteTenantList")]
    public async Task<IActionResult> DeleteTenantList([FromBody] BaseQuery<TenantDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.DeleteTenantsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportTenantList")]
    public async Task<IActionResult> ExportTenantExcel([FromBody] BaseQuery<TenantDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await orgService.ExportTenantExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportTenantList")]
    public async Task<IActionResult> ImportTenantExcel(B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.ImportTenantExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region User Accounts

    [HttpPost("UserAccountList")]
    public async Task<IActionResult> GetUserAccountList([FromBody] BaseQuery<UserAccountDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<UserAccountListItemDto> result =
            await orgService.QueryUserAccountAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("UserAccountDetails")]
    public async Task<IActionResult> GetUserAccountDetails(Guid userAccountId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<UserAccountDto> result =
            await orgService.GetUserAccountByIdAsync(userAccountId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddUserAccount")]
    public async Task<IActionResult> AddUserAccount([FromBody] UserAccountDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.AddUserAccountAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateUserAccount")]
    public async Task<IActionResult> UpdateUserAccount([FromBody] UserAccountDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.UpdateUserAccountAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteUserAccount")]
    public async Task<IActionResult> DeleteUserAccount(Guid userAccountId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await orgService.DeleteUserAccountAsync(userAccountId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddUserAccountList")]
    public async Task<IActionResult> AddUserAccountList([FromBody] BaseQuery<UserAccountDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.AddUserAccountsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateUserAccountList")]
    public async Task<IActionResult> UpdateUserAccountList([FromBody] BaseQuery<UserAccountDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.UpdateUserAccountsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteUserAccountList")]
    public async Task<IActionResult> DeleteUserAccountList([FromBody] BaseQuery<UserAccountDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.DeleteUserAccountsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportUserAccountList")]
    public async Task<IActionResult> ExportUserAccountExcel([FromBody] BaseQuery<UserAccountDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await orgService.ExportUserAccountExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportUserAccountList")]
    public async Task<IActionResult> ImportUserAccountExcel(B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await orgService.ImportUserAccountExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion
}