using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Interfaces;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests;
using Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.InternalService;

public class RoleService(IRoleManager roleManager) : IRoleService
{
    private readonly IRoleManager roleManager = roleManager;

    #region Role

    public Task<EntityResponsePaged<RoleListItemDto>> QueryRoleAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return roleManager.QueryRoleAsync(request, cancellationToken);
    }

    public Task<EntityResponse<RoleDto>> GetRoleByIdAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        return roleManager.GetRoleByIdAsync(roleId, cancellationToken);
    }

    public Task<EntityResponse<Guid>> AddRoleAsync(RoleDto model, CancellationToken cancellationToken = default)
    {
        return roleManager.AddRoleAsync(model, cancellationToken);
    }

    public Task<EntityResponse<Guid>> UpdateRoleAsync(RoleDto model, CancellationToken cancellationToken = default)
    {
        return roleManager.UpdateRoleAsync(model, cancellationToken);
    }

    public Task<EntityResponse<Guid>> DeleteRoleAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        return roleManager.DeleteRoleAsync(roleId, cancellationToken);
    }

    public async Task<EntityResponse> AddRolesAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await roleManager.AddRolesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateRolesAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await roleManager.UpdateRolesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteRolesAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await roleManager.DeleteRolesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportRoleExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.ExportRoleExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportRoleExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await roleManager.ImportRoleExcel(file, cancellationToken);
    }

    #endregion

    #region Role Access

    public Task<EntityResponsePaged<RoleAccessListItemDto>> QueryRoleAccessAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return roleManager.QueryRoleAccessAsync(request, cancellationToken);
    }

    public Task<EntityResponse<RoleAccessDto>> GetRoleAccessByIdAsync(Guid roleAccessId,
        CancellationToken cancellationToken = default)
    {
        return roleManager.GetRoleAccessByIdAsync(roleAccessId, cancellationToken);
    }

    public Task<EntityResponse<Guid>> AddRoleAccessAsync(RoleAccessDto model,
        CancellationToken cancellationToken = default)
    {
        return roleManager.AddRoleAccessAsync(model, cancellationToken);
    }

    public Task<EntityResponse<Guid>> UpdateRoleAccessAsync(RoleAccessDto model,
        CancellationToken cancellationToken = default)
    {
        return roleManager.UpdateRoleAccessAsync(model, cancellationToken);
    }

    public Task<EntityResponse<Guid>> DeleteRoleAccessAsync(Guid roleAccessId,
        CancellationToken cancellationToken = default)
    {
        return roleManager.DeleteRoleAccessAsync(roleAccessId, cancellationToken);
    }

    public async Task<EntityResponse> AddRoleAccessesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.AddRoleAccessesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateRoleAccessesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.UpdateRoleAccessesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteRoleAccessesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.DeleteRoleAccessesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportRoleAccessExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.ExportRoleAccessExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportRoleAccessExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await roleManager.ImportRoleAccessExcel(file, cancellationToken);
    }

    #endregion

    #region Assignable Role

    public Task<EntityResponsePaged<AssignableRoleListItemDto>> QueryAssignableRoleAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return roleManager.QueryAssignableRoleAsync(request, cancellationToken);
    }

    public Task<EntityResponse<AssignableRoleDto>> GetAssignableRoleByIdAsync(Guid roleAccessId,
        CancellationToken cancellationToken = default)
    {
        return roleManager.GetAssignableRoleByIdAsync(roleAccessId, cancellationToken);
    }

    public Task<EntityResponse<Guid>> AddAssignableRoleAsync(AssignableRoleDto model,
        CancellationToken cancellationToken = default)
    {
        return roleManager.AddAssignableRoleAsync(model, cancellationToken);
    }

    public Task<EntityResponse<Guid>> UpdateAssignableRoleAsync(AssignableRoleDto model,
        CancellationToken cancellationToken = default)
    {
        return roleManager.UpdateAssignableRoleAsync(model, cancellationToken);
    }

    public Task<EntityResponse<Guid>> DeleteAssignableRoleAsync(Guid roleAccessId,
        CancellationToken cancellationToken = default)
    {
        return roleManager.DeleteAssignableRoleAsync(roleAccessId, cancellationToken);
    }

    public async Task<EntityResponse> AddAssignableRolesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.AddAssignableRolesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateAssignableRolesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.UpdateAssignableRolesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteAssignableRolesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.DeleteAssignableRolesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportAssignableRoleExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.ExportAssignableRoleExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportAssignableRoleExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.ImportAssignableRoleExcel(file, cancellationToken);
    }

    #endregion

    #region Role Assignment

    public Task<EntityResponsePaged<RoleAssignmentListItemDto>> QueryRoleAssignmentAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return roleManager.QueryRoleAssignmentAsync(request, cancellationToken);
    }

    public Task<EntityResponse<RoleAssignmentDto>> GetRoleAssignmentByIdAsync(Guid roleAssignmentId,
        CancellationToken cancellationToken = default)
    {
        return roleManager.GetRoleAssignmentByIdAsync(roleAssignmentId, cancellationToken);
    }

    public Task<EntityResponse<Guid>> AddRoleAssignmentAsync(RoleAssignmentDto model,
        CancellationToken cancellationToken = default)
    {
        return roleManager.AddRoleAssignmentAsync(model, cancellationToken);
    }

    public Task<EntityResponse<Guid>> UpdateRoleAssignmentAsync(RoleAssignmentDto model,
        CancellationToken cancellationToken = default)
    {
        return roleManager.UpdateRoleAssignmentAsync(model, cancellationToken);
    }

    public Task<EntityResponse<Guid>> DeleteRoleAssignmentAsync(Guid roleAssignmentId,
        CancellationToken cancellationToken = default)
    {
        return roleManager.DeleteRoleAssignmentAsync(roleAssignmentId, cancellationToken);
    }

    public async Task<EntityResponse> AddRoleAssignmentsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.AddRoleAssignmentsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateRoleAssignmentsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.UpdateRoleAssignmentsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteRoleAssignmentsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.DeleteRoleAssignmentsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportRoleAssignmentExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.ExportRoleAssignmentExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportRoleAssignmentExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.ImportRoleAssignmentExcel(file, cancellationToken);
    }

    #endregion

    #region Access Resource

    public Task<EntityResponsePaged<AccessResourceListItemDto>> QueryAccessResourceAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return roleManager.QueryAccessResourceAsync(request, cancellationToken);
    }

    public Task<EntityResponse<AccessResourceDto>> GetAccessResourceByIdAsync(Guid accessResourceId,
        CancellationToken cancellationToken = default)
    {
        return roleManager.GetAccessResourceByIdAsync(accessResourceId, cancellationToken);
    }

    public Task<EntityResponse<Guid>> AddAccessResourceAsync(AccessResourceDto model,
        CancellationToken cancellationToken = default)
    {
        return roleManager.AddAccessResourceAsync(model, cancellationToken);
    }

    public Task<EntityResponse<Guid>> UpdateAccessResourceAsync(AccessResourceDto model,
        CancellationToken cancellationToken = default)
    {
        return roleManager.UpdateAccessResourceAsync(model, cancellationToken);
    }

    public Task<EntityResponse<Guid>> DeleteAccessResourceAsync(Guid accessResourceId,
        CancellationToken cancellationToken = default)
    {
        return roleManager.DeleteAccessResourceAsync(accessResourceId, cancellationToken);
    }

    public async Task<EntityResponse> AddAccessResourcesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.AddAccessResourcesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateAccessResourcesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.UpdateAccessResourcesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteAccessResourcesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.DeleteAccessResourcesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportAccessResourceExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.ExportAccessResourceExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportAccessResourceExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await roleManager.ImportAccessResourceExcel(file, cancellationToken);
    }

    public EntityResponse<List<bool>> MatchAccessResourceAsync(StringListRequest request,
        CancellationToken cancellationToken = default)
    {
        return roleManager.MatchAccessResourceAsync(request, cancellationToken);
    }

    #endregion
}