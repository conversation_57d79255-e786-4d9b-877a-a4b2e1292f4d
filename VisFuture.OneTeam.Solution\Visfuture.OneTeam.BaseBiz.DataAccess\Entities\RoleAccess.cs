﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.DataAccess.Entities;

public partial class RoleAccess : TenantEntity
{
    public Guid RoleId { get; set; }

    public Guid ResourceId { get; set; }

    public string AccessType { get; set; } = null!;

    public virtual AccessResource Resource { get; set; } = null!;

    public virtual Role Role { get; set; } = null!;
}
