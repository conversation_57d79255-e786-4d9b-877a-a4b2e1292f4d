﻿using Microsoft.EntityFrameworkCore;
using Visfuture.OneTeam.Core.Common.Base.DatabaseContext;
using Visfuture.OneTeam.Core.Common.Base.Entities.Project;
using ENTITY = Visfuture.OneTeam.Project.DataAccess.Entities;

namespace Visfuture.OneTeam.Project.DataAccess.DbContext;

public partial class AppDataContext(DbContextOptions<AppDataContext> options) : DatabaseContext<AppDataContext>(options)
{
    public virtual DbSet<MyOrder> MyOrders { get; set; }




    public virtual DbSet<ENTITY.Company> Companies { get; set; }

    public virtual DbSet<ENTITY.CompanyContact> CompanyContacts { get; set; }

    // TODO:
    public virtual DbSet<ENTITY.Project> Projects { get; set; }

    public virtual DbSet<ENTITY.ProjectContract> ProjectContracts { get; set; }

    public virtual DbSet<ENTITY.ProjectRelatedParty> ProjectRelatedParties { get; set; }

    public virtual DbSet<ENTITY.ProjectRoleAssignment> ProjectRoleAssignments { get; set; }

    public virtual DbSet<ENTITY.ProjectTimeLog> ProjectTimeLogs { get; set; }

    public virtual DbSet<ENTITY.ProjectDocument> ProjectDocuments { get; set; }

    public virtual DbSet<ENTITY.ProjectTask> ProjectTasks { get; set; }

    public virtual DbSet<ENTITY.ProjectPaymentPlan> ProjectPaymentPlans { get; set; }

    public virtual DbSet<ENTITY.ProjectPaymentFact> ProjectPaymentFacts { get; set; }


    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        #region Company

        modelBuilder.Entity<ENTITY.Company>(entity =>
        {
            entity.ToTable("Company");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Address1).HasMaxLength(255);
            entity.Property(e => e.Address2).HasMaxLength(255);
            entity.Property(e => e.Alias).HasMaxLength(255);
            entity.Property(e => e.City).HasMaxLength(255);
            entity.Property(e => e.Code).HasMaxLength(255);
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.Currency).HasMaxLength(50);
            entity.Property(e => e.Fax).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Phone).HasMaxLength(50);
            entity.Property(e => e.PostalCode).HasMaxLength(50);
            entity.Property(e => e.Province).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);
            entity.Property(e => e.WebSite).HasMaxLength(255);
        });
        #endregion

        #region Company Contact
        modelBuilder.Entity<ENTITY.CompanyContact>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_CompanyContact");

            entity.HasIndex(e => new { e.TenantId, e.CompanyId }, "Idx_CompanyContact_CompanyId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Address1).HasMaxLength(255);
            entity.Property(e => e.Address2).HasMaxLength(255);
            entity.Property(e => e.City).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.Email).HasMaxLength(255);
            entity.Property(e => e.Fax).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.JobTitle).HasMaxLength(255);
            entity.Property(e => e.Mobile).HasMaxLength(50);
            entity.Property(e => e.Phone).HasMaxLength(50);
            entity.Property(e => e.PostalCode).HasMaxLength(50);
            entity.Property(e => e.Province).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.Company).WithMany(p => p.CompanyContacts)
                .HasForeignKey(d => d.CompanyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanyContacts_Company");
        });
        #endregion

        #region Project

        modelBuilder.Entity<ENTITY.Project>(entity =>
        {
            entity.ToTable("Project");

            entity.HasIndex(e => new { e.TenantId, e.Code }, "Idx_Project_ProjectNo").IsUnique();

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.MainType).HasMaxLength(50);
            entity.Property(e => e.ProjectManager).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Code).HasMaxLength(50);
            entity.Property(e => e.Status).HasMaxLength(50);
            entity.Property(e => e.SubType).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);
        });
        #endregion

        #region Project Contract

        modelBuilder.Entity<ENTITY.ProjectContract>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_ProjectContract");

            entity.HasIndex(e => new { e.TenantId, e.ProjectId }, "Idx_ProjectContract_ProjectId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Amount).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.Currency).HasMaxLength(50);
            entity.Property(e => e.MainType).HasMaxLength(50);
            entity.Property(e => e.SubType).HasMaxLength(50);
            entity.Property(e => e.SumMethod).HasMaxLength(50);
            entity.Property(e => e.TaxAmount).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.TaxRate).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.Project).WithMany(p => p.ProjectContracts)
                .HasForeignKey(d => d.ProjectId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProjectContracts_Project");
        });
        #endregion

        #region Project Related Party

        modelBuilder.Entity<ENTITY.ProjectRelatedParty>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_ProjectRelatedParty");

            entity.HasIndex(e => new { e.TenantId, e.ProjectId }, "Idx_ProjectRelatedParty_ProjectId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ContactAddress1).HasMaxLength(255);
            entity.Property(e => e.ContactAddress2).HasMaxLength(255);
            entity.Property(e => e.ContactCity).HasMaxLength(255);
            entity.Property(e => e.ContactEmail).HasMaxLength(255);
            entity.Property(e => e.ContactFax).HasMaxLength(50);
            entity.Property(e => e.ContactJobTitle).HasMaxLength(255);
            entity.Property(e => e.ContactMobile).HasMaxLength(50);
            entity.Property(e => e.ContactName).HasMaxLength(255);
            entity.Property(e => e.ContactPhone).HasMaxLength(50);
            entity.Property(e => e.ContactPostalCode).HasMaxLength(50);
            entity.Property(e => e.ContactProvince).HasMaxLength(50);
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.Currency).HasMaxLength(50);
            entity.Property(e => e.RelatedType).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.Project).WithMany(p => p.ProjectRelatedParties)
                .HasForeignKey(d => d.ProjectId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProjectRelatedParties_Project");
        });
        #endregion

        #region Project Role Assignment

        modelBuilder.Entity<ENTITY.ProjectRoleAssignment>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_ProjectRoleAssignment");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.EmployeeId).HasMaxLength(50);
            entity.Property(e => e.HourlyCurrency).HasMaxLength(50);
            entity.Property(e => e.HourlyRate).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.RoleCode).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.Project).WithMany(p => p.ProjectRoleAssignments)
                .HasForeignKey(d => d.ProjectId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProjectRoleAssignments_Project");
        });
        #endregion

        #region Project Time Log

        modelBuilder.Entity<ENTITY.ProjectTimeLog>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_ProjectTimeLog");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AdjustTime).HasDefaultValue((short)0);
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.EmployeeId).HasMaxLength(50);
            entity.Property(e => e.Factor)
                .HasDefaultValue(1m)
                .HasColumnType("decimal(18, 3)");
            entity.Property(e => e.TaskType).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.Project).WithMany(p => p.ProjectTimeLogs)
                .HasForeignKey(d => d.ProjectId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProjectTimeLogs_Project");
        });
        #endregion

        #region Project Document

        modelBuilder.Entity<ENTITY.ProjectDocument>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_ProjectDocument");

            entity.HasIndex(e => new { e.TenantId, e.ProjectId }, "Idx_ProjectDocument_ProjectId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Category).HasMaxLength(50);
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.DocumentId).HasMaxLength(50);
            entity.Property(e => e.FileName).HasMaxLength(255);
            entity.Property(e => e.FileType).HasMaxLength(50);
            entity.Property(e => e.ProjectId).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.Project).WithMany(p => p.ProjectDocuments)
                .HasForeignKey(d => d.ProjectId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProjectDocuments_Project");
        });
        #endregion

        #region Project Task

        modelBuilder.Entity<ENTITY.ProjectTask>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_ProjectTask");

            entity.HasIndex(e => new { e.TenantId, e.ProjectId }, "Idx_ProjectTask_ProjectId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.WBS).HasMaxLength(50);
            entity.Property(e => e.StartDate).HasMaxLength(50);
            entity.Property(e => e.EndDate).HasMaxLength(50);            
            entity.Property(e => e.AssigneeId).HasMaxLength(50);
            entity.Property(e => e.Status).HasMaxLength(50);
            entity.Property(e => e.Milestone).HasDefaultValue(false);
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.Project).WithMany(p => p.ProjectTasks)
                .HasForeignKey(d => d.ProjectId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProjectTasks_Project");
        });
        #endregion

        #region Project Payment Plan

        modelBuilder.Entity<ENTITY.ProjectPaymentPlan>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_ProjectPaymentPlan");

            entity.HasIndex(e => new { e.TenantId, e.ProjectId }, "Idx_ProjectPaymentPlan_ProjectId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Category).HasMaxLength(50);
            entity.Property(e => e.PayMethod).HasMaxLength(50);
            entity.Property(e => e.DueDate).HasMaxLength(50);
            entity.Property(e => e.Percentage).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.Currency).HasMaxLength(50);
            entity.Property(e => e.Amount).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.TaxRate).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.TaxAmount).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.Project).WithMany(p => p.ProjectPaymentPlans)
                .HasForeignKey(d => d.ProjectId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProjectPaymentPlans_Project");
        });
        #endregion

        #region Project Payment Fact

        modelBuilder.Entity<ENTITY.ProjectPaymentFact>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_ProjectPaymentFact");

            entity.HasIndex(e => new { e.TenantId, e.ProjectId }, "Idx_ProjectPaymentFact_ProjectId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Category).HasMaxLength(50);
            entity.Property(e => e.PayMethod).HasMaxLength(50);
            entity.Property(e => e.PayDate).HasMaxLength(50);
            entity.Property(e => e.Percentage).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.Currency).HasMaxLength(50);
            entity.Property(e => e.Amount).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.TaxRate).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.TaxAmount).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(18, 3)");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.Project).WithMany(p => p.ProjectPaymentFacts)
                .HasForeignKey(d => d.ProjectId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProjectPaymentFacts_Project");

            entity.HasOne(d => d.PaymentPlan).WithMany(p => p.PaymentFacts)
                 .HasForeignKey(d => d.PayPlanId)
                 .OnDelete(DeleteBehavior.ClientSetNull)
                 .HasConstraintName("FK_ProjectPaymentFacts_ProjectPaymentPlan");
        });
        #endregion

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
