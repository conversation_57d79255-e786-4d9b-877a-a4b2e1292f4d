﻿namespace Visfuture.OneTeam.Core.Common.Helpers;

public class BusResponse<T>
{
    public bool Success { get; set; }
    public T? Data { get; set; }
    public string? Message { get; set; }
    public string? ErrorCode { get; set; }
    public Exception? Exception { get; set; }

    public BusResponse(T data)
    {
        Success = true;
        Data = data;
        Message = "Operation completed successfully.";
        ErrorCode = null;
        Exception = null;
    }

    public BusResponse(string message, string? errorCode = null, Exception? exception = null)
    {
        Success = false;
        Data = default;
        Message = message;
        ErrorCode = errorCode;
        Exception = exception;
    }
}
