using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Visfuture.OneTeam.Core.Common.Base.Controller;
using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Project.BusinessLogic.DTOs;
using Visfuture.OneTeam.Project.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.Project.BusinessLogic.Requests;
using Visfuture.OneTeam.Project.InternalService.Interfaces;

namespace Visfuture.OneTeam.Project.Api.Controllers;

[Authorize]
public class ProjectController(IProjectService projectService) : BaseController<ProjectController>()
{
    private readonly IProjectService projectService = projectService;

    #region Project
    [HttpPost("ProjectList")]
    public async Task<IActionResult> QueryProjectListAsync([FromBody] BaseQuery<ProjectDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<ProjectListItemDto> result = await projectService.QueryProjectAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("ProjectDetail")]
    public async Task<IActionResult> GetProjectAsync(Guid projectId, CancellationToken cancellationToken = default)
    {
        EntityResponse<ProjectDto> result = await projectService.GetProjectAsync(projectId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddProject")]
    public async Task<IActionResult> AddProjectAsync(ProjectDto projectDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.AddProjectAsync(projectDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddProjectList")]
    public async Task<IActionResult> AddProjectListAsync([FromBody] BaseQuery<ProjectDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.AddProjectsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateProject")]
    public async Task<IActionResult> UpdateProjectAsync(ProjectDto projectDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.UpdateProjectAsync(projectDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateProjectList")]
    public async Task<IActionResult> UpdateProjectListAsync([FromBody] BaseQuery<ProjectDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.UpdateProjectsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteProject")]
    public async Task<IActionResult> DeleteProjectAsync(Guid projectId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.DeleteProjectAsync(projectId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteProjectList")]
    public async Task<IActionResult> DeleteProjectListAsync([FromBody] BaseQuery<ProjectDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.DeleteProjectsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportProjectList")]
    public async Task<IActionResult> ExportProjectListAsync([FromBody] BaseQuery<ProjectDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await projectService.ExportProjectExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportProjectList")]
    public async Task<IActionResult> ImportProjectListAsync([FromBody] B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.ImportProjectExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region Contract
    [HttpPost("ContractList")]
    public async Task<IActionResult> QueryContractListAsync([FromBody] BaseQuery<ContractDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<ContractListItemDto> result = await projectService.QueryContractAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("ContractDetail")]
    public async Task<IActionResult> GetContractAsync(Guid contractId, CancellationToken cancellationToken = default)
    {
        EntityResponse<ContractDto> result = await projectService.GetContractAsync(contractId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddContract")]
    public async Task<IActionResult> AddContractAsync(ContractDto contractDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.AddContractAsync(contractDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddContractList")]
    public async Task<IActionResult> AddContractListAsync([FromBody] BaseQuery<ContractDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.AddContractsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateContract")]
    public async Task<IActionResult> UpdateContractAsync(ContractDto contractDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.UpdateContractAsync(contractDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateContractList")]
    public async Task<IActionResult> UpdateContractListAsync([FromBody] BaseQuery<ContractDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.UpdateContractsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteContract")]
    public async Task<IActionResult> DeleteContractAsync(Guid contractId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.DeleteContractAsync(contractId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteContractList")]
    public async Task<IActionResult> DeleteContractListAsync([FromBody] BaseQuery<ContractDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.DeleteContractsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportContractList")]
    public async Task<IActionResult> ExportContractListAsync([FromBody] BaseQuery<ContractDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await projectService.ExportContractExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportContractList")]
    public async Task<IActionResult> ImportContractListAsync([FromBody] B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.ImportContractExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region Related Party
    [HttpPost("RelatedPartyList")]
    public async Task<IActionResult> QueryRelatedPartyListAsync([FromBody] BaseQuery<RelatedPartyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<RelatedPartyListItemDto> result = await projectService.QueryRelatedPartyAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("RelatedPartyDetail")]
    public async Task<IActionResult> GetRelatedPartyAsync(Guid relatedPartyId, CancellationToken cancellationToken = default)
    {
        EntityResponse<RelatedPartyDto> result = await projectService.GetRelatedPartyAsync(relatedPartyId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddRelatedParty")]
    public async Task<IActionResult> AddRelatedPartyAsync(RelatedPartyDto relatedPartyDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.AddRelatedPartyAsync(relatedPartyDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddRelatedPartyList")]
    public async Task<IActionResult> AddRelatedPartyListAsync([FromBody] BaseQuery<RelatedPartyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.AddRelatedPartiesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateRelatedParty")]
    public async Task<IActionResult> UpdateRelatedPartyAsync(RelatedPartyDto relatedPartyDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.UpdateRelatedPartyAsync(relatedPartyDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateRelatedPartyList")]
    public async Task<IActionResult> UpdateRelatedPartyListAsync([FromBody] BaseQuery<RelatedPartyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.UpdateRelatedPartiesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteRelatedParty")]
    public async Task<IActionResult> DeleteRelatedPartyAsync(Guid relatedPartyId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.DeleteRelatedPartyAsync(relatedPartyId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteRelatedPartyList")]
    public async Task<IActionResult> DeleteRelatedPartyListAsync([FromBody] BaseQuery<RelatedPartyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.DeleteRelatedPartiesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportRelatedPartyList")]
    public async Task<IActionResult> ExportRelatedPartyListAsync([FromBody] BaseQuery<RelatedPartyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await projectService.ExportRelatedPartyExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportRelatedPartyList")]
    public async Task<IActionResult> ImportRelatedPartyListAsync([FromBody] B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.ImportRelatedPartyExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region Role Assignment
    [HttpPost("ProjectRoleAssignmentList")]
    public async Task<IActionResult> QueryRoleAssignmentListAsync([FromBody] BaseQuery<ProjectRoleAssignmentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<ProjectRoleAssignmentListItemDto> result = await projectService.QueryRoleAssignmentListAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("ProjectRoleAssignmentDetail")]
    public async Task<IActionResult> GetRoleAssignmentAsync(Guid roleAssignmentId, CancellationToken cancellationToken = default)
    {
        EntityResponse<ProjectRoleAssignmentDto> result = await projectService.GetRoleAssignmentAsync(roleAssignmentId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddProjectRoleAssignment")]
    public async Task<IActionResult> AddRoleAssignmentAsync(ProjectRoleAssignmentDto roleAssignmentDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.AddRoleAssignmentAsync(roleAssignmentDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddProjectRoleAssignmentList")]
    public async Task<IActionResult> AddRoleAssignmentListAsync([FromBody] BaseQuery<ProjectRoleAssignmentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.AddRoleAssignmentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateProjectRoleAssignment")]
    public async Task<IActionResult> UpdateRoleAssignmentAsync(ProjectRoleAssignmentDto roleAssignmentDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.UpdateRoleAssignmentAsync(roleAssignmentDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateProjectRoleAssignmentList")]
    public async Task<IActionResult> UpdateRoleAssignmentListAsync([FromBody] BaseQuery<ProjectRoleAssignmentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.UpdateRoleAssignmentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteProjectRoleAssignment")]
    public async Task<IActionResult> DeleteRoleAssignmentAsync(Guid projectRoleAssignmentId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.DeleteRoleAssignmentAsync(projectRoleAssignmentId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteProjectRoleAssignmentList")]
    public async Task<IActionResult> DeleteRoleAssignmentListAsync([FromBody] BaseQuery<ProjectRoleAssignmentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.DeleteRoleAssignmentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportProjectRoleAssignmentList")]
    public async Task<IActionResult> ExportRoleAssignmentListAsync([FromBody] BaseQuery<ProjectRoleAssignmentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await projectService.ExportProjectRoleAssignmentExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportProjectRoleAssignmentList")]
    public async Task<IActionResult> ImportRoleAssignmentListAsync([FromBody] B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.ImportProjectRoleAssignmentExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region Time Log
    [HttpPost("TimeLogList")]
    public async Task<IActionResult> QueryTimeLogListAsync([FromBody] InvoiceQueueQuery request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<TimeLogListItemDto> result = await projectService.QueryTimeLogAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("TimeLogDetail")]
    public async Task<IActionResult> GetTimeLogAsync(Guid timeLogId, CancellationToken cancellationToken = default)
    {
        EntityResponse<TimeLogDto> result = await projectService.GetTimeLogAsync(timeLogId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddTimeLog")]
    public async Task<IActionResult> AddTimeLogAsync(TimeLogDto timeLogDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.AddTimeLogAsync(timeLogDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddTimeLogList")]
    public async Task<IActionResult> AddTimeLogListAsync([FromBody] BaseQuery<TimeLogDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.AddTimeLogsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTimeLog")]
    public async Task<IActionResult> UpdateTimeLogAsync(TimeLogDto timeLogDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.UpdateTimeLogAsync(timeLogDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateTimeLogList")]
    public async Task<IActionResult> UpdateTimeLogListAsync([FromBody] BaseQuery<TimeLogDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.UpdateTimeLogsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTimeLog")]
    public async Task<IActionResult> DeleteTimeLogAsync(Guid timeLogId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.DeleteTimeLogAsync(timeLogId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteTimeLogList")]
    public async Task<IActionResult> DeleteTimeLogListAsync([FromBody] BaseQuery<TimeLogDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.DeleteTimeLogsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportTimeLogList")]
    public async Task<IActionResult> ExportTimeLogListAsync([FromBody] BaseQuery<TimeLogDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await projectService.ExportTimeLogExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportTimeLogList")]
    public async Task<IActionResult> ImportTimeLogListAsync([FromBody] B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.ImportTimeLogExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region Project Document
    [HttpPost("ProjectDocumentList")]
    public async Task<IActionResult> QueryProjectDocumentListAsync([FromBody] BaseQuery<ProjectDocumentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<ProjectDocumentListItemDto> result = await projectService.QueryProjectDocumentAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("ProjectDocumentDetail")]
    public async Task<IActionResult> GetProjectDocumentAsync(Guid projectDocumentId, CancellationToken cancellationToken = default)
    {
        EntityResponse<ProjectDocumentDto> result = await projectService.GetProjectDocumentAsync(projectDocumentId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddProjectDocument")]
    public async Task<IActionResult> AddProjectDocumentAsync(ProjectDocumentDto projectDocumentDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.AddProjectDocumentAsync(projectDocumentDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddProjectDocumentList")]
    public async Task<IActionResult> AddProjectDocumentListAsync([FromBody] BaseQuery<ProjectDocumentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.AddProjectDocumentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateProjectDocument")]
    public async Task<IActionResult> UpdateProjectDocumentAsync(ProjectDocumentDto projectDocumentDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.UpdateProjectDocumentAsync(projectDocumentDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateProjectDocumentList")]
    public async Task<IActionResult> UpdateProjectDocumentListAsync([FromBody] BaseQuery<ProjectDocumentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.UpdateProjectDocumentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteProjectDocument")]
    public async Task<IActionResult> DeleteProjectDocumentAsync(Guid projectDocumentId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.DeleteProjectDocumentAsync(projectDocumentId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteProjectDocumentList")]
    public async Task<IActionResult> DeleteProjectDocumentListAsync([FromBody] BaseQuery<ProjectDocumentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.DeleteProjectDocumentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportProjectDocumentList")]
    public async Task<IActionResult> ExportProjectDocumentListAsync([FromBody] BaseQuery<ProjectDocumentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await projectService.ExportProjectDocumentExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportProjectDocumentList")]
    public async Task<IActionResult> ImportProjectDocumentListAsync([FromBody] B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.ImportProjectDocumentExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region Task
    [HttpPost("TaskList")]
    public async Task<IActionResult> QueryTaskListAsync([FromBody] BaseQuery<TaskDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<TaskListItemDto> result = await projectService.QueryTaskAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("TaskDetail")]
    public async Task<IActionResult> GetTaskAsync(Guid taskId, CancellationToken cancellationToken = default)
    {
        EntityResponse<TaskDto> result = await projectService.GetTaskAsync(taskId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddTask")]
    public async Task<IActionResult> AddTaskAsync(TaskDto taskDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.AddTaskAsync(taskDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddTaskList")]
    public async Task<IActionResult> AddTaskListAsync([FromBody] BaseQuery<TaskDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.AddTasksAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTask")]
    public async Task<IActionResult> UpdateTaskAsync(TaskDto taskDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.UpdateTaskAsync(taskDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateTaskList")]
    public async Task<IActionResult> UpdateTaskListAsync([FromBody] BaseQuery<TaskDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.UpdateTasksAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTask")]
    public async Task<IActionResult> DeleteTaskAsync(Guid taskId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.DeleteTaskAsync(taskId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteTaskList")]
    public async Task<IActionResult> DeleteTaskListAsync([FromBody] BaseQuery<TaskDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.DeleteTasksAsync(request, cancellationToken);
        return Ok(result);
    }
    #endregion


    #region ProjectPaymentPlan

    [HttpPost("ProjectPaymentPlanList")]
    public async Task<IActionResult> QueryProjectPaymentPlanListAsync([FromBody] BaseQuery<ProjectPaymentPlanDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<ProjectPaymentPlanListItemDto> result = await projectService.QueryProjectPaymentPlanAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("ProjectPaymentPlanDetail")]
    public async Task<IActionResult> GetProjectPaymentPlanAsync(Guid projectPaymentPlanId, CancellationToken cancellationToken = default)
    {
        EntityResponse<ProjectPaymentPlanDto> result = await projectService.GetProjectPaymentPlanAsync(projectPaymentPlanId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddProjectPaymentPlan")]
    public async Task<IActionResult> AddProjectPaymentPlanAsync(ProjectPaymentPlanDto projectPaymentPlanDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.AddProjectPaymentPlanAsync(projectPaymentPlanDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddProjectPaymentPlanList")]
    public async Task<IActionResult> AddProjectPaymentPlanListAsync([FromBody] BaseQuery<ProjectPaymentPlanDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.AddProjectPaymentPlansAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateProjectPaymentPlan")]
    public async Task<IActionResult> UpdateProjectPaymentPlanAsync(ProjectPaymentPlanDto projectPaymentPlanDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.UpdateProjectPaymentPlanAsync(projectPaymentPlanDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateProjectPaymentPlanList")]
    public async Task<IActionResult> UpdateProjectPaymentPlanListAsync([FromBody] BaseQuery<ProjectPaymentPlanDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.UpdateProjectPaymentPlansAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteProjectPaymentPlan")]
    public async Task<IActionResult> DeleteProjectPaymentPlanAsync(Guid projectPaymentPlanId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.DeleteProjectPaymentPlanAsync(projectPaymentPlanId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteProjectPaymentPlanList")]
    public async Task<IActionResult> DeleteProjectPaymentPlanListAsync([FromBody] BaseQuery<ProjectPaymentPlanDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.DeleteProjectPaymentPlansAsync(request, cancellationToken);
        return Ok(result);
    }

    #endregion


    #region ProjectPaymentFact

    [HttpPost("ProjectPaymentFactList")]
    public async Task<IActionResult> QueryProjectPaymentFactListAsync([FromBody] BaseQuery<ProjectPaymentFactDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<ProjectPaymentFactListItemDto> result = await projectService.QueryProjectPaymentFactAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("ProjectPaymentFactDetail")]
    public async Task<IActionResult> GetProjectPaymentFactAsync(Guid projectPaymentFactId, CancellationToken cancellationToken = default)
    {
        EntityResponse<ProjectPaymentFactDto> result = await projectService.GetProjectPaymentFactAsync(projectPaymentFactId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddProjectPaymentFact")]
    public async Task<IActionResult> AddProjectPaymentFactAsync(ProjectPaymentFactDto projectPaymentFactDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.AddProjectPaymentFactAsync(projectPaymentFactDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddProjectPaymentFactList")]
    public async Task<IActionResult> AddProjectPaymentFactListAsync([FromBody] BaseQuery<ProjectPaymentFactDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.AddProjectPaymentFactsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateProjectPaymentFact")]
    public async Task<IActionResult> UpdateProjectPaymentFactAsync(ProjectPaymentFactDto projectPaymentFactDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.UpdateProjectPaymentFactAsync(projectPaymentFactDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateProjectPaymentFactList")]
    public async Task<IActionResult> UpdateProjectPaymentFactListAsync([FromBody] BaseQuery<ProjectPaymentFactDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.UpdateProjectPaymentFactsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteProjectPaymentFact")]
    public async Task<IActionResult> DeleteProjectPaymentFactAsync(Guid projectPaymentFactId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await projectService.DeleteProjectPaymentFactAsync(projectPaymentFactId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteProjectPaymentFactList")]
    public async Task<IActionResult> DeleteProjectPaymentFactListAsync([FromBody] BaseQuery<ProjectPaymentFactDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await projectService.DeleteProjectPaymentFactsAsync(request, cancellationToken);
        return Ok(result);
    }

    #endregion

}
