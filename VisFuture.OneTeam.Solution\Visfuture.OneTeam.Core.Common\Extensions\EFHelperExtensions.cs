﻿//using AutoMapper.QueryableExtensions;
//using AutoMapper;
using System.Data;
using System.Linq.Expressions;
using Mapster;
using Microsoft.EntityFrameworkCore;


namespace Visfuture.OneTeam.Core.Common.Extensions;

public static class EFHelperExtensions
{
    //--- Auto Mapper
    //public static IMapper Mapper { get; set; } = default!;

    //public static T? Map<F, T>(this F? source) where F : class where T : class
    //{
    //    if (source == null) return null;
    //    return Mapper.Map<F?, T?>(source);
    //}

    //public static IQueryable<T> Map<T>(this IQueryable source) where T : class
    //{
    //    return source.ProjectTo<T>(Mapper.ConfigurationProvider);
    //}

    //public static T? FirstOrDefault<F, T>(this IQueryable<F> source, Expression<Func<F, bool>>? filter = null) where F : class where T : class
    //{
    //    if (filter != null)
    //    {
    //        source = source.Where(filter);
    //    }
    //    return Mapper.Map<F?, T?>(source.FirstOrDefault());
    //}


    //public static async Task<T?> FirstOrDefaultAsync<F, T>(this IQueryable<F> source, CancellationToken cancellationToken = default) where F : class where T : class
    //{
    //    F? result = await source.FirstOrDefaultAsync(cancellationToken);
    //    return Mapper.Map<F?, T?>(result);
    //}

    //public static async Task<T?> FirstOrDefaultAsync<F, T>(this IQueryable<F> source, Expression<Func<F, bool>>? filter = null, CancellationToken cancellationToken = default) where F : class where T : class
    //{
    //    if (filter != null)
    //    {
    //        source = source.Where(filter);
    //    }
    //    F? result = await source.FirstOrDefaultAsync(cancellationToken);
    //    return Mapper.Map<F?, T?>(result);
    //}

    //public static IQueryable<T> AsNoTracking<F, T>(this IQueryable<F> source, Expression<Func<F, bool>>? filter = null, Func<IQueryable<F>, IOrderedQueryable<F>>? orderBy = null) where F : class where T : class
    //{
    //    if (filter != null)
    //    {
    //        source = source.Where(filter);
    //    }
    //    if (orderBy != null)
    //    {
    //        source = orderBy(source);
    //    }
    //    return source.AsNoTracking().ProjectTo<T>(Mapper.ConfigurationProvider);
    //}

    //public static async Task<List<T>> ToListAsync<F, T>(this IQueryable<F> source, CancellationToken cancellationToken = default) where F : class where T : class
    //{
    //    var data = source.AsNoTracking().ProjectTo<T>(Mapper.ConfigurationProvider);
    //    return await data.ToListAsync(cancellationToken);
    //}

    //--- Mapster
    public static T? Map<F, T>(this F? source) where F : class where T : class
    {
        if (source == null) return null;
        return source.Adapt<T>();
    }

    public static IQueryable<T> Map<T>(this IQueryable source) where T : class
    {
        return source.ProjectToType<T>();
    }

    public static T? FirstOrDefault<F, T>(this IQueryable<F> source, Expression<Func<F, bool>>? filter = null) where F : class where T : class
    {
        if (filter != null)
        {
            source = source.Where(filter);
        }
        return source.FirstOrDefault()?.Adapt<T>();
    }

    public static async Task<T?> FirstOrDefaultAsync<F, T>(this IQueryable<F> source, CancellationToken cancellationToken = default) where F : class where T : class
    {
        F? result = await source.FirstOrDefaultAsync(cancellationToken);
        return result?.Adapt<T>();
    }

    public static async Task<T?> FirstOrDefaultAsync<F, T>(this IQueryable<F> source, Expression<Func<F, bool>>? filter = null, CancellationToken cancellationToken = default) where F : class where T : class
    {
        if (filter != null)
        {
            source = source.Where(filter);
        }
        F? result = await source.FirstOrDefaultAsync(cancellationToken);
        return result?.Adapt<T>();
    }

    public static IQueryable<T> AsNoTracking<F, T>(this IQueryable<F> source, Expression<Func<F, bool>>? filter = null, Func<IQueryable<F>, IOrderedQueryable<F>>? orderBy = null) where F : class where T : class
    {
        if (filter != null)
        {
            source = source.Where(filter);
        }
        if (orderBy != null)
        {
            source = orderBy(source);
        }
        return source.AsNoTracking().ProjectToType<T>();
    }

    public static async Task<List<T>> ToListAsync<F, T>(this IQueryable<F> source, CancellationToken cancellationToken = default) where F : class where T : class
    {
        var data = source.AsNoTracking().ProjectToType<T>();
        return await data.ToListAsync(cancellationToken);
    }
}
