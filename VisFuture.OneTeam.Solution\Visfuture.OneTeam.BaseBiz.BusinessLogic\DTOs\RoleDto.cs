﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;

public class RoleDto : CodeDto
{
    public string Name { get; set; } = string.Empty;

    public string? Description { get; set; }

    public bool IsActive { get; set; }

    public string Usage { get; set; } = null!;

    public virtual ICollection<AssignableRoleDto> AssignableRoles { get; set; } = [];

    public virtual ICollection<RoleAccessDto> RoleAccesses { get; set; } = [];
}

