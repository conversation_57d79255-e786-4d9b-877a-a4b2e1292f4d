﻿namespace Visfuture.OneTeam.Core.Common.Base.Models
{
    public class EmailMessageDto
    {
        public int EmailKey { get; set; }
        public string? EmailId { get; set; }
        public string? ConversationId { get; set; }
        public byte[]? ConversationIndex { get; set; }
        public string? Subject { get; set; }
        public string? From { get; set; }
        public string? FromName { get; set; }
        public string? To { get; set; }
        public string? Cc { get; set; }
        public string? Bcc { get; set; }
        public DateTime? ReceivedDateTime { get; set; }
        public string? Importance { get; set; }
        public string? ContentType { get; set; }
        public string? Body { get; set; }
        public string? BodyTextContent { get; set; }
        public DateTime? CreatedOn { get; set; }

        public List<EmailAttachmentDto> EmailAttachments { get; set; } = new List<EmailAttachmentDto>();
    }
}
