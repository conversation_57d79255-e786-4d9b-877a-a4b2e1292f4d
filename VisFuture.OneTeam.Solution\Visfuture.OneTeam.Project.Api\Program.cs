using Visfuture.OneTeam.Project.Api.Extensions;

var builder = WebApplication.CreateBuilder(args);

//******************************************
// Add services to the container.
//******************************************
builder.Services.ConfigureRedisCache(builder.Configuration);
builder.Services.ConfigureDbContext(builder.Configuration);
builder.Services.ConfigureJWT(builder.Configuration);
builder.Services.ConfigureExceptionHandler();
builder.Services.ConfgiureHttpClient();
builder.Services.ConfigureCustomServices();

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
//builder.Services.AddSwaggerGen();

builder.Services.AddMapsterServices();
builder.Services.AddSwaggerExtension();
builder.Services.AddCors(options =>
    {
        options.AddPolicy("CorsPolicy", builder =>
        {
            builder
                //.WithOrigins("http://localhost:5173", "http://localhost:5173", "http://localhost:7250", "https://localhost:7250")
                .AllowAnyOrigin()
                .AllowAnyMethod()
                .AllowAnyHeader();
            //.AllowCredentials();
        });
    });


//******************************************
// Tells the application to use the services
//******************************************
var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}
app.UseCors("CorsPolicy");

//app.UseHttpsRedirection();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();
