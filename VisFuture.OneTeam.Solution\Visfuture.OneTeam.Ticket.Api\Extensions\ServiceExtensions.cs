﻿using System.Text;
using Mapster;
using MapsterMapper;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Visfuture.OneTeam.Ticket.Api.Exceptions;
using Visfuture.OneTeam.Ticket.BusinessLogic;
using Visfuture.OneTeam.Ticket.BusinessLogic.Interfaces;
using Visfuture.OneTeam.Ticket.BusinessLogic.Mapper;
using Visfuture.OneTeam.Ticket.DataAccess.DbContext;
using Visfuture.OneTeam.Ticket.InternalService;
using Visfuture.OneTeam.Ticket.InternalService.interfaces;
using Visfuture.OneTeam.Ticket.InternalService.Interfaces;
using Visfuture.OneTeam.Ticket.JobService;

namespace Visfuture.OneTeam.Ticket.Api.Extensions;

public static class ServiceExtensions
{
    public static void ConfigureDbContext(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<AppDataContext>(options =>
        {
            options.UseSqlServer(configuration.GetConnectionString("sqlConnection"));
        });
    }

    public static void ConfigureExceptionHandler(this IServiceCollection services)
    {
        services
            .AddProblemDetails()
            .AddExceptionHandler<GlobalExceptionHandler>(); // you can add multiple exception handlers here
    }

    //public static void ConfigureRedisCache(this IServiceCollection services, IConfiguration configuration)
    //{
    //    services.AddStackExchangeRedisCache(options =>
    //    {
    //        string? connection = configuration.GetConnectionString("ConnectionStrings:Redis");

    //        if (connection == null) throw new ArgumentNullException(nameof(connection));

    //        options.Configuration = connection;
    //    });
    //}

    public static void ConfigureCustomServices(this IServiceCollection services)
    {
        // E.g.
        //services.AddScoped<IUserService, UserService>();    // allows DI by interface if declared this way
        //services.AddScoped<IWeatherService, WeatherService>();

        // Add additional services here...
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddHttpClient();

        services.AddScoped<ITicketManager, TicketManager>();
        services.AddScoped<IInvoiceManager, InvoiceManager>();
        services.AddScoped<IProductManager, ProductManager>();

        services.AddScoped<ITicketService, TicketServiceImpl>();
        services.AddScoped<IInvoiceService, InvoiceServiceImpl>();
        services.AddScoped<IProductService, ProductServiceImpl>();
        services.AddScoped<IMapper, ServiceMapper>();
    }

    public static void ConfigureJWT(this IServiceCollection services, IConfiguration configuration)
    {
        //var jwtSettings = configuration.GetSection("JwtSettings");
        //var secretKey = Environment.GetEnvironmentVariable("SECRET") ?? "!@#$%^&*()abxgretretyehdghvbnsfserbdfgdf@#$^%$#^%$^@#$@#ewrwer";

        services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = false;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey =
                        true, // can be eliminated if you don't want to validate the issuer signing key
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero,

                    ValidIssuer = configuration["JWT:Issuer"] ?? "",
                    ValidAudience = configuration["JWT:Audience"] ?? "",
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["JWT:Key"] ??
                        "!@#$%^&*()abxgretretyehdghvbnsfserbdfgdf@#$^%$#^%$^@#$@#ewrwer"))
                };
            });
    }

    public static void AddSwaggerExtension(this IServiceCollection services)
    {
        services.AddSwaggerGen(opt =>
        {
            OpenApiSecurityScheme scheme = new()
            {
                Description = "Authorization header.\r\nExample:'Bearer 123456abcdef'",
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Authorization"
                },
                Scheme = "oauth2",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey
            };
            opt.AddSecurityDefinition("Authorization", scheme);
            OpenApiSecurityRequirement requirment = new()
            {
                [scheme] = []
            };
            opt.AddSecurityRequirement(requirment);
        });
    }

    public static void AddMapsterServices(this IServiceCollection services)
    {
        // Register Mapster configuration and mapper
        TypeAdapterConfig config = TypeAdapterConfig.GlobalSettings;
        // Scan for Mapster configurations in this assembly
        config.Scan(typeof(Program).Assembly);
        MapsterConfig.RegisterMappings();

        services.AddSingleton(config);
    }
}