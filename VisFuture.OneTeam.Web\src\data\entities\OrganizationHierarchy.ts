import { Info } from '../FieldTypes'
import { TenantEntityInfo } from './TenantEntity'
import { AssignableRole } from './AssignableRole'
import { OrganizationEmployee } from './OrganizationEmployee'
import { RoleAssignment } from './RoleAssignment'
import { CodeEntity } from './CodeEntity'

export interface OrganizationHierarchy extends CodeEntity {
  description?: string
  name: string
  isActive: boolean
  mainType: OrgMainType
  subType: OrgSubType
  superiorId?: string
  defaultLocation?: string
  defaultWorkSchedule?: string
  assignableRoles?: AssignableRole[]
  roleAssignments?: RoleAssignment[]
  organizationEmployees?: OrganizationEmployee[]
}

export const OrgMainTypes = ['Org'] as const
export type OrgMainType = (typeof OrgMainTypes)[number]

export const OrgSubTypes = ['Company', 'Department', 'Team'] as const
export type OrgSubType = (typeof OrgSubTypes)[number]

export const organizationHierarchyInfo: Info<OrganizationHierarchy> = {
  typeName: 'Organization Hierarchy',
  nameKey: 'name',
  sortKey: 'code',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'OrganizationHierarchy',
  fields: {
    code: { label: 'Code', type: 'smalltext', disabled: true },
    description: { label: 'Description', type: 'textarea' },
    name: { label: 'Name', type: 'bigtext', required: true },
    mainType: { label: 'Main Type', type: 'select', required: true },
    subType: { label: 'Sub Type', type: 'select', required: true },
    superiorId: { label: 'Superior', type: 'external', required: false },
    defaultLocation: { label: 'Default Location', type: 'place' },
    defaultWorkSchedule: { label: 'Default Work Schedule', type: 'bigtext' },
    isActive: { label: 'Active', type: 'bool', required: true },
    assignableRoles: { label: 'Assignable Roles', type: 'external' },
    roleAssignments: { label: 'Role Assignments', type: 'external' },
    organizationEmployees: { label: 'Employees', type: 'external' },
    ...TenantEntityInfo.fields,
  },
  options: {
    superiorId: {
      entity: 'OrganizationHierarchy',
      routePrefix: '/tenant-admin/organizational-hierarchy',
      labelField: 'name',
    },
    mainType: { options: OrgMainTypes.slice() },
    subType: { options: OrgSubTypes.slice() },
    ...TenantEntityInfo.options,
  },
  default: {
    code: 'Auto-generated',
    name: '',
    isActive: true,
    mainType: 'Org',
    subType: 'Department',
  },
  columnsShown: new Set(['name', 'code', 'mainType', 'subType', 'superiorId', 'isActive']),
  formLayout: [
    ['name', 'code'],
    ['mainType', 'subType'],
    ['superiorId', 'isActive'],
    // ['defaultLocation', 'defaultWorkSchedule'],
  ],
  tabList: {
    OrganizationEmployee: { tabName: 'Employees', relation: { id: 'organizationId' } },
    OrganizationHierarchy: { tabName: 'Subordinates', relation: { id: 'superiorId' } },
    AssignableRole: { tabName: 'Assignable Roles', relation: { id: 'organizationId' } },
    RoleAssignment: { tabName: 'Role Assignments', relation: { id: 'organizationId' } },
  },
}
