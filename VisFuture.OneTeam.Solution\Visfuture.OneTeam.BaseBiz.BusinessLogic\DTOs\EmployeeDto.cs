﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;

public class EmployeeDto : TenantBaseDto
{
    public string Code { get; set; } = null!;

    public string Name { get; set; } = null!;

    public string MainType { get; set; } = null!;

    public string? SubType { get; set; }

    public string? Description { get; set; }

    public bool IsActive { get; set; }

    public string? PositionId { get; set; }

    public string? JobTitle { get; set; }

    public string BusinessEmail { get; set; } = null!;

    public string? WorkScheduleId { get; set; }

    public DateTime HireDate { get; set; }

    public DateTime? TerminateDate { get; set; }

    public string EmployeeStatus { get; set; } = null!;

    public Guid? UserAccountId { get; set; }

    public bool IsTenantAdmin { get; set; }

    public virtual ICollection<OrganizationEmployeeDto> OrganizationEmployees { get; set; } = [];

    public virtual ICollection<RoleAssignmentDto> RoleAssignments { get; set; } = [];
}