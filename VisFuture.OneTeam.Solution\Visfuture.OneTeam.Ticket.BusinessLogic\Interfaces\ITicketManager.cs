using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Ticket.BusinessLogic.DTOs;
using Visfuture.OneTeam.Ticket.BusinessLogic.ListItemDtos;

namespace Visfuture.OneTeam.Ticket.BusinessLogic.Interfaces;

public interface ITicketManager
{
    #region Ticket
    Task<EntityResponse<TicketDto>> GetTicketByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<TicketDto>> GetTicketByNoAsync(string ticketNo, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteTicketAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTicketAsync(TicketDto ticketDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTicketFromEmailScannerAsync(EmailMessageDto emailMessage, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateTicketAsync(TicketDto ticketDto, CancellationToken cancellationToken = default);
    Task<EntityResponsePaged<TicketListItemDto>> QueryTicketAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddTicketsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<B64File>> ExportTicketExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketReview
    Task<EntityResponse<TicketReviewDto>> GetTicketReviewByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteTicketReviewAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTicketReviewAsync(TicketReviewDto ticketReviewDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateTicketReviewAsync(TicketReviewDto ticketReviewDto, CancellationToken cancellationToken = default);
    Task<EntityResponsePaged<TicketReviewListItemDto>> QueryTicketReviewAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddTicketReviewsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketReviewsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketReviewsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<B64File>> ExportTicketReviewExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketReviewExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketLink
    Task<EntityResponse<TicketLinkDto>> GetTicketLinkByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteTicketLinkAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTicketLinkAsync(TicketLinkDto ticketLinkDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateTicketLinkAsync(TicketLinkDto ticketLinkDto, CancellationToken cancellationToken = default);
    Task<EntityResponsePaged<TicketLinkListItemDto>> QueryTicketLinkAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddTicketLinksAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketLinksAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketLinksAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<B64File>> ExportTicketLinkExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketLinkExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketDevOpsLink
    Task<EntityResponse<TicketDevOpsLinkDto>> GetTicketDevOpsLinkByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteTicketDevOpsLinkAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTicketDevOpsLinkAsync(TicketDevOpsLinkDto ticketDevOpsLinkDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateTicketDevOpsLinkAsync(TicketDevOpsLinkDto ticketDevOpsLinkDto, CancellationToken cancellationToken = default);
    Task<EntityResponsePaged<TicketDevOpsLinkListItemDto>> QueryTicketDevOpsLinkAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddTicketDevOpsLinksAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketDevOpsLinksAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketDevOpsLinksAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<B64File>> ExportTicketDevOpsLinkExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketDevOpsLinkExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketBillingPayment
    Task<EntityResponse<TicketBillingPaymentDto>> GetTicketBillingPaymentByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteTicketBillingPaymentAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTicketBillingPaymentAsync(TicketBillingPaymentDto ticketBillingPaymentDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateTicketBillingPaymentAsync(TicketBillingPaymentDto ticketBillingPaymentDto, CancellationToken cancellationToken = default);
    Task<EntityResponsePaged<TicketBillingPaymentListItemDto>> QueryTicketBillingPaymentAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddTicketBillingPaymentsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketBillingPaymentsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketBillingPaymentsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<B64File>> ExportTicketBillingPaymentExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketBillingPaymentExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketBillingDelivery
    Task<EntityResponse<TicketBillingDeliveryDto>> GetTicketBillingDeliveryByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteTicketBillingDeliveryAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTicketBillingDeliveryAsync(TicketBillingDeliveryDto ticketBillingDeliveryDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateTicketBillingDeliveryAsync(TicketBillingDeliveryDto ticketBillingDeliveryDto, CancellationToken cancellationToken = default);
    Task<EntityResponsePaged<TicketBillingDeliveryListItemDto>> QueryTicketBillingDeliveryAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddTicketBillingDeliveriesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketBillingDeliveriesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketBillingDeliveriesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<B64File>> ExportTicketBillingDeliveryExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketBillingDeliveryExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketBilling
    Task<EntityResponse<TicketBillingDto>> GetTicketBillingByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteTicketBillingAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTicketBillingAsync(TicketBillingDto ticketBillingDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateTicketBillingAsync(TicketBillingDto ticketBillingDto, CancellationToken cancellationToken = default);
    Task<EntityResponsePaged<TicketBillingListItemDto>> QueryTicketBillingAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddTicketBillingsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketBillingsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketBillingsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<B64File>> ExportTicketBillingExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketBillingExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region Ticket Discussion
    Task<EntityResponse<TicketDiscussionDto>> GetTicketDiscussionByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteTicketDiscussionAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTicketDiscussionAsync(TicketDiscussionDto ticketDiscussionDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateTicketDiscussionAsync(TicketDiscussionDto ticketDiscussionDto, CancellationToken cancellationToken = default);
    Task<EntityResponsePaged<TicketDiscussionListItemDto>> QueryTicketDiscussionAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddTicketDiscussionsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketDiscussionsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketDiscussionsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<B64File>> ExportTicketDiscussionExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketDiscussionExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketDocument
    Task<EntityResponse<TicketDocumentDto>> GetTicketDocumentByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteTicketDocumentAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTicketDocumentAsync(TicketDocumentDto ticketDocumentDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateTicketDocumentAsync(TicketDocumentDto ticketDocumentDto, CancellationToken cancellationToken = default);
    Task<EntityResponsePaged<TicketDocumentListItemDto>> QueryTicketDocumentAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddTicketDocumentsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketDocumentsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketDocumentsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<B64File>> ExportTicketDocumentExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketDocumentExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion
}
