﻿using System.ComponentModel;
using System.Reflection;

namespace Visfuture.OneTeam.Core.Common;
#region Enumerations
public static class Enumerations
{
    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="value"></param>
    /// <returns></returns>
    public static string? GetName<T>(T value)
    {
        return Enum.GetName(typeof(T), value!);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="value"></param>
    /// <returns></returns>
    public static int GetValue<T>(T value)
    {
        if (value != null)
        {
            return (int)Enum.Parse(typeof(T), value.ToString() ?? "");
        }
        else
        {
            return 0;
        }
    }

    public static string GetDescription<T>(T value)
    {
        if (value == null) { return string.Empty; }
        else
        {
            FieldInfo? fi = value.GetType().GetField(value.ToString() ?? "");
            if (fi != null)
            {
                DescriptionAttribute[] attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);
                if (attributes != null && attributes.Length > 0)
                    return attributes[0].Description;
            }
            return value.ToString() ?? "";
        }
    }

    public static T StringToEnum<T>(string value)
    {
        if (!string.IsNullOrEmpty(value) && System.Enum.GetNames(typeof(T)).Contains(value))
        {
            return (T)Enum.Parse(typeof(T), value);
        }
        else
        {
            return default!;
        }
    }
}

#endregion


public enum SortOperator
{
    /// <summary>
    /// Ascending
    /// </summary>
    [Description("Ascending")]
    Asc = 0,
    /// <summary>
    /// Descending
    /// </summary>
    [Description("Descending")]
    Desc = 1
}


public enum LanguageType
{
    [Description("Default")]
    NO = 0,
    [Description("English")]
    EN = 1,
    [Description("Français")]
    FR = 2
}

public enum DocumentType
{
    [Description("AppWeb")]
    AppWeb = 1,

    [Description("Message")]
    Message = 2,

    //[Description("Attachment")]
    //Attachment = 3
}


public enum RoleType
{
    [Description("Null")]
    None = 0,
    [Description("User")]
    User = 1,
    [Description("Internal User")]
    InternalUser = 2,
}

public enum TokenDataType
{
    [Description("Authorization")]
    Authorization = 1,
    [Description("Verification Code")]
    VerificationCode = 2,
    [Description("Reset Password Code")]
    ResetPasswordCode = 3
}

public enum ApiAuthorizeType
{
    [Description("Non")]
    Non = 0
}

public enum EmailTemplateType
{
    [Description("Activate Account")]
    ActivateAccount = 0,
    [Description("Reset Password")]
    ResetPassword = 1,
    [Description("Verification Code")]
    VerificationCode = 2,
}

public enum MessageHubType
{
    Notice = 0,
    Message = 1,
}

public enum MessageTypeCode
{
    Non = 0,
    Welcome = 100,
    Online = 101,
    Offline = 102,
    OrderMsg = 1001,
}