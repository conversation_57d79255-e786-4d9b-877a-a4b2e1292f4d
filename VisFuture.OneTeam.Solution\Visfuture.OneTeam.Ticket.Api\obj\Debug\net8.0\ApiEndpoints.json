[{"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "AddInvoiceAsync", "RelativePath": "AddInvoice", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "invoiceDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "AddInvoiceItemAsync", "RelativePath": "AddInvoiceItem", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "invoiceItemDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceItemDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "AddInvoiceItemsAsync", "RelativePath": "AddInvoiceItemList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceItemDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "AddInvoicesAsync", "RelativePath": "AddInvoiceList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "AddProductAsync", "RelativePath": "AddProduct", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "productDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "AddProductDeploymentTrackingAsync", "RelativePath": "AddProductDeploymentTracking", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "productDeploymentTrackingDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductDeploymentTrackingDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "AddProductDeploymentTrackingListAsync", "RelativePath": "AddProductDeploymentTrackingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductDeploymentTrackingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "AddProductLicenseTrackingAsync", "RelativePath": "AddProductLicenseTracking", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "productLicenseTrackingDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductLicenseTrackingDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "AddProductLicenseTrackingListAsync", "RelativePath": "AddProductLicenseTrackingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductLicenseTrackingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "AddProductsAsync", "RelativePath": "AddProductList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "AddProductReleaseTrackingAsync", "RelativePath": "AddProductReleaseTracking", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "productReleaseTrackingDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductReleaseTrackingDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "AddProductReleaseTrackingListAsync", "RelativePath": "AddProductReleaseTrackingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductReleaseTrackingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketAsync", "RelativePath": "AddTicket", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketBillingAsync", "RelativePath": "AddTicketBilling", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketBillingDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketBillingDeliveryAsync", "RelativePath": "AddTicketBillingDelivery", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketBillingDeliveryDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDeliveryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketBillingDeliveriesAsync", "RelativePath": "AddTicketBillingDeliveryList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDeliveryDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketBillingsAsync", "RelativePath": "AddTicketBillingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketBillingPaymentAsync", "RelativePath": "AddTicketBillingPayment", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketBillingPaymentDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingPaymentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketBillingPaymentsAsync", "RelativePath": "AddTicketBillingPaymentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingPaymentDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketDevOpsLinkAsync", "RelativePath": "AddTicketDevOpsLink", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDevOpsLinkDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDevOpsLinkDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketDevOpsLinksAsync", "RelativePath": "AddTicketDevOpsLinkList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDevOpsLinkDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketDiscussionAsync", "RelativePath": "AddTicketDiscussion", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDiscussionDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDiscussionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketDiscussionsAsync", "RelativePath": "AddTicketDiscussionList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDiscussionDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketDocumentAsync", "RelativePath": "AddTicketDocument", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDocumentDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDocumentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketDocumentListAsync", "RelativePath": "AddTicketDocumentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDocumentDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketFromEmailScannerAsync", "RelativePath": "AddTicketFromEmailScanner", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketLinkAsync", "RelativePath": "AddTicketLink", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketLinkDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketLinkDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketLinksAsync", "RelativePath": "AddTicketLinkList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketLinkDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketsAsync", "RelativePath": "AddTicketList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketReviewAsync", "RelativePath": "AddTicketReview", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketReviewDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketReviewDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "AddTicketReviewsAsync", "RelativePath": "AddTicketReviewList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketReviewDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "DeleteInvoiceAsync", "RelativePath": "DeleteInvoice", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "invoiceId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "DeleteInvoiceItemAsync", "RelativePath": "DeleteInvoiceItem", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "invoiceItemId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "DeleteInvoiceItemsAsync", "RelativePath": "DeleteInvoiceItemList", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceItemDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "DeleteInvoicesAsync", "RelativePath": "DeleteInvoiceList", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "DeleteProductAsync", "RelativePath": "DeleteProduct", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "DeleteProductDeploymentTrackingAsync", "RelativePath": "DeleteProductDeploymentTracking", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "productDeploymentTrackingId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "DeleteProductDeploymentTrackingListAsync", "RelativePath": "DeleteProductDeploymentTrackingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductDeploymentTrackingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "DeleteProductLicenseTrackingAsync", "RelativePath": "DeleteProductLicenseTracking", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "productLicenseTrackingId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "DeleteProductLicenseTrackingListAsync", "RelativePath": "DeleteProductLicenseTrackingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductLicenseTrackingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "DeleteProductsAsync", "RelativePath": "DeleteProductList", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "DeleteProductReleaseTrackingAsync", "RelativePath": "DeleteProductReleaseTracking", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "productReleaseTrackingId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "DeleteProductReleaseTrackingListAsync", "RelativePath": "DeleteProductReleaseTrackingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductReleaseTrackingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketAsync", "RelativePath": "DeleteTicket", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketBillingAsync", "RelativePath": "DeleteTicketBilling", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketBillingId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketBillingDeliveryAsync", "RelativePath": "DeleteTicketBillingDelivery", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketBillingDeliveryId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketBillingDeliveriesAsync", "RelativePath": "DeleteTicketBillingDeliveryList", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDeliveryDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketBillingsAsync", "RelativePath": "DeleteTicketBillingList", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketBillingPaymentAsync", "RelativePath": "DeleteTicketBillingPayment", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketBillingPaymentId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketBillingPaymentsAsync", "RelativePath": "DeleteTicketBillingPaymentList", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingPaymentDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketDevOpsLinkAsync", "RelativePath": "DeleteTicketDevOpsLink", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDevOpsLinkId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketDevOpsLinksAsync", "RelativePath": "DeleteTicketDevOpsLinkList", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDevOpsLinkDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketDiscussionAsync", "RelativePath": "DeleteTicketDiscussion", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDiscussionId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketDiscussionsAsync", "RelativePath": "DeleteTicketDiscussionList", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDiscussionDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketDocumentAsync", "RelativePath": "DeleteTicketDocument", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDocumentId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketDocumentListAsync", "RelativePath": "DeleteTicketDocumentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDocumentDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketLinkAsync", "RelativePath": "DeleteTicketLink", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketLinkId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketLinksAsync", "RelativePath": "DeleteTicketLinkList", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketLinkDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketsAsync", "RelativePath": "DeleteTicketList", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketReviewAsync", "RelativePath": "DeleteTicketReview", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketReviewId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "DeleteTicketReviewsAsync", "RelativePath": "DeleteTicketReviewList", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketReviewDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "ExportInvoiceItemExcel", "RelativePath": "ExportInvoiceItemList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceItemDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "ExportInvoiceExcel", "RelativePath": "ExportInvoiceList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "ExportProductExcel", "RelativePath": "ExportProductList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ExportTicketBillingDeliveryExcel", "RelativePath": "ExportTicketBillingDeliveryList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDeliveryDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ExportTicketBillingExcel", "RelativePath": "ExportTicketBillingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ExportTicketBillingPaymentExcel", "RelativePath": "ExportTicketBillingPaymentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingPaymentDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ExportTicketDevOpsLinkExcel", "RelativePath": "ExportTicketDevOpsLinkList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDevOpsLinkDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ExportTicketDiscussionExcel", "RelativePath": "ExportTicketDiscussionList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDiscussionDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ExportTicketDocumentExcel", "RelativePath": "ExportTicketDocumentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDocumentDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ExportTicketLinkExcel", "RelativePath": "ExportTicketLinkList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketLinkDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ExportTicketExcel", "RelativePath": "ExportTicketList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ExportTicketReviewExcel", "RelativePath": "ExportTicketReviewList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketReviewDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "ImportInvoiceItemExcel", "RelativePath": "ImportInvoiceItemList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "ImportInvoiceExcel", "RelativePath": "ImportInvoiceList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "ImportProductExcel", "RelativePath": "ImportProductList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ImportTicketBillingDeliveryExcel", "RelativePath": "ImportTicketBillingDeliveryList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ImportTicketBillingExcel", "RelativePath": "ImportTicketBillingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ImportTicketBillingPaymentExcel", "RelativePath": "ImportTicketBillingPaymentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ImportTicketDevOpsLinkExcel", "RelativePath": "ImportTicketDevOpsLinkList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ImportTicketDiscussionExcel", "RelativePath": "ImportTicketDiscussionList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ImportTicketDocumentExcel", "RelativePath": "ImportTicketDocumentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ImportTicketLinkExcel", "RelativePath": "ImportTicketLinkList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ImportTicketExcel", "RelativePath": "ImportTicketList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "ImportTicketReviewExcel", "RelativePath": "ImportTicketReviewList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "GetInvoiceByIdAsync", "RelativePath": "InvoiceDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "invoiceId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "GetInvoiceByNoAsync", "RelativePath": "InvoiceDetailByNo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "invoiceNo", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "GetInvoiceItemByIdAsync", "RelativePath": "InvoiceItemDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "invoiceItemId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "QueryInvoiceItemListAsync", "RelativePath": "InvoiceItemList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceItemDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "QueryInvoiceListAsync", "RelativePath": "InvoiceList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "GetProductDeploymentTrackingAsync", "RelativePath": "ProductDeploymentTrackingDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productDeploymentTrackingId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "QueryProductDeploymentTrackingListAsync", "RelativePath": "ProductDeploymentTrackingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductDeploymentTrackingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "GetProductByIdAsync", "RelativePath": "ProductDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "GetProductLicenseTrackingAsync", "RelativePath": "ProductLicenseTrackingDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productLicenseTrackingId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "QueryProductLicenseTrackingListAsync", "RelativePath": "ProductLicenseTrackingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductLicenseTrackingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "QueryProductListAsync", "RelativePath": "ProductList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "GetProductReleaseTrackingAsync", "RelativePath": "ProductReleaseTrackingDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productReleaseTrackingId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "QueryProductReleaseTrackingListAsync", "RelativePath": "ProductReleaseTrackingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductReleaseTrackingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "GetTicketBillingDeliveryByIdAsync", "RelativePath": "TicketBillingDeliveryDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketBillingDeliveryId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "QueryTicketBillingDeliveryListAsync", "RelativePath": "TicketBillingDeliveryList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDeliveryDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "GetTicketBillingByIdAsync", "RelativePath": "TicketBillingDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketBillingId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "QueryTicketBillingListAsync", "RelativePath": "TicketBillingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "GetTicketBillingPaymentByIdAsync", "RelativePath": "TicketBillingPaymentDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketBillingPaymentId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "QueryTicketBillingPaymentListAsync", "RelativePath": "TicketBillingPaymentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingPaymentDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "GetTicketByIdAsync", "RelativePath": "TicketDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "GetTicketByNoAsync", "RelativePath": "TicketDetailByNo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketNo", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "GetTicketDevOpsLinkByIdAsync", "RelativePath": "TicketDevOpsLinkDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDevOpsLinkId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "QueryTicketDevOpsLinkListAsync", "RelativePath": "TicketDevOpsLinkList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDevOpsLinkDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "GetTicketDiscussionByIdAsync", "RelativePath": "TicketDiscussionDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDiscussionId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "QueryTicketDiscussionListAsync", "RelativePath": "TicketDiscussionList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDiscussionDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "GetTicketDocumentByIdAsync", "RelativePath": "TicketDocumentDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDocumentId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "QueryTicketDocumentListAsync", "RelativePath": "TicketDocumentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDocumentDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "GetTicketLinkByIdAsync", "RelativePath": "TicketLinkDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketLinkId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "QueryTicketLinkListAsync", "RelativePath": "TicketLinkList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketLinkDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "QueryTicketListAsync", "RelativePath": "TicketList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "GetTicketReviewByIdAsync", "RelativePath": "TicketReviewDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketReviewId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "QueryTicketReviewListAsync", "RelativePath": "TicketReviewList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketReviewDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "UpdateInvoiceAsync", "RelativePath": "UpdateInvoice", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "invoiceDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "UpdateInvoiceItemAsync", "RelativePath": "UpdateInvoiceItem", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "invoiceItemDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceItemDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "UpdateInvoiceItemsAsync", "RelativePath": "UpdateInvoiceItemList", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceItemDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.InvoiceController", "Method": "UpdateInvoicesAsync", "RelativePath": "UpdateInvoiceList", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.InvoiceDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "UpdateProductAsync", "RelativePath": "UpdateProduct", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "productDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "UpdateProductDeploymentTrackingAsync", "RelativePath": "UpdateProductDeploymentTracking", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "productDeploymentTrackingDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductDeploymentTrackingDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "UpdateProductDeploymentTrackingListAsync", "RelativePath": "UpdateProductDeploymentTrackingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductDeploymentTrackingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "UpdateProductLicenseTrackingAsync", "RelativePath": "UpdateProductLicenseTracking", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "productLicenseTrackingDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductLicenseTrackingDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "UpdateProductLicenseTrackingListAsync", "RelativePath": "UpdateProductLicenseTrackingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductLicenseTrackingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "UpdateProductsAsync", "RelativePath": "UpdateProductList", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "UpdateProductReleaseTrackingAsync", "RelativePath": "UpdateProductReleaseTracking", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "productReleaseTrackingDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductReleaseTrackingDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.ProductController", "Method": "UpdateProductReleaseTrackingListAsync", "RelativePath": "UpdateProductReleaseTrackingList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.ProductReleaseTrackingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketAsync", "RelativePath": "UpdateTicket", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketBillingAsync", "RelativePath": "UpdateTicketBilling", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketBillingDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketBillingDeliveryAsync", "RelativePath": "UpdateTicketBillingDelivery", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketBillingDeliveryDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDeliveryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketBillingDeliveriesAsync", "RelativePath": "UpdateTicketBillingDeliveryList", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDeliveryDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketBillingsAsync", "RelativePath": "UpdateTicketBillingList", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketBillingPaymentAsync", "RelativePath": "UpdateTicketBillingPayment", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketBillingPaymentDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingPaymentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketBillingPaymentsAsync", "RelativePath": "UpdateTicketBillingPaymentList", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketBillingPaymentDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketDevOpsLinkAsync", "RelativePath": "UpdateTicketDevOpsLink", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDevOpsLinkDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDevOpsLinkDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketDevOpsLinksAsync", "RelativePath": "UpdateTicketDevOpsLinkList", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDevOpsLinkDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketDiscussionAsync", "RelativePath": "UpdateTicketDiscussion", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDiscussionDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDiscussionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketDiscussionsAsync", "RelativePath": "UpdateTicketDiscussionList", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDiscussionDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketDocumentAsync", "RelativePath": "UpdateTicketDocument", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketDocumentDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDocumentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketDocumentListAsync", "RelativePath": "UpdateTicketDocumentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDocumentDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketLinkAsync", "RelativePath": "UpdateTicketLink", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketLinkDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketLinkDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketLinksAsync", "RelativePath": "UpdateTicketLinkList", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketLinkDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketsAsync", "RelativePath": "UpdateTicketList", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketReviewAsync", "RelativePath": "UpdateTicketReview", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketReviewDto", "Type": "Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketReviewDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.TicketController", "Method": "UpdateTicketReviewsAsync", "RelativePath": "UpdateTicketReviewList", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Ticket.BusinessLogic.DTOs.TicketReviewDto, Visfuture.OneTeam.Ticket.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Ticket.Api.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Visfuture.OneTeam.Ticket.Api.WeatherForecast, Visfuture.OneTeam.Ticket.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]