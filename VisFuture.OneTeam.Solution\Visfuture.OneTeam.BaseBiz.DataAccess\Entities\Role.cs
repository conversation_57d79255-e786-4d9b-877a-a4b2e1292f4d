﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.DataAccess.Entities;

public class Role : CodeEntity
{
    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public bool IsActive { get; set; }

    public string Usage { get; set; } = null!;

    public virtual ICollection<AssignableRole> AssignableRoles { get; set; } = [];

    public virtual ICollection<RoleAccess> RoleAccesses { get; set; } = [];
}