﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.DataAccess.Entities;

public partial class OrganizationHierarchy : CodeEntity
{

    public string? Description { get; set; }

    public string Name { get; set; } = string.Empty;

    public bool IsActive { get; set; }

    public string MainType { get; set; } = null!;

    public string SubType { get; set; } = null!;

    public Guid? SuperiorId { get; set; }

    public string? DefaultLocation { get; set; }

    public string? DefaultWorkSchedule { get; set; }

    public virtual ICollection<AssignableRole> AssignableRoles { get; set; } = [];

    public virtual ICollection<OrganizationHierarchy> InverseSuperior { get; set; } = [];

    public virtual ICollection<OrganizationEmployee> OrganizationEmployees { get; set; } = [];

    public virtual OrganizationHierarchy? Superior { get; set; }
}
