﻿
using System.Data;
using System.Data.Common;
using System.Dynamic;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Visfuture.OneTeam.Core.Common.Base.DatabaseContext;

namespace Visfuture.OneTeam.Core.Common.Extensions;

public static class DatabaseContextExtensions
{
    #region Inline sql scripts
    public static async Task<List<T>> SqlQueryAsync<T, TContext>(this DatabaseContext<TContext> context, string sql, params object[] parameters) where T : class where TContext : DbContext
    {
        return await context.Set<T>().FromSqlRaw(sql, parameters).ToListAsync();
    }

    public static async Task<int> ExecuteSqlCommandAsync<TContext>(this DatabaseContext<TContext> context, string sql, params object[] parameters) where TContext : DbContext
    {
        return await context.Database.ExecuteSqlRawAsync(sql, parameters);
    }

    public static async Task<List<T>> SqlQueryAsync<T, TContext>(this DatabaseContext<TContext> context, string sql, Func<DbDataReader, T> map, params object[] parameters) where TContext : DbContext
    {
        using var command = context.Database.GetDbConnection().CreateCommand();
        command.CommandText = sql;
        command.Parameters.AddRange(parameters);

        await context.Database.OpenConnectionAsync();

        using var result = await command.ExecuteReaderAsync();
        var entities = new List<T>();

        while (await result.ReadAsync())
        {
            entities.Add(map(result));
        }

        return entities;
    }
    #endregion

    #region Stored procedures
    // New method for executing a stored procedure with dynamic parameters
    public static async Task<List<T>> ExecuteStoredProcedureAsync<T, TContext>(
        this DatabaseContext<TContext> context,
        string storedProcedureName,
        IDictionary<string, object>? parameters = null) where T : class, new() where TContext : DbContext
    {
        using var command = context.Database.GetDbConnection().CreateCommand();
        command.CommandText = storedProcedureName;
        command.CommandType = CommandType.StoredProcedure;

        if (parameters != null)
        {
            foreach (var param in parameters)
            {
                var parameter = command.CreateParameter();
                parameter.ParameterName = param.Key;
                parameter.Value = param.Value ?? DBNull.Value;
                command.Parameters.Add(parameter);
            }
        }

        await context.Database.OpenConnectionAsync();

        using var result = await command.ExecuteReaderAsync();
        return await MapToListAsync<T>(result);
    }

    // Method for executing a stored procedure and returning a single result
    public static async Task<T?> ExecuteStoredProcedureSingleAsync<T, TContext>(
        this DatabaseContext<TContext> context,
        string storedProcedureName,
        IDictionary<string, object>? parameters = null) where T : class, new() where TContext : DbContext
    {
        var results = await ExecuteStoredProcedureAsync<T, TContext>(context, storedProcedureName, parameters);
        return results.FirstOrDefault();
    }

    // Method for executing a stored procedure with dynamic result
    public static async Task<List<dynamic>> ExecuteStoredProcedureDynamicAsync<TContext>(
        this DatabaseContext<TContext> context,
        string storedProcedureName,
        IDictionary<string, object>? parameters = null) where TContext : DbContext
    {
        using var command = context.Database.GetDbConnection().CreateCommand();
        command.CommandText = storedProcedureName;
        command.CommandType = CommandType.StoredProcedure;

        if (parameters != null)
        {
            foreach (var param in parameters)
            {
                var parameter = command.CreateParameter();
                parameter.ParameterName = param.Key;
                parameter.Value = param.Value ?? DBNull.Value;
                command.Parameters.Add(parameter);
            }
        }

        await context.Database.OpenConnectionAsync();

        using var result = await command.ExecuteReaderAsync();
        return await MapToDynamicListAsync(result);
    }

    // Helper method to map DbDataReader to a list of objects
    private static async Task<List<T>> MapToListAsync<T>(DbDataReader reader) where T : class, new()
    {
        var results = new List<T>();
        var properties = typeof(T).GetProperties();

        while (await reader.ReadAsync())
        {
            var item = new T();
            foreach (var property in properties)
            {
                if (!reader.IsDBNull(reader.GetOrdinal(property.Name)))
                {
                    property.SetValue(item, reader[property.Name]);
                }
            }
            results.Add(item);
        }

        return results;
    }

    // Helper method to map DbDataReader to a list of dynamic objects
    private static async Task<List<dynamic>> MapToDynamicListAsync(DbDataReader reader)
    {
        var results = new List<dynamic>();

        while (await reader.ReadAsync())
        {
            var item = new ExpandoObject() as IDictionary<string, object?>;
            for (var i = 0; i < reader.FieldCount; i++)
            {
                item.Add(reader.GetName(i), reader.IsDBNull(i) ? null : reader[i]);
            }
            results.Add(item);
        }

        return results;
    }

    // Method to execute a stored procedure with output parameters
    public static async Task<(T? Result, IDictionary<string, object> OutputParameters)> ExecuteStoredProcedureWithOutputAsync<T, TContext>(
        this DatabaseContext<TContext> context,
        string storedProcedureName,
        IDictionary<string, (object Value, SqlDbType Type, ParameterDirection Direction)> parameters) where T : class, new() where TContext : DbContext
    {
        using var command = context.Database.GetDbConnection().CreateCommand();
        command.CommandText = storedProcedureName;
        command.CommandType = CommandType.StoredProcedure;

        var sqlParameters = new List<SqlParameter>();

        foreach (var param in parameters)
        {
            var parameter = new SqlParameter
            {
                ParameterName = param.Key,
                SqlDbType = param.Value.Type,
                Direction = param.Value.Direction,
                Value = param.Value.Value ?? DBNull.Value
            };
            sqlParameters.Add(parameter);
            command.Parameters.Add(parameter);
        }

        await context.Database.OpenConnectionAsync();

        using var result = await command.ExecuteReaderAsync();
        var resultList = await MapToListAsync<T>(result);

        var outputParameters = sqlParameters
            .Where(p => p.Direction == ParameterDirection.Output || p.Direction == ParameterDirection.InputOutput)
            .ToDictionary(p => p.ParameterName, p => p.Value);

        return (resultList.FirstOrDefault(), outputParameters);
    }
    #endregion
}

