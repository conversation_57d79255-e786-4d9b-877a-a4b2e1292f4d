{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ReverseProxy": {
    "Routes": {
      "basebiz-route": {
        "ClusterId": "basebiz-cluster",
        //"AuthorizationPolicy": "authenticated",
        "Match": {
          "Path": "/basebiz-service/{**catch-all}"
        },
        "Transforms": [
          { "PathPattern": "{**catch-all}" }
        ]
      },
      "project-route": {
        "ClusterId": "project-cluster",
        //"AuthorizationPolicy": "authenticated",
        "Match": {
          "Path": "/project-service/{**catch-all}"
        },
        "Transforms": [
          { "PathPattern": "{**catch-all}" }
        ]
      },
      "people-route": {
        "ClusterId": "people-cluster",
        "RateLimiterPolicy": "fixed",
        "Match": {
          "Path": "/people-service/{**catch-all}"
        },
        "Transforms": [
          { "PathPattern": "{**catch-all}" }
        ]
      }
    },
    "Clusters": {
      "basebiz-cluster": {
        "Destinations": {
          "destination1": {
            "Address": "https://localhost:7250/"
          }
        }
      },
      "project-cluster": {
        "Destinations": {
          "destination1": {
            "Address": "https://localhost:7260/"
          }
        }
      },
      "people-cluster": {
        "Destinations": {
          "destination1": {
            "Address": "https://localhost:7236/"
          }
        }
      }
    }
  }
}
