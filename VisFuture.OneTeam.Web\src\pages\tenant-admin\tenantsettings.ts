export interface SettingCard {
  id: number
  icon: string
  name: string
  intro: string
  route: string
}

export const tenantSettings: SettingCard[] = [
  {
    id: 1,
    icon: 'manage_accounts',
    name: 'Tenant Roles',
    intro: 'Manage role definitions and assignments.',
    route: '/tenant-admin/tenant-roles',
  },
  {
    id: 2,
    icon: 'groups',
    name: 'Tenant Employees',
    intro: "Manage your organization's employees.",
    route: '/tenant-admin/tenant-employees',
  },
  {
    id: 3,
    icon: 'account_tree',
    name: 'Org Hierarchy',
    intro: 'Define the structure of your organization.',
    route: '/tenant-admin/organizational-hierarchy',
  },
  {
    id: 4,
    icon: 'vpn_key',
    name: 'Assignable Roles',
    intro: 'Control what roles are assignable by org level.',
    route: '/tenant-admin/assignable-roles',
  },
  {
    id: 5,
    icon: 'security',
    name: 'Role Access',
    intro: 'Set access rights for different roles.',
    route: '/tenant-admin/role-access',
  },
  {
    id: 6,
    icon: 'badge',
    name: 'Role Assignments',
    intro: 'Assign roles to specific users.',
    route: '/tenant-admin/role-assignments',
  },
  {
    id: 7,
    icon: 'category',
    name: 'Code Types',
    intro: 'Create and manage code categories.',
    route: '/tenant-admin/code-types',
  },
  {
    id: 8,
    icon: 'translate',
    name: 'I18n Keys',
    intro: 'Manage internationalization keys.',
    route: '/tenant-admin/i18n-keys',
  },
  {
    id: 9,
    icon: 'notifications',
    name: 'Notification Templates',
    intro: 'Manage notification templates.',
    route: '/tenant-admin/notification-templates',
  },
  {
    id: 10,
    icon: 'tag',
    name: 'Sequence Numbers',
    intro: 'Manage sequence numbers.',
    route: '/tenant-admin/sequense-numbers',
  },
]
