﻿using MapsterMapper;
using Microsoft.AspNetCore.Http;
using Visfuture.OneTeam.BaseBiz.DataAccess.DbContext;
using Visfuture.OneTeam.Core.Common.Base.Manager;

namespace Visfuture.OneTeam.BaseBiz.DataAccess;

public abstract class BaseBizBaseManager(
    IHttpContextAccessor httpContextAccessor,
    AppDataContext appDataContext,
    IMapper mapper) : BaseManager(httpContextAccessor)
{
    protected readonly AppDataContext DB = appDataContext;

    protected IMapper _mapper = mapper;
}