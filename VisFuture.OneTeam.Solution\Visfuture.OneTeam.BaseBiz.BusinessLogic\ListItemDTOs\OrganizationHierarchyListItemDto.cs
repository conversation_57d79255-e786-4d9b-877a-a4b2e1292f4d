﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;

public partial class OrganizationHierarchyListItemDto : CodeDto
{

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public bool IsActive { get; set; }

    public string MainType { get; set; } = null!;

    public string SubType { get; set; } = null!;

    public Guid? SuperiorId { get; set; }

    public string? DefaultLocation { get; set; }

    public string? DefaultWorkSchedule { get; set; }

}
