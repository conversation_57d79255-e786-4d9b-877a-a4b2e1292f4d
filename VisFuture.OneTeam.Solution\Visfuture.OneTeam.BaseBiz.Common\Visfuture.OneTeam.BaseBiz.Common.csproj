﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.6.1" />
    <PackageReference Include="System.Security.Claims" Version="4.3.0" />
    <PackageReference Include="System.Security.Cryptography.Cng" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Visfuture.OneTeam.Core.Common\Visfuture.OneTeam.Core.Common.csproj" />
    <ProjectReference Include="..\Visfuture.OneTeam.Project.DataAccess\Visfuture.OneTeam.Project.DataAccess.csproj" />
  </ItemGroup>

</Project>
