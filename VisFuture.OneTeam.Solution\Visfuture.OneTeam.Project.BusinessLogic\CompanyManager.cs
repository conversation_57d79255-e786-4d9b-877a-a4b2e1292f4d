using Mapster;
using MapsterMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Core.Common.Helpers;
using Visfuture.OneTeam.Project.BusinessLogic.DTOs;
using Visfuture.OneTeam.Project.BusinessLogic.Interfaces;
using Visfuture.OneTeam.Project.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.Project.DataAccess.DbContext;
using Visfuture.OneTeam.Project.DataAccess.Entities;

namespace Visfuture.OneTeam.Project.BusinessLogic;

public class CompanyManager(
    IConfiguration configuration,
    IHttpClientFactory httpClientFactory,
    IHttpContextAccessor httpContextAccessor,
    AppDataContext appDataContext,
    IMapper mapper) : ProjectBaseManager(httpContextAccessor, appDataContext, mapper), ICompanyManager
{
    private readonly CrudHelper<Company, CompanyDto, CompanyListItemDto> _companyCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.Companies);

    private readonly CrudHelper<CompanyContact, ContactDto, ContactListItemDto> _contactCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.CompanyContacts);

    #region Company

    public async Task<EntityResponse<CompanyDto>> GetCompanyByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _companyCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<CompanyDto>> GetCompanyByCodeAsync(string code,
        CancellationToken cancellationToken = default)
    {
        return await _companyCrudHelper.GetEntityBy(t => t.Code == code, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteCompanyAsync(Guid id, CancellationToken cancellationToken = default)
    {
        //await _appdbContext.CompanyContacts.Where(t => t.CompanyId == id).ExecuteDeleteAsync(cancellationToken);
        return await _companyCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddCompanyAsync(CompanyDto companyDto,
        CancellationToken cancellationToken = default)
    {
        return await _companyCrudHelper.AddEntity(GetCurrentUser(), companyDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateCompanyAsync(CompanyDto companyDto,
        CancellationToken cancellationToken = default)
    {
        return await _companyCrudHelper.UpdateEntity(GetCurrentUser(), companyDto, cancellationToken);
    }

    public async Task<EntityResponsePaged<CompanyListItemDto>> QueryCompanyAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _companyCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse> AddCompaniesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _companyCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateCompaniesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for updating companies in bulk
        return await _companyCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteCompaniesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for deleting companies in bulk
        return await _companyCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportCompanyExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _companyCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportCompanyExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _companyCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region Contact

    public async Task<EntityResponsePaged<ContactListItemDto>> QueryContactAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _contactCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse<ContactDto>> GetContactByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _contactCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteContactAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _contactCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddContactAsync(ContactDto contactDto,
        CancellationToken cancellationToken = default)
    {
        return await _contactCrudHelper.AddEntity(GetCurrentUser(), contactDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateContactAsync(ContactDto contactDto,
        CancellationToken cancellationToken = default)
    {
        return await _contactCrudHelper.UpdateEntity(GetCurrentUser(), contactDto, cancellationToken);
    }

    public async Task<EntityResponse> AddContactsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for adding contacts in bulk
        return await _contactCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateContactsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for updating contacts in bulk
        return await _contactCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteContactsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for deleting contacts in bulk
        return await _contactCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportContactExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _contactCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportContactExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _contactCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    public async Task<EntityResponse<List<ContactDto>>> GetContactsByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        return EntityResponse<List<ContactDto>>.Success(
            await appDataContext.CompanyContacts
            .AsNoTracking()
            .Where(p => p.Email == email)
            .Select(p => p.Adapt<ContactDto>())
            .ToListAsync(cancellationToken)
            );
    }

    #endregion
}