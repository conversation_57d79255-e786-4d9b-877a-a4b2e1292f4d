﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.Project.DataAccess.Entities;

public partial class ProjectPaymentPlan : TenantEntity
{
    public Guid ProjectId { get; set; }

    public Guid? ContractId { get; set; }

    public Guid PayerId { get; set; }

    public Guid PayeeId { get; set; }

    public string Name { get; set; } = null!;

    public string? Category { get; set; }

    public string? PayMethod { get; set; }

    public string? Description { get; set; }

    public DateTime DueDate { get; set; }

    public decimal Percentage { get; set; }

    public string Currency { get; set; } = null!;

    public decimal Amount { get; set; }

    public decimal TaxRate { get; set; }

    public decimal TaxAmount { get; set; }

    public decimal TotalAmount { get; set; }

    public virtual Project Project { get; set; } = null!;

    public virtual ICollection<ProjectPaymentFact> PaymentFacts { get; set; } = [];
}
