using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.WebUtilities;
using System.Net.Mail;
using System.Net.Mime;
using System.Text.Json;
using System.Threading;
using Visfuture.OneTeam.Core.Common.Base.Controller;
using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Ticket.BusinessLogic.DTOs;
using Visfuture.OneTeam.Ticket.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.Ticket.InternalService.interfaces;

namespace Visfuture.OneTeam.Ticket.Api.Controllers;

public class TicketController(ITicketService ticketService) : BaseController<TicketController>()
{
    private readonly ITicketService ticketService = ticketService;

    #region Ticket
    [HttpPost("TicketList")]
    public async Task<IActionResult> QueryTicketListAsync([FromBody] BaseQuery<TicketDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<TicketListItemDto> result = await ticketService.QueryTicketAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("TicketDetail")]
    public async Task<IActionResult> GetTicketByIdAsync(Guid ticketId, CancellationToken cancellationToken = default)
    {
        EntityResponse<TicketDto> result = await ticketService.GetTicketByIdAsync(ticketId, cancellationToken);
        return Ok(result);
    }

    [HttpGet("TicketDetailByNo")]
    public async Task<IActionResult> GetTicketByNoAsync(string ticketNo, CancellationToken cancellationToken = default)
    {
        EntityResponse<TicketDto> result = await ticketService.GetTicketByNoAsync(ticketNo, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddTicket")]
    public async Task<IActionResult> AddTicketAsync(TicketDto ticketDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.AddTicketAsync(ticketDto, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddTicketFromEmailScanner")]
    public async Task<IActionResult> AddTicketFromEmailScannerAsync()
    {
        var contentType = Request.Headers.FirstOrDefault(
            p => string.Equals(p.Key, "Content-Type", StringComparison.OrdinalIgnoreCase));
        var contentTypeValue = contentType.Value.FirstOrDefault();
        var contentTypeParams = contentTypeValue!.Split(";").Select(p => p.Trim());
        var boundary = contentTypeParams.Where(p => p.StartsWith("boundary"))
            .Select(p => p.Split("=")[1])
            .FirstOrDefault();
        if (boundary!.StartsWith('"') && boundary.EndsWith('"'))
        {
            boundary = boundary.Substring(1, boundary.Length - 2);
        }

        EmailMessageDto? emailMessage = null;

        var multipartReader = new MultipartReader(boundary, Request.Body);
        MultipartSection? multipartSection = null;
        while ((multipartSection = await multipartReader.ReadNextSectionAsync()) != null)
        {
            var contentDisposition = new ContentDisposition(multipartSection!.ContentDisposition!);
            var bodyPartName = contentDisposition.Parameters["name"];

            if (string.Equals(bodyPartName, "emailMessage", StringComparison.OrdinalIgnoreCase))
            {
                var json = await new StreamReader(multipartSection.Body).ReadToEndAsync();
                emailMessage = JsonSerializer.Deserialize<EmailMessageDto>(json);
            }
        }

        var result = await ticketService.AddTicketFromEmailScannerAsync(emailMessage!, default);
        return Ok(result);
    }

    [HttpPut("UpdateTicket")]
    public async Task<IActionResult> UpdateTicketAsync(TicketDto ticketDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.UpdateTicketAsync(ticketDto, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicket")]
    public async Task<IActionResult> DeleteTicketAsync(Guid ticketId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.DeleteTicketAsync(ticketId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddTicketList")]
    public async Task<IActionResult> AddTicketsAsync([FromBody] BaseQuery<TicketDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.AddTicketsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketList")]
    public async Task<IActionResult> UpdateTicketsAsync([FromBody] BaseQuery<TicketDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.UpdateTicketsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketList")]
    public async Task<IActionResult> DeleteTicketsAsync([FromBody] BaseQuery<TicketDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.DeleteTicketsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportTicketList")]
    public async Task<IActionResult> ExportTicketExcel([FromBody] BaseQuery<TicketDto> request, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ExportTicketExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportTicketList")]
    public async Task<IActionResult> ImportTicketExcel(B64File file, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ImportTicketExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region TicketReview
    [HttpPost("TicketReviewList")]
    public async Task<IActionResult> QueryTicketReviewListAsync([FromBody] BaseQuery<TicketReviewDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<TicketReviewListItemDto> result = await ticketService.QueryTicketReviewAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("TicketReviewDetail")]
    public async Task<IActionResult> GetTicketReviewByIdAsync(Guid ticketReviewId, CancellationToken cancellationToken = default)
    {
        EntityResponse<TicketReviewDto> result = await ticketService.GetTicketReviewByIdAsync(ticketReviewId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddTicketReview")]
    public async Task<IActionResult> AddTicketReviewAsync(TicketReviewDto ticketReviewDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.AddTicketReviewAsync(ticketReviewDto, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketReview")]
    public async Task<IActionResult> UpdateTicketReviewAsync(TicketReviewDto ticketReviewDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.UpdateTicketReviewAsync(ticketReviewDto, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketReview")]
    public async Task<IActionResult> DeleteTicketReviewAsync(Guid ticketReviewId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.DeleteTicketReviewAsync(ticketReviewId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddTicketReviewList")]
    public async Task<IActionResult> AddTicketReviewsAsync([FromBody] BaseQuery<TicketReviewDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.AddTicketReviewsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketReviewList")]
    public async Task<IActionResult> UpdateTicketReviewsAsync([FromBody] BaseQuery<TicketReviewDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.UpdateTicketReviewsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketReviewList")]
    public async Task<IActionResult> DeleteTicketReviewsAsync([FromBody] BaseQuery<TicketReviewDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.DeleteTicketReviewsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportTicketReviewList")]
    public async Task<IActionResult> ExportTicketReviewExcel([FromBody] BaseQuery<TicketReviewDto> request, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ExportTicketReviewExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportTicketReviewList")]
    public async Task<IActionResult> ImportTicketReviewExcel(B64File file, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ImportTicketReviewExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region TicketLink
    [HttpPost("TicketLinkList")]
    public async Task<IActionResult> QueryTicketLinkListAsync([FromBody] BaseQuery<TicketLinkDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<TicketLinkListItemDto> result = await ticketService.QueryTicketLinkAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("TicketLinkDetail")]
    public async Task<IActionResult> GetTicketLinkByIdAsync(Guid ticketLinkId, CancellationToken cancellationToken = default)
    {
        EntityResponse<TicketLinkDto> result = await ticketService.GetTicketLinkByIdAsync(ticketLinkId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddTicketLink")]
    public async Task<IActionResult> AddTicketLinkAsync(TicketLinkDto ticketLinkDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.AddTicketLinkAsync(ticketLinkDto, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketLink")]
    public async Task<IActionResult> UpdateTicketLinkAsync(TicketLinkDto ticketLinkDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.UpdateTicketLinkAsync(ticketLinkDto, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketLink")]
    public async Task<IActionResult> DeleteTicketLinkAsync(Guid ticketLinkId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.DeleteTicketLinkAsync(ticketLinkId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddTicketLinkList")]
    public async Task<IActionResult> AddTicketLinksAsync([FromBody] BaseQuery<TicketLinkDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.AddTicketLinksAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketLinkList")]
    public async Task<IActionResult> UpdateTicketLinksAsync([FromBody] BaseQuery<TicketLinkDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.UpdateTicketLinksAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketLinkList")]
    public async Task<IActionResult> DeleteTicketLinksAsync([FromBody] BaseQuery<TicketLinkDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.DeleteTicketLinksAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportTicketLinkList")]
    public async Task<IActionResult> ExportTicketLinkExcel([FromBody] BaseQuery<TicketLinkDto> request, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ExportTicketLinkExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportTicketLinkList")]
    public async Task<IActionResult> ImportTicketLinkExcel(B64File file, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ImportTicketLinkExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region TicketDevOpsLink
    [HttpPost("TicketDevOpsLinkList")]
    public async Task<IActionResult> QueryTicketDevOpsLinkListAsync([FromBody] BaseQuery<TicketDevOpsLinkDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<TicketDevOpsLinkListItemDto> result = await ticketService.QueryTicketDevOpsLinkAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("TicketDevOpsLinkDetail")]
    public async Task<IActionResult> GetTicketDevOpsLinkByIdAsync(Guid ticketDevOpsLinkId, CancellationToken cancellationToken = default)
    {
        EntityResponse<TicketDevOpsLinkDto> result = await ticketService.GetTicketDevOpsLinkByIdAsync(ticketDevOpsLinkId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddTicketDevOpsLink")]
    public async Task<IActionResult> AddTicketDevOpsLinkAsync(TicketDevOpsLinkDto ticketDevOpsLinkDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.AddTicketDevOpsLinkAsync(ticketDevOpsLinkDto, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketDevOpsLink")]
    public async Task<IActionResult> UpdateTicketDevOpsLinkAsync(TicketDevOpsLinkDto ticketDevOpsLinkDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.UpdateTicketDevOpsLinkAsync(ticketDevOpsLinkDto, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketDevOpsLink")]
    public async Task<IActionResult> DeleteTicketDevOpsLinkAsync(Guid ticketDevOpsLinkId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.DeleteTicketDevOpsLinkAsync(ticketDevOpsLinkId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddTicketDevOpsLinkList")]
    public async Task<IActionResult> AddTicketDevOpsLinksAsync([FromBody] BaseQuery<TicketDevOpsLinkDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.AddTicketDevOpsLinksAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketDevOpsLinkList")]
    public async Task<IActionResult> UpdateTicketDevOpsLinksAsync([FromBody] BaseQuery<TicketDevOpsLinkDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.UpdateTicketDevOpsLinksAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketDevOpsLinkList")]
    public async Task<IActionResult> DeleteTicketDevOpsLinksAsync([FromBody] BaseQuery<TicketDevOpsLinkDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.DeleteTicketDevOpsLinksAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportTicketDevOpsLinkList")]
    public async Task<IActionResult> ExportTicketDevOpsLinkExcel([FromBody] BaseQuery<TicketDevOpsLinkDto> request, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ExportTicketDevOpsLinkExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportTicketDevOpsLinkList")]
    public async Task<IActionResult> ImportTicketDevOpsLinkExcel(B64File file, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ImportTicketDevOpsLinkExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region TicketBillingPayment
    [HttpPost("TicketBillingPaymentList")]
    public async Task<IActionResult> QueryTicketBillingPaymentListAsync([FromBody] BaseQuery<TicketBillingPaymentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<TicketBillingPaymentListItemDto> result = await ticketService.QueryTicketBillingPaymentAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("TicketBillingPaymentDetail")]
    public async Task<IActionResult> GetTicketBillingPaymentByIdAsync(Guid ticketBillingPaymentId, CancellationToken cancellationToken = default)
    {
        EntityResponse<TicketBillingPaymentDto> result = await ticketService.GetTicketBillingPaymentByIdAsync(ticketBillingPaymentId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddTicketBillingPayment")]
    public async Task<IActionResult> AddTicketBillingPaymentAsync(TicketBillingPaymentDto ticketBillingPaymentDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.AddTicketBillingPaymentAsync(ticketBillingPaymentDto, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketBillingPayment")]
    public async Task<IActionResult> UpdateTicketBillingPaymentAsync(TicketBillingPaymentDto ticketBillingPaymentDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.UpdateTicketBillingPaymentAsync(ticketBillingPaymentDto, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketBillingPayment")]
    public async Task<IActionResult> DeleteTicketBillingPaymentAsync(Guid ticketBillingPaymentId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.DeleteTicketBillingPaymentAsync(ticketBillingPaymentId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddTicketBillingPaymentList")]
    public async Task<IActionResult> AddTicketBillingPaymentsAsync([FromBody] BaseQuery<TicketBillingPaymentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.AddTicketBillingPaymentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketBillingPaymentList")]
    public async Task<IActionResult> UpdateTicketBillingPaymentsAsync([FromBody] BaseQuery<TicketBillingPaymentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.UpdateTicketBillingPaymentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketBillingPaymentList")]
    public async Task<IActionResult> DeleteTicketBillingPaymentsAsync([FromBody] BaseQuery<TicketBillingPaymentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.DeleteTicketBillingPaymentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportTicketBillingPaymentList")]
    public async Task<IActionResult> ExportTicketBillingPaymentExcel([FromBody] BaseQuery<TicketBillingPaymentDto> request, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ExportTicketBillingPaymentExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportTicketBillingPaymentList")]
    public async Task<IActionResult> ImportTicketBillingPaymentExcel(B64File file, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ImportTicketBillingPaymentExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region TicketBillingDelivery
    [HttpPost("TicketBillingDeliveryList")]
    public async Task<IActionResult> QueryTicketBillingDeliveryListAsync([FromBody] BaseQuery<TicketBillingDeliveryDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<TicketBillingDeliveryListItemDto> result = await ticketService.QueryTicketBillingDeliveryAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("TicketBillingDeliveryDetail")]
    public async Task<IActionResult> GetTicketBillingDeliveryByIdAsync(Guid ticketBillingDeliveryId, CancellationToken cancellationToken = default)
    {
        EntityResponse<TicketBillingDeliveryDto> result = await ticketService.GetTicketBillingDeliveryByIdAsync(ticketBillingDeliveryId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddTicketBillingDelivery")]
    public async Task<IActionResult> AddTicketBillingDeliveryAsync(TicketBillingDeliveryDto ticketBillingDeliveryDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.AddTicketBillingDeliveryAsync(ticketBillingDeliveryDto, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketBillingDelivery")]
    public async Task<IActionResult> UpdateTicketBillingDeliveryAsync(TicketBillingDeliveryDto ticketBillingDeliveryDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.UpdateTicketBillingDeliveryAsync(ticketBillingDeliveryDto, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketBillingDelivery")]
    public async Task<IActionResult> DeleteTicketBillingDeliveryAsync(Guid ticketBillingDeliveryId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.DeleteTicketBillingDeliveryAsync(ticketBillingDeliveryId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddTicketBillingDeliveryList")]
    public async Task<IActionResult> AddTicketBillingDeliveriesAsync([FromBody] BaseQuery<TicketBillingDeliveryDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.AddTicketBillingDeliveriesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketBillingDeliveryList")]
    public async Task<IActionResult> UpdateTicketBillingDeliveriesAsync([FromBody] BaseQuery<TicketBillingDeliveryDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.UpdateTicketBillingDeliveriesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketBillingDeliveryList")]
    public async Task<IActionResult> DeleteTicketBillingDeliveriesAsync([FromBody] BaseQuery<TicketBillingDeliveryDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.DeleteTicketBillingDeliveriesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportTicketBillingDeliveryList")]
    public async Task<IActionResult> ExportTicketBillingDeliveryExcel([FromBody] BaseQuery<TicketBillingDeliveryDto> request, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ExportTicketBillingDeliveryExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportTicketBillingDeliveryList")]
    public async Task<IActionResult> ImportTicketBillingDeliveryExcel(B64File file, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ImportTicketBillingDeliveryExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region TicketBilling
    [HttpPost("TicketBillingList")]
    public async Task<IActionResult> QueryTicketBillingListAsync([FromBody] BaseQuery<TicketBillingDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<TicketBillingListItemDto> result = await ticketService.QueryTicketBillingAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("TicketBillingDetail")]
    public async Task<IActionResult> GetTicketBillingByIdAsync(Guid ticketBillingId, CancellationToken cancellationToken = default)
    {
        EntityResponse<TicketBillingDto> result = await ticketService.GetTicketBillingByIdAsync(ticketBillingId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddTicketBilling")]
    public async Task<IActionResult> AddTicketBillingAsync(TicketBillingDto ticketBillingDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.AddTicketBillingAsync(ticketBillingDto, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketBilling")]
    public async Task<IActionResult> UpdateTicketBillingAsync(TicketBillingDto ticketBillingDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.UpdateTicketBillingAsync(ticketBillingDto, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketBilling")]
    public async Task<IActionResult> DeleteTicketBillingAsync(Guid ticketBillingId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.DeleteTicketBillingAsync(ticketBillingId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddTicketBillingList")]
    public async Task<IActionResult> AddTicketBillingsAsync([FromBody] BaseQuery<TicketBillingDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.AddTicketBillingsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketBillingList")]
    public async Task<IActionResult> UpdateTicketBillingsAsync([FromBody] BaseQuery<TicketBillingDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.UpdateTicketBillingsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketBillingList")]
    public async Task<IActionResult> DeleteTicketBillingsAsync([FromBody] BaseQuery<TicketBillingDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.DeleteTicketBillingsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportTicketBillingList")]
    public async Task<IActionResult> ExportTicketBillingExcel([FromBody] BaseQuery<TicketBillingDto> request, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ExportTicketBillingExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportTicketBillingList")]
    public async Task<IActionResult> ImportTicketBillingExcel(B64File file, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ImportTicketBillingExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region Ticket Discussion

    [HttpPost("TicketDiscussionList")]
    public async Task<IActionResult> QueryTicketDiscussionListAsync([FromBody] BaseQuery<TicketDiscussionDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<TicketDiscussionListItemDto> result = await ticketService.QueryTicketDiscussionAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("TicketDiscussionDetail")]
    public async Task<IActionResult> GetTicketDiscussionByIdAsync(Guid ticketDiscussionId, CancellationToken cancellationToken = default)
    {
        EntityResponse<TicketDiscussionDto> result = await ticketService.GetTicketDiscussionByIdAsync(ticketDiscussionId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddTicketDiscussion")]
    public async Task<IActionResult> AddTicketDiscussionAsync(TicketDiscussionDto ticketDiscussionDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.AddTicketDiscussionAsync(ticketDiscussionDto, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketDiscussion")]
    public async Task<IActionResult> UpdateTicketDiscussionAsync(TicketDiscussionDto ticketDiscussionDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.UpdateTicketDiscussionAsync(ticketDiscussionDto, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketDiscussion")]
    public async Task<IActionResult> DeleteTicketDiscussionAsync(Guid ticketDiscussionId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.DeleteTicketDiscussionAsync(ticketDiscussionId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddTicketDiscussionList")]
    public async Task<IActionResult> AddTicketDiscussionsAsync([FromBody] BaseQuery<TicketDiscussionDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.AddTicketDiscussionsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketDiscussionList")]
    public async Task<IActionResult> UpdateTicketDiscussionsAsync([FromBody] BaseQuery<TicketDiscussionDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.UpdateTicketDiscussionsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketDiscussionList")]
    public async Task<IActionResult> DeleteTicketDiscussionsAsync([FromBody] BaseQuery<TicketDiscussionDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.DeleteTicketDiscussionsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportTicketDiscussionList")]
    public async Task<IActionResult> ExportTicketDiscussionExcel([FromBody] BaseQuery<TicketDiscussionDto> request, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ExportTicketDiscussionExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportTicketDiscussionList")]
    public async Task<IActionResult> ImportTicketDiscussionExcel(B64File file, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ImportTicketDiscussionExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region TicketDocument
    [HttpPost("TicketDocumentList")]
    public async Task<IActionResult> QueryTicketDocumentListAsync([FromBody] BaseQuery<TicketDocumentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<TicketDocumentListItemDto> result = await ticketService.QueryTicketDocumentAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("TicketDocumentDetail")]
    public async Task<IActionResult> GetTicketDocumentByIdAsync(Guid ticketDocumentId, CancellationToken cancellationToken = default)
    {
        EntityResponse<TicketDocumentDto> result = await ticketService.GetTicketDocumentByIdAsync(ticketDocumentId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddTicketDocument")]
    public async Task<IActionResult> AddTicketDocumentAsync(TicketDocumentDto ticketDocumentDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.AddTicketDocumentAsync(ticketDocumentDto, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateTicketDocument")]
    public async Task<IActionResult> UpdateTicketDocumentAsync(TicketDocumentDto ticketDocumentDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.UpdateTicketDocumentAsync(ticketDocumentDto, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteTicketDocument")]
    public async Task<IActionResult> DeleteTicketDocumentAsync(Guid ticketDocumentId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await ticketService.DeleteTicketDocumentAsync(ticketDocumentId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddTicketDocumentList")]
    public async Task<IActionResult> AddTicketDocumentListAsync([FromBody] BaseQuery<TicketDocumentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.AddTicketDocumentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateTicketDocumentList")]
    public async Task<IActionResult> UpdateTicketDocumentListAsync([FromBody] BaseQuery<TicketDocumentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.UpdateTicketDocumentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteTicketDocumentList")]
    public async Task<IActionResult> DeleteTicketDocumentListAsync([FromBody] BaseQuery<TicketDocumentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await ticketService.DeleteTicketDocumentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportTicketDocumentList")]
    public async Task<IActionResult> ExportTicketDocumentExcel([FromBody] BaseQuery<TicketDocumentDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await ticketService.ExportTicketDocumentExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportTicketDocumentList")]
    public async Task<IActionResult> ImportTicketDocumentExcel(B64File file, CancellationToken cancellationToken = default)
    {
        var result = await ticketService.ImportTicketDocumentExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion
}
