﻿using System.Security.Claims;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;

public interface IJwtTokenService
{
    EntityResponse<UserJwtDto>
        RenewJwtToken(Guid userId, string refreshToken); // should only be called AFTER Authorize() fails!

    string GenerateToken(Guid userId, string userName, Guid? tenantId = null);
    string GenerateRefreshToken(Guid userId);
    bool ValidateRefreshToken(Guid userId, string refreshToken);
    public void InvalidateRefreshToken(Guid userId);
}