using System.Runtime.CompilerServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests;
using Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.Api.Controllers;

public class RoleController(IRoleService roleService) : BaseController<RoleController>
{
    public IRoleService roleService = roleService;

    #region Role

    [HttpPost("RoleList")]
    public async Task<IActionResult> QueryRoleListAsync([FromBody] BaseQuery<RoleDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<RoleListItemDto> result = await roleService.QueryRoleAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("RoleDetails")]
    public async Task<IActionResult> GetRoleDetails(Guid roleId, CancellationToken cancellationToken = default)
    {
        EntityResponse<RoleDto> result = await roleService.GetRoleByIdAsync(roleId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddRole")]
    public async Task<IActionResult> AddRoleAsync(RoleDto model, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.AddRoleAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateRole")]
    public async Task<IActionResult> UpdateRoleAsync(RoleDto model, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.UpdateRoleAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteRole")]
    public async Task<IActionResult> DeleteRoleAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.DeleteRoleAsync(roleId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddRoleList")]
    public async Task<IActionResult> AddRoleList([FromBody] BaseQuery<RoleDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.AddRolesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateRoleList")]
    public async Task<IActionResult> UpdateRoleList([FromBody] BaseQuery<RoleDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.UpdateRolesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteRoleList")]
    public async Task<IActionResult> DeleteRoleList([FromBody] BaseQuery<RoleDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.DeleteRolesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportRoleList")]
    public async Task<IActionResult> ExportRoleExcel(BaseQuery request, CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await roleService.ExportRoleExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportRoleList")]
    public async Task<IActionResult> ImportRoleExcel(B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.ImportRoleExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region Role Access

    [HttpPost("RoleAccessList")]
    public async Task<IActionResult> QueryRoleAccessListAsync([FromBody] BaseQuery<RoleAccessDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<RoleAccessListItemDto> result =
            await roleService.QueryRoleAccessAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("RoleAccessDetails")]
    public async Task<IActionResult> GetRoleAccessDetails(Guid roleAccessId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<RoleAccessDto>
            result = await roleService.GetRoleAccessByIdAsync(roleAccessId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddRoleAccess")]
    public async Task<IActionResult> AddRoleAccessAsync(RoleAccessDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.AddRoleAccessAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateRoleAccess")]
    public async Task<IActionResult> UpdateRoleAccessAsync(RoleAccessDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.UpdateRoleAccessAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteRoleAccess")]
    public async Task<IActionResult> DeleteRoleAccessAsync(Guid roleAccessId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.DeleteRoleAccessAsync(roleAccessId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddRoleAccessList")]
    public async Task<IActionResult> AddRoleAccessList([FromBody] BaseQuery<RoleAccessDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.AddRoleAccessesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateRoleAccessList")]
    public async Task<IActionResult> UpdateRoleAccessList([FromBody] BaseQuery<RoleAccessDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.UpdateRoleAccessesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteRoleAccessList")]
    public async Task<IActionResult> DeleteRoleAccessList([FromBody] BaseQuery<RoleAccessDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.DeleteRoleAccessesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportRoleAccessList")]
    public async Task<IActionResult> ExportRoleAccessExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await roleService.ExportRoleAccessExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportRoleAccessList")]
    public async Task<IActionResult> ImportRoleAccessExcel(B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.ImportRoleAccessExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region Assignable Role

    [HttpPost("AssignableRoleList")]
    public async Task<IActionResult> QueryAssignableRoleListAsync([FromBody] BaseQuery<AssignableRoleDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<AssignableRoleListItemDto> result =
            await roleService.QueryAssignableRoleAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("AssignableRoleDetails")]
    public async Task<IActionResult> GetAssignableRoleDetails(Guid assignableRoleId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<AssignableRoleDto> result =
            await roleService.GetAssignableRoleByIdAsync(assignableRoleId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddAssignableRole")]
    public async Task<IActionResult> AddAssignableRoleAsync(AssignableRoleDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.AddAssignableRoleAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateAssignableRole")]
    public async Task<IActionResult> UpdateAssignableRoleAsync(AssignableRoleDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.UpdateAssignableRoleAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteAssignableRole")]
    public async Task<IActionResult> DeleteAssignableRoleAsync(Guid assignableRoleId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.DeleteAssignableRoleAsync(assignableRoleId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddAssignableRoleList")]
    public async Task<IActionResult> AddAssignableRoleList([FromBody] BaseQuery<AssignableRoleDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.AddAssignableRolesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateAssignableRoleList")]
    public async Task<IActionResult> UpdateAssignableRoleList([FromBody] BaseQuery<AssignableRoleDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.UpdateAssignableRolesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteAssignableRoleList")]
    public async Task<IActionResult> DeleteAssignableRoleList([FromBody] BaseQuery<AssignableRoleDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.DeleteAssignableRolesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportAssignableRoleList")]
    public async Task<IActionResult> ExportAssignableRoleExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await roleService.ExportAssignableRoleExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportAssignableRoleList")]
    public async Task<IActionResult> ImportAssignableRoleExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.ImportAssignableRoleExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region Role Assignment

    [HttpPost("RoleAssignmentList")]
    public async Task<IActionResult> QueryRoleAssignmentListAsync([FromBody] BaseQuery<RoleAssignmentDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<RoleAssignmentListItemDto> result =
            await roleService.QueryRoleAssignmentAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("RoleAssignmentDetails")]
    public async Task<IActionResult> GetRoleAssignmentDetails(Guid roleAssignmentId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<RoleAssignmentDto> result =
            await roleService.GetRoleAssignmentByIdAsync(roleAssignmentId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddRoleAssignment")]
    public async Task<IActionResult> AddRoleAssignmentAsync(RoleAssignmentDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.AddRoleAssignmentAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateRoleAssignment")]
    public async Task<IActionResult> UpdateRoleAssignmentAsync(RoleAssignmentDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.UpdateRoleAssignmentAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteRoleAssignment")]
    public async Task<IActionResult> DeleteRoleAssignmentAsync(Guid roleAssignmentId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.DeleteRoleAssignmentAsync(roleAssignmentId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddRoleAssignmentList")]
    public async Task<IActionResult> AddRoleAssignmentList([FromBody] BaseQuery<RoleAssignmentDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.AddRoleAssignmentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateRoleAssignmentList")]
    public async Task<IActionResult> UpdateRoleAssignmentList([FromBody] BaseQuery<RoleAssignmentDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.UpdateRoleAssignmentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteRoleAssignmentList")]
    public async Task<IActionResult> DeleteRoleAssignmentList([FromBody] BaseQuery<RoleAssignmentDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.DeleteRoleAssignmentsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportRoleAssignmentList")]
    public async Task<IActionResult> ExportRoleAssignmentExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await roleService.ExportRoleAssignmentExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportRoleAssignmentList")]
    public async Task<IActionResult> ImportRoleAssignmentExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.ImportRoleAssignmentExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region Access Resource

    [HttpPost("AccessResourceList")]
    public async Task<IActionResult> QueryAccessResourceListAsync([FromBody] BaseQuery<AccessResourceDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<AccessResourceListItemDto> result =
            await roleService.QueryAccessResourceAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("AccessResourceDetails")]
    public async Task<IActionResult> GetAccessResourceDetails(Guid accessResourceId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<AccessResourceDto> result =
            await roleService.GetAccessResourceByIdAsync(accessResourceId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddAccessResource")]
    public async Task<IActionResult> AddAccessResourceAsync(AccessResourceDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.AddAccessResourceAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateAccessResource")]
    public async Task<IActionResult> UpdateAccessResourceAsync(AccessResourceDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.UpdateAccessResourceAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteAccessResource")]
    public async Task<IActionResult> DeleteAccessResourceAsync(Guid accessResourceId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await roleService.DeleteAccessResourceAsync(accessResourceId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddAccessResourceList")]
    public async Task<IActionResult> AddAccessResourceList([FromBody] BaseQuery<AccessResourceDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.AddAccessResourcesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateAccessResourceList")]
    public async Task<IActionResult> UpdateAccessResourceList([FromBody] BaseQuery<AccessResourceDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.UpdateAccessResourcesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteAccessResourceList")]
    public async Task<IActionResult> DeleteAccessResourceList([FromBody] BaseQuery<AccessResourceDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.DeleteAccessResourcesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportAccessResourceList")]
    public async Task<IActionResult> ExportAccessResourceExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File> result = await roleService.ExportAccessResourceExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportAccessResourceList")]
    public async Task<IActionResult> ImportAccessResourceExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        EntityResponse result = await roleService.ImportAccessResourceExcel(file, cancellationToken);
        return Ok(result);
    }

    [Authorize]
    [HttpPost("MatchAccessResource")]
    public async Task<IActionResult> MatchAccessResourceAsync(StringListRequest request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<List<bool>> result = await Task.Run(() => roleService.MatchAccessResourceAsync(request, cancellationToken));
        // EntityResponse<List<bool>> result = roleService.MatchAccessResourceAsync(request, cancellationToken);
        return Ok(result);
    }

    #endregion
}