﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;

public partial class CodeItemListItemDto : TenantBaseDto
{
    public Guid CodeTypeId { get; set; }

    public string Name { get; set; } = null!;

    public string Value { get; set; } = null!;

    public string? SeqNo { get; set; }

    public Guid? SuperiorId { get; set; }

    public string? ItemField1 { get; set; }

    public string? ItemField2 { get; set; }

    public string? ItemField3 { get; set; }

}
