﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests;
using Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.Api.Controllers;

public class AccountController(IAccountService accountService) : BaseController<AccountController>
{
    [AllowAnonymous]
    [HttpPost("Login")]
    public async Task<IActionResult> Login([FromBody] UserLoginRequest request,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid) return Forbid("Model invalid");

        EntityResponse<UserJwtDto> result = await accountService.AuthenticateAsync(request, cancellationToken);

        return Ok(result);
    }

    [AllowAnonymous]
    [HttpPost("Refresh")]
    public IActionResult RefreshToken([FromBody] UserJwtDto request)
    {
        EntityResponse<UserJwtDto> result = accountService.RefreshToken(request.UserId, request.RefreshToken);
        return Ok(result);
    }

    [Authorize]
    [HttpGet("Logout")]
    public IActionResult Logout(Guid userId)
    {
        EntityResponse result = accountService.Deauthenticate(userId);
        return Ok(result);
    }

    [Authorize]
    [HttpGet("UserTenantList")]
    public async Task<IActionResult> GetUser()
    {
        EntityResponse<List<TenantDto>> result = await accountService.GetUserTenants();
        return Ok(result);
    }

    [Authorize]
    [HttpPost("SetTenant")]
    public async Task<IActionResult> SetTenant(GuidRequest tenantId, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await accountService.SetTenant(tenantId, cancellationToken);
        return Ok(result);
    }
}