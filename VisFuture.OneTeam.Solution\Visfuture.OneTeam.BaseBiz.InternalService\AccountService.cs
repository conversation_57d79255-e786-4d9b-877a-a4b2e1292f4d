﻿using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Interfaces;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests;
using Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.InternalService;

public class AccountService(IAccountManager accountManager) : IAccountService
{
    public Task<EntityResponse<UserJwtDto>> AuthenticateAsync(UserLoginRequest request,
        CancellationToken cancellationToken = default)
    {
        return accountManager.AuthenticateAsync(request, cancellationToken);
    }

    public Task<EntityResponse<List<TenantDto>>> GetUserTenants()
    {
        return accountManager.GetUserTenants();
    }

    public EntityResponse<UserJwtDto> RefreshToken(Guid userId, string tokenStr)
    {
        return accountManager.RefreshToken(userId, tokenStr);
    }

    public EntityResponse Deauthenticate(Guid userId)
    {
        return accountManager.Deauthenticate(userId);
    }

    public Task<EntityResponse> SetTenant(GuidRequest tenantId, CancellationToken cancellationToken = default)
    {
        return accountManager.SetTenant(tenantId, cancellationToken);
    }
}