﻿using Visfuture.OneTeam.BaseBiz.DataAccess.Entities;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;

public partial class I18nKeyDto : TenantBaseDto
{
    public string KeyCode { get; set; } = null!;

    public string KeyType { get; set; } = null!;

    public string? DefaultValue { get; set; }

    public virtual ICollection<I18nTranslation> I18NTranslations { get; set; } = [];
}
