using Microsoft.AspNetCore.Mvc;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.Api.Controllers;

[ApiController]
[Route("[controller]")]
public class ExternalController(IMiscService miscService) : BaseController<MiscController>
{
    [HttpGet("SequenceNoByName")]
    public async Task<IActionResult> GetSequenceNoByNameExt(string name, CancellationToken cancellationToken = default)
    {
        EntityResponse<SequenceNoDto> result = await miscService.GetSequenceNoByNameAsync(name, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateSequenceNo")]
    public async Task<IActionResult> UpdateSequenceNoExt([FromBody] SequenceNoDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await miscService.UpdateSequenceNoAsync(model, cancellationToken);
        return Ok(result);
    }
}