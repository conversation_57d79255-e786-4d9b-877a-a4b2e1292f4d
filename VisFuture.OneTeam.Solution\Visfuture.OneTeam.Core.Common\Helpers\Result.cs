﻿namespace Visfuture.OneTeam.Core.Common.Helpers;

public class Result
{
    public Result(bool isSuccess, Error error)
    {
        if (isSuccess && error != Error.None ||
            !isSuccess && error == Error.None)
        {
            throw new ArgumentException("Invalid error", nameof(error));
        }

        IsSuccess = isSuccess;
        Error = error;
    }

    public bool IsSuccess { get; }
    public bool IsFailure => !IsSuccess;
    public Error Error { get; }

    public static Result Failure(Error error) => new(false, error);
    public static Result Success() => new(true, Error.None);
}

// Handle Generic Result
public class Result<T> : Result where T : class
{
    private Result(bool isSuccess, Error error, T data)
    : base(isSuccess, error)
    {
        Data = data;
    }

    public T Data { get; }

    public static new Result Failure(Error error) => new(false, error); // Call base class's Failure
    public static new Result Success() => new(true, Error.None); // call base class's Success
    public static Result<T> Success(T data) => new(true, Error.None, data); // Call current class's Success

    // Optional: override ToString() for easier debugging
    public override string ToString() => IsSuccess ? $"Success: {Data}" : $"Failure: {Error}";
}

public sealed record Error(string Code, string Description)
{
    public static readonly Error None = new(string.Empty, string.Empty);
}
