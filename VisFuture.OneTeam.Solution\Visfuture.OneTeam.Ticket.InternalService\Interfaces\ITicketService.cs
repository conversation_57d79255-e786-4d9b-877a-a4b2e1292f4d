using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Ticket.BusinessLogic.DTOs;
using Visfuture.OneTeam.Ticket.BusinessLogic.ListItemDtos;

namespace Visfuture.OneTeam.Ticket.InternalService.interfaces;

public interface ITicketService
{
    #region Ticket
    Task<EntityResponsePaged<TicketListItemDto>> QueryTicketAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<TicketDto>> GetTicketByIdAsync(Guid ticketId, CancellationToken cancellationToken = default);

    Task<EntityResponse<TicketDto>> GetTicketByNoAsync(string ticketNo, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddTicketAsync(TicketDto ticketDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTicketFromEmailScannerAsync(EmailMessageDto emailMessage, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateTicketAsync(TicketDto ticketDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteTicketAsync(Guid ticketId, CancellationToken cancellationToken = default);

    Task<EntityResponse> AddTicketsAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse> UpdateTicketsAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse> DeleteTicketsAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportTicketExcel(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse> ImportTicketExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketReview
    Task<EntityResponsePaged<TicketReviewListItemDto>> QueryTicketReviewAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<TicketReviewDto>> GetTicketReviewByIdAsync(Guid ticketReviewId, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddTicketReviewAsync(TicketReviewDto ticketReviewDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateTicketReviewAsync(TicketReviewDto ticketReviewDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteTicketReviewAsync(Guid ticketReviewId, CancellationToken cancellationToken = default);

    Task<EntityResponse> AddTicketReviewsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketReviewsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketReviewsAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportTicketReviewExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketReviewExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketLink
    Task<EntityResponsePaged<TicketLinkListItemDto>> QueryTicketLinkAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<TicketLinkDto>> GetTicketLinkByIdAsync(Guid ticketLinkId, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddTicketLinkAsync(TicketLinkDto ticketLinkDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateTicketLinkAsync(TicketLinkDto ticketLinkDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteTicketLinkAsync(Guid ticketLinkId, CancellationToken cancellationToken = default);

    Task<EntityResponse> AddTicketLinksAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketLinksAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketLinksAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportTicketLinkExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketLinkExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketDevOpsLink
    Task<EntityResponsePaged<TicketDevOpsLinkListItemDto>> QueryTicketDevOpsLinkAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<TicketDevOpsLinkDto>> GetTicketDevOpsLinkByIdAsync(Guid ticketDevOpsLinkId, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddTicketDevOpsLinkAsync(TicketDevOpsLinkDto ticketDevOpsLinkDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateTicketDevOpsLinkAsync(TicketDevOpsLinkDto ticketDevOpsLinkDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteTicketDevOpsLinkAsync(Guid ticketDevOpsLinkId, CancellationToken cancellationToken = default);

    Task<EntityResponse> AddTicketDevOpsLinksAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketDevOpsLinksAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketDevOpsLinksAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportTicketDevOpsLinkExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketDevOpsLinkExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketBillingPayment
    Task<EntityResponsePaged<TicketBillingPaymentListItemDto>> QueryTicketBillingPaymentAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<TicketBillingPaymentDto>> GetTicketBillingPaymentByIdAsync(Guid ticketBillingPaymentId, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddTicketBillingPaymentAsync(TicketBillingPaymentDto ticketBillingPaymentDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateTicketBillingPaymentAsync(TicketBillingPaymentDto ticketBillingPaymentDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteTicketBillingPaymentAsync(Guid ticketBillingPaymentId, CancellationToken cancellationToken = default);

    Task<EntityResponse> AddTicketBillingPaymentsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketBillingPaymentsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketBillingPaymentsAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportTicketBillingPaymentExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketBillingPaymentExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketBillingDelivery
    Task<EntityResponsePaged<TicketBillingDeliveryListItemDto>> QueryTicketBillingDeliveryAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<TicketBillingDeliveryDto>> GetTicketBillingDeliveryByIdAsync(Guid ticketBillingDeliveryId, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddTicketBillingDeliveryAsync(TicketBillingDeliveryDto ticketBillingDeliveryDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateTicketBillingDeliveryAsync(TicketBillingDeliveryDto ticketBillingDeliveryDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteTicketBillingDeliveryAsync(Guid ticketBillingDeliveryId, CancellationToken cancellationToken = default);

    Task<EntityResponse> AddTicketBillingDeliveriesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketBillingDeliveriesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketBillingDeliveriesAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportTicketBillingDeliveryExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketBillingDeliveryExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketBilling
    Task<EntityResponsePaged<TicketBillingListItemDto>> QueryTicketBillingAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<TicketBillingDto>> GetTicketBillingByIdAsync(Guid ticketBillingId, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddTicketBillingAsync(TicketBillingDto ticketBillingDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateTicketBillingAsync(TicketBillingDto ticketBillingDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteTicketBillingAsync(Guid ticketBillingId, CancellationToken cancellationToken = default);

    Task<EntityResponse> AddTicketBillingsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketBillingsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketBillingsAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportTicketBillingExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketBillingExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region Ticket Discussion
    Task<EntityResponsePaged<TicketDiscussionListItemDto>> QueryTicketDiscussionAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<TicketDiscussionDto>> GetTicketDiscussionByIdAsync(Guid ticketDiscussionId, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTicketDiscussionAsync(TicketDiscussionDto ticketDiscussionDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateTicketDiscussionAsync(TicketDiscussionDto ticketDiscussionDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteTicketDiscussionAsync(Guid ticketDiscussionId, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddTicketDiscussionsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketDiscussionsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketDiscussionsAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportTicketDiscussionExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketDiscussionExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion

    #region TicketDocument
    Task<EntityResponsePaged<TicketDocumentListItemDto>> QueryTicketDocumentAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<TicketDocumentDto>> GetTicketDocumentByIdAsync(Guid ticketDocumentId, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTicketDocumentAsync(TicketDocumentDto ticketDocumentDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateTicketDocumentAsync(TicketDocumentDto ticketDocumentDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteTicketDocumentAsync(Guid ticketDocumentId, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddTicketDocumentsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTicketDocumentsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTicketDocumentsAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportTicketDocumentExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTicketDocumentExcel(B64File file, CancellationToken cancellationToken = default);
    #endregion
}
