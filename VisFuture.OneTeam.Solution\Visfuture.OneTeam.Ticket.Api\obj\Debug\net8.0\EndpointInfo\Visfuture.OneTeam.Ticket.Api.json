{"openapi": "3.0.1", "info": {"title": "Visfuture.OneTeam.Ticket.Api", "version": "1.0"}, "paths": {"/InvoiceList": {"post": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/InvoiceDetail": {"get": {"tags": ["Invoice"], "parameters": [{"name": "invoiceId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/InvoiceDetailByNo": {"get": {"tags": ["Invoice"], "parameters": [{"name": "invoiceNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/AddInvoice": {"put": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateInvoice": {"put": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteInvoice": {"delete": {"tags": ["Invoice"], "parameters": [{"name": "invoiceId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddInvoiceList": {"post": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateInvoiceList": {"put": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteInvoiceList": {"delete": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportInvoiceList": {"post": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportInvoiceList": {"post": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/InvoiceItemList": {"post": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/InvoiceItemDetail": {"get": {"tags": ["Invoice"], "parameters": [{"name": "invoiceItemId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/DeleteInvoiceItem": {"delete": {"tags": ["Invoice"], "parameters": [{"name": "invoiceItemId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddInvoiceItem": {"put": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateInvoiceItem": {"put": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddInvoiceItemList": {"post": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateInvoiceItemList": {"put": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteInvoiceItemList": {"delete": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportInvoiceItemList": {"post": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportInvoiceItemList": {"post": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProductList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProductDetail": {"get": {"tags": ["Product"], "parameters": [{"name": "productId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddProduct": {"put": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProduct": {"put": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteProduct": {"delete": {"tags": ["Product"], "parameters": [{"name": "productId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddProductList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProductList": {"put": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteProductList": {"delete": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportProductList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportProductList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProductReleaseTrackingList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProductReleaseTrackingDetail": {"get": {"tags": ["Product"], "parameters": [{"name": "productReleaseTrackingId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddProductReleaseTracking": {"put": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddProductReleaseTrackingList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProductReleaseTracking": {"put": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProductReleaseTrackingList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteProductReleaseTracking": {"delete": {"tags": ["Product"], "parameters": [{"name": "productReleaseTrackingId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/DeleteProductReleaseTrackingList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductReleaseTrackingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProductLicenseTrackingList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProductLicenseTrackingDetail": {"get": {"tags": ["Product"], "parameters": [{"name": "productLicenseTrackingId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddProductLicenseTracking": {"put": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddProductLicenseTrackingList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProductLicenseTracking": {"put": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProductLicenseTrackingList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteProductLicenseTracking": {"delete": {"tags": ["Product"], "parameters": [{"name": "productLicenseTrackingId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/DeleteProductLicenseTrackingList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductLicenseTrackingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProductDeploymentTrackingList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProductDeploymentTrackingDetail": {"get": {"tags": ["Product"], "parameters": [{"name": "productDeploymentTrackingId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddProductDeploymentTracking": {"put": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddProductDeploymentTrackingList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProductDeploymentTracking": {"put": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProductDeploymentTrackingList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteProductDeploymentTracking": {"delete": {"tags": ["Product"], "parameters": [{"name": "productDeploymentTrackingId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/DeleteProductDeploymentTrackingList": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDeploymentTrackingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketDetail": {"get": {"tags": ["Ticket"], "parameters": [{"name": "ticketId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/TicketDetailByNo": {"get": {"tags": ["Ticket"], "parameters": [{"name": "ticketNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicket": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddTicketFromEmailScanner": {"post": {"tags": ["Ticket"], "responses": {"200": {"description": "OK"}}}}, "/UpdateTicket": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicket": {"delete": {"tags": ["Ticket"], "parameters": [{"name": "ticketId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketList": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketList": {"delete": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportTicketList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportTicketList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketReviewList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketReviewDetail": {"get": {"tags": ["Ticket"], "parameters": [{"name": "ticketReviewId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketReview": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketReviewDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketReview": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketReviewDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketReview": {"delete": {"tags": ["Ticket"], "parameters": [{"name": "ticketReviewId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketReviewList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketReviewList": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketReviewList": {"delete": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportTicketReviewList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketReviewDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportTicketReviewList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketLinkList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketLinkDetail": {"get": {"tags": ["Ticket"], "parameters": [{"name": "ticketLinkId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketLink": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketLinkDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketLink": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketLinkDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketLink": {"delete": {"tags": ["Ticket"], "parameters": [{"name": "ticketLinkId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketLinkList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketLinkList": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketLinkList": {"delete": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportTicketLinkList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketLinkDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportTicketLinkList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketDevOpsLinkList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketDevOpsLinkDetail": {"get": {"tags": ["Ticket"], "parameters": [{"name": "ticketDevOpsLinkId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketDevOpsLink": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketDevOpsLink": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketDevOpsLink": {"delete": {"tags": ["Ticket"], "parameters": [{"name": "ticketDevOpsLinkId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketDevOpsLinkList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketDevOpsLinkList": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketDevOpsLinkList": {"delete": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportTicketDevOpsLinkList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDevOpsLinkDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportTicketDevOpsLinkList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketBillingPaymentList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketBillingPaymentDetail": {"get": {"tags": ["Ticket"], "parameters": [{"name": "ticketBillingPaymentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketBillingPayment": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketBillingPayment": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketBillingPayment": {"delete": {"tags": ["Ticket"], "parameters": [{"name": "ticketBillingPaymentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketBillingPaymentList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketBillingPaymentList": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketBillingPaymentList": {"delete": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportTicketBillingPaymentList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingPaymentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportTicketBillingPaymentList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketBillingDeliveryList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketBillingDeliveryDetail": {"get": {"tags": ["Ticket"], "parameters": [{"name": "ticketBillingDeliveryId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketBillingDelivery": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketBillingDelivery": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketBillingDelivery": {"delete": {"tags": ["Ticket"], "parameters": [{"name": "ticketBillingDeliveryId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketBillingDeliveryList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketBillingDeliveryList": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketBillingDeliveryList": {"delete": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportTicketBillingDeliveryList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDeliveryDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportTicketBillingDeliveryList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketBillingList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketBillingDetail": {"get": {"tags": ["Ticket"], "parameters": [{"name": "ticketBillingId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketBilling": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketBilling": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketBilling": {"delete": {"tags": ["Ticket"], "parameters": [{"name": "ticketBillingId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketBillingList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketBillingList": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketBillingList": {"delete": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportTicketBillingList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketBillingDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportTicketBillingList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketDiscussionList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketDiscussionDetail": {"get": {"tags": ["Ticket"], "parameters": [{"name": "ticketDiscussionId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketDiscussion": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketDiscussion": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketDiscussion": {"delete": {"tags": ["Ticket"], "parameters": [{"name": "ticketDiscussionId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketDiscussionList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketDiscussionList": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketDiscussionList": {"delete": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportTicketDiscussionList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDiscussionDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportTicketDiscussionList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketDocumentList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TicketDocumentDetail": {"get": {"tags": ["Ticket"], "parameters": [{"name": "ticketDocumentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketDocument": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketDocument": {"put": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketDocument": {"delete": {"tags": ["Ticket"], "parameters": [{"name": "ticketDocumentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTicketDocumentList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTicketDocumentList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTicketDocumentList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportTicketDocumentList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TicketDocumentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportTicketDocumentList": {"post": {"tags": ["Ticket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"B64File": {"type": "object", "properties": {"mimeType": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "data": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BaseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Invoice": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "invoiceDate": {"type": "string", "format": "date-time"}, "status": {"type": "string", "nullable": true}, "creditFlag": {"type": "boolean"}, "creditInvoiceId": {"type": "string", "format": "uuid", "nullable": true}, "creditDate": {"type": "string", "format": "date-time", "nullable": true}, "creditReason": {"type": "string", "nullable": true}, "clientId": {"type": "string", "format": "uuid"}, "clientName": {"type": "string", "nullable": true}, "clientAddress1": {"type": "string", "nullable": true}, "clientAddress2": {"type": "string", "nullable": true}, "clientCity": {"type": "string", "nullable": true}, "clientProvince": {"type": "string", "nullable": true}, "clientPostalCode": {"type": "string", "nullable": true}, "terms": {"type": "string", "nullable": true}, "poNumber": {"type": "string", "nullable": true}, "csr": {"type": "string", "nullable": true}, "currency": {"type": "string", "nullable": true}, "subTotal": {"type": "number", "format": "double"}, "discountRate": {"type": "number", "format": "double"}, "discountAmount": {"type": "number", "format": "double"}, "taxRate": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "totalAmount": {"type": "number", "format": "double"}, "invoiceItems": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceItem"}, "nullable": true}}, "additionalProperties": false}, "InvoiceDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "invoiceDate": {"type": "string", "format": "date-time"}, "status": {"type": "string", "nullable": true}, "creditFlag": {"type": "boolean"}, "creditInvoiceId": {"type": "string", "format": "uuid", "nullable": true}, "creditDate": {"type": "string", "format": "date-time", "nullable": true}, "creditReason": {"type": "string", "nullable": true}, "clientId": {"type": "string", "format": "uuid"}, "clientName": {"type": "string", "nullable": true}, "clientAddress1": {"type": "string", "nullable": true}, "clientAddress2": {"type": "string", "nullable": true}, "clientCity": {"type": "string", "nullable": true}, "clientProvince": {"type": "string", "nullable": true}, "clientPostalCode": {"type": "string", "nullable": true}, "terms": {"type": "string", "nullable": true}, "poNumber": {"type": "string", "nullable": true}, "csr": {"type": "string", "nullable": true}, "currency": {"type": "string", "nullable": true}, "subTotal": {"type": "number", "format": "double"}, "discountRate": {"type": "number", "format": "double"}, "discountAmount": {"type": "number", "format": "double"}, "taxRate": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "totalAmount": {"type": "number", "format": "double"}, "invoiceItems": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceItem"}, "nullable": true}}, "additionalProperties": false}, "InvoiceDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "InvoiceItem": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "invoiceId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "unit": {"type": "number", "format": "double"}, "rate": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "invoice": {"$ref": "#/components/schemas/Invoice"}}, "additionalProperties": false}, "InvoiceItemDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "invoiceId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "unit": {"type": "number", "format": "double"}, "rate": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}}, "additionalProperties": false}, "InvoiceItemDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceItemDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "PageSort": {"required": ["sortDirection", "sortField"], "type": "object", "properties": {"sortField": {"minLength": 1, "type": "string"}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}}, "additionalProperties": false}, "Product": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "productType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "productManager": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "productReleaseTrackings": {"type": "array", "items": {"$ref": "#/components/schemas/ProductReleaseTracking"}, "nullable": true}, "productLicenseTrackings": {"type": "array", "items": {"$ref": "#/components/schemas/ProductLicenseTracking"}, "nullable": true}}, "additionalProperties": false}, "ProductDeploymentTracking": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "productReleaseId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "clientId": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "siteId": {"type": "string", "nullable": true}, "siteName": {"type": "string", "nullable": true}, "dateDeployed": {"type": "string", "format": "date-time", "nullable": true}, "targetEnvironment": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "productReleaseTracking": {"$ref": "#/components/schemas/ProductReleaseTracking"}}, "additionalProperties": false}, "ProductDeploymentTrackingDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "productReleaseId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "clientId": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "siteId": {"type": "string", "nullable": true}, "siteName": {"type": "string", "nullable": true}, "dateDeployed": {"type": "string", "format": "date-time", "nullable": true}, "targetEnvironment": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProductDeploymentTrackingDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDeploymentTrackingDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ProductDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "productType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "productManager": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "productReleaseTrackings": {"type": "array", "items": {"$ref": "#/components/schemas/ProductReleaseTracking"}, "nullable": true}, "productLicenseTrackings": {"type": "array", "items": {"$ref": "#/components/schemas/ProductLicenseTracking"}, "nullable": true}}, "additionalProperties": false}, "ProductDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ProductLicenseTracking": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "clientId": {"type": "string", "nullable": true}, "materialManager": {"type": "integer", "format": "int32", "nullable": true}, "materialManagerViewOnly": {"type": "integer", "format": "int32", "nullable": true}, "pollManager": {"type": "integer", "format": "int32", "nullable": true}, "scaleManager": {"type": "integer", "format": "int32", "nullable": true}, "touchScreen": {"type": "integer", "format": "int32", "nullable": true}, "touchScreenPay": {"type": "integer", "format": "int32", "nullable": true}, "craneStationManager": {"type": "integer", "format": "int32", "nullable": true}, "licenseType": {"type": "string", "nullable": true}, "maxUsers": {"type": "integer", "format": "int32", "nullable": true}, "maxProjects": {"type": "integer", "format": "int32", "nullable": true}, "status": {"type": "string", "nullable": true}, "modulesEnabled": {"type": "string", "nullable": true}, "effectiveDate": {"type": "string", "format": "date-time", "nullable": true}, "expireDate": {"type": "string", "format": "date-time", "nullable": true}, "description": {"type": "string", "nullable": true}, "product": {"$ref": "#/components/schemas/Product"}}, "additionalProperties": false}, "ProductLicenseTrackingDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "clientId": {"type": "string", "nullable": true}, "materialManager": {"type": "integer", "format": "int32", "nullable": true}, "materialManagerViewOnly": {"type": "integer", "format": "int32", "nullable": true}, "pollManager": {"type": "integer", "format": "int32", "nullable": true}, "scaleManager": {"type": "integer", "format": "int32", "nullable": true}, "touchScreen": {"type": "integer", "format": "int32", "nullable": true}, "touchScreenPay": {"type": "integer", "format": "int32", "nullable": true}, "craneStationManager": {"type": "integer", "format": "int32", "nullable": true}, "licenseType": {"type": "string", "nullable": true}, "maxUsers": {"type": "integer", "format": "int32", "nullable": true}, "maxProjects": {"type": "integer", "format": "int32", "nullable": true}, "status": {"type": "string", "nullable": true}, "modulesEnabled": {"type": "string", "nullable": true}, "effectiveDate": {"type": "string", "format": "date-time", "nullable": true}, "expireDate": {"type": "string", "format": "date-time", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProductLicenseTrackingDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/ProductLicenseTrackingDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ProductReleaseTracking": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "versionId": {"type": "string", "nullable": true}, "releaseId": {"type": "string", "nullable": true}, "buildId": {"type": "string", "nullable": true}, "dateSent": {"type": "string", "format": "date-time", "nullable": true}, "dateInstalled": {"type": "string", "format": "date-time", "nullable": true}, "description": {"type": "string", "nullable": true}, "product": {"$ref": "#/components/schemas/Product"}, "productDeploymentTrackings": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDeploymentTracking"}, "nullable": true}}, "additionalProperties": false}, "ProductReleaseTrackingDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "versionId": {"type": "string", "nullable": true}, "releaseId": {"type": "string", "nullable": true}, "buildId": {"type": "string", "nullable": true}, "dateSent": {"type": "string", "format": "date-time", "nullable": true}, "dateInstalled": {"type": "string", "format": "date-time", "nullable": true}, "description": {"type": "string", "nullable": true}, "productDeploymentTrackings": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDeploymentTracking"}, "nullable": true}}, "additionalProperties": false}, "ProductReleaseTrackingDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/ProductReleaseTrackingDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "SortOperator": {"enum": [0, 1], "type": "integer", "format": "int32"}, "TicketBillingDeliveryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "ticketBillingId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "sequence": {"type": "string", "nullable": true}, "estimatedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "TicketBillingDeliveryDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/TicketBillingDeliveryDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TicketBillingDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "ticketId": {"type": "string", "format": "uuid"}, "analysisDone": {"type": "boolean"}, "requirementsDone": {"type": "boolean"}, "specificationsDone": {"type": "boolean"}, "billable": {"type": "boolean"}, "estimateDone": {"type": "boolean"}, "estimateAmount": {"type": "number", "format": "double"}, "sowApproved": {"type": "boolean"}, "sowFileId": {"type": "string", "nullable": true}, "unitType": {"type": "string", "nullable": true}, "rate": {"type": "number", "format": "double"}, "lowUnit": {"type": "number", "format": "double"}, "lowRate": {"type": "number", "format": "double"}, "lowAmount": {"type": "number", "format": "double"}, "highUnit": {"type": "number", "format": "double"}, "highRate": {"type": "number", "format": "double"}, "highAmount": {"type": "number", "format": "double"}, "mostLikelyUnit": {"type": "number", "format": "double"}, "mostLikelyRate": {"type": "number", "format": "double"}, "mostLikelyAmount": {"type": "number", "format": "double"}, "quotedType": {"type": "string", "nullable": true}, "quotedPrice": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "ticketBillingDeliveries": {"type": "array", "items": {"$ref": "#/components/schemas/TicketBillingDeliveryDto"}, "nullable": true}, "ticketBillingPayments": {"type": "array", "items": {"$ref": "#/components/schemas/TicketBillingPaymentDto"}, "nullable": true}}, "additionalProperties": false}, "TicketBillingDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/TicketBillingDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TicketBillingPaymentDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "ticketBillingId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "sequence": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "estimatedDate": {"type": "string", "format": "date-time", "nullable": true}, "currency": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TicketBillingPaymentDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/TicketBillingPaymentDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TicketDevOpsLinkDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "ticketId": {"type": "string", "format": "uuid"}, "devOpsTicketId": {"type": "string", "nullable": true}, "releaseOn": {"type": "string", "format": "date-time", "nullable": true}, "releaseNotes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TicketDevOpsLinkDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/TicketDevOpsLinkDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TicketDiscussionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "ticketId": {"type": "string", "format": "uuid"}, "type": {"type": "string", "nullable": true}, "replyDate": {"type": "string", "format": "date-time"}, "author": {"type": "string", "nullable": true}, "sender": {"type": "string", "nullable": true}, "receiver": {"type": "string", "nullable": true}, "cc": {"type": "string", "nullable": true}, "template": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "comment": {"type": "string", "nullable": true}, "isPublic": {"type": "boolean"}}, "additionalProperties": false}, "TicketDiscussionDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/TicketDiscussionDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TicketDocumentDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "ticketId": {"type": "string", "format": "uuid"}, "ticketCommentId": {"type": "string", "format": "uuid"}, "documentId": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "fileType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TicketDocumentDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/TicketDocumentDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TicketDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "projectId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "mainType": {"type": "string", "nullable": true}, "subType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "priority": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}, "sourceId": {"type": "string", "nullable": true}, "raisedOn": {"type": "string", "format": "date-time"}, "raisedBy": {"type": "string", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "assignee": {"type": "string", "nullable": true}, "customerId": {"type": "string", "format": "uuid", "nullable": true}, "contractId": {"type": "string", "format": "uuid", "nullable": true}, "reviewFlag": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "module": {"type": "string", "nullable": true}, "isPublic": {"type": "boolean"}, "billMethod": {"type": "string", "nullable": true}, "billable": {"type": "boolean"}, "workMinutes": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TicketDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/TicketDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TicketLinkDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "ticketId": {"type": "string", "format": "uuid"}, "linkedId": {"type": "string", "format": "uuid"}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TicketLinkDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/TicketLinkDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TicketReviewDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "ticketId": {"type": "string", "format": "uuid"}, "reason": {"type": "string", "nullable": true}, "addedBy": {"type": "string", "nullable": true}, "reviewedOn": {"type": "string", "format": "date-time", "nullable": true}, "reviewedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TicketReviewDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/TicketReviewDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}