﻿using System.Text;
using Mapster;
using MapsterMapper;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using StackExchange.Redis;
using Visfuture.OneTeam.Project.Api.Exceptions;
using Visfuture.OneTeam.Project.BusinessLogic;
using Visfuture.OneTeam.Project.BusinessLogic.Interfaces;
using Visfuture.OneTeam.Project.BusinessLogic.Mapper;
using Visfuture.OneTeam.Project.DataAccess.DbContext;
using Visfuture.OneTeam.Project.ExternalService;
using Visfuture.OneTeam.Project.InternalService;
using Visfuture.OneTeam.Project.InternalService.Interfaces;

namespace Visfuture.OneTeam.Project.Api.Extensions;

public static class ServiceExtensions
{
    public static void ConfigureDbContext(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<AppDataContext>(options =>
        {
            options.UseSqlServer(configuration.GetConnectionString("sqlConnection"));
        });

        services.AddDbContext<BaseBizAppDataContext>(options =>
        {
            options.UseSqlServer(configuration.GetConnectionString("basebiz-sqlConnection"));
        });
    }

    public static void ConfigureExceptionHandler(this IServiceCollection services)
    {
        services
            .AddProblemDetails()
            .AddExceptionHandler<GlobalExceptionHandler>(); // you can add multiple exception handlers here
    }

    public static void ConfigureRedisCache(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IConnectionMultiplexer>(_ =>
            ConnectionMultiplexer.Connect(configuration.GetConnectionString("Redis")!));
    }

    public static void ConfgiureHttpClient(this IServiceCollection services)
    {
        // modif this to fit your requirements!!!
        services.AddHttpClient<GitHubService>(client =>
        {
            client.BaseAddress = new Uri("https://api.github.com/");
            client.DefaultRequestHeaders.Add("Accept", "application/vnd.github.v3+json");
            client.DefaultRequestHeaders.Add("User-Agent", "HttpClientFactory-Sample");
            client.DefaultRequestHeaders.Add("Authorization", "Bearer <your token>");
        });
    }

    public static void ConfigureCustomServices(this IServiceCollection services)
    {
        // E.g.
        //services.AddScoped<IUserService, UserService>();    // allows DI by interface if declared this way
        //services.AddScoped<IWeatherService, WeatherService>();

        // Add additional services here...
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddHttpClient();

        services.AddScoped<IProjectManager, ProjectManager>();
        services.AddScoped<ICompanyManager, CompanyManager>();

        services.AddScoped<IProjectService, ProjectServiceImpl>();
        services.AddScoped<ICompanyService, CompanyServiceImpl>();

        services.AddScoped<IMyOrderServiceBus, MyOrderServiceBus>();
        services.AddScoped<IMyOrderService, MyOrderServiceImpl>();
        services.AddScoped<IMapper, ServiceMapper>();
    }

    public static void ConfigureJWT(this IServiceCollection services, IConfiguration configuration)
    {
        //var jwtSettings = configuration.GetSection("JwtSettings");
        //var secretKey = Environment.GetEnvironmentVariable("SECRET") ?? "!@#$%^&*()abxgretretyehdghvbnsfserbdfgdf@#$^%$#^%$^@#$@#ewrwer";

        services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = false;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey =
                        true, // can be eliminated if you don't want to validate the issuer signing key
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero,

                    ValidIssuer = configuration["JWT:Issuer"] ?? "",
                    ValidAudience = configuration["JWT:Audience"] ?? "",
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["JWT:Key"] ??
                        "!@#$%^&*()abxgretretyehdghvbnsfserbdfgdf@#$^%$#^%$^@#$@#ewrwer"))
                };

                // Add event handlers for debugging
                options.Events = new JwtBearerEvents
                {
                    OnAuthenticationFailed = context =>
                    {
                        Console.WriteLine($"JWT Authentication failed: {context.Exception.Message}");
                        return Task.CompletedTask;
                    },
                    OnTokenValidated = context =>
                    {
                        Console.WriteLine("JWT Token validated successfully");
                        var claims = context.Principal?.Claims;
                        if (claims != null)
                        {
                            foreach (var claim in claims)
                            {
                                Console.WriteLine($"Claim: {claim.Type} = {claim.Value}");
                            }
                        }
                        return Task.CompletedTask;
                    },
                    OnChallenge = context =>
                    {
                        Console.WriteLine($"JWT Challenge: {context.Error} - {context.ErrorDescription}");
                        return Task.CompletedTask;
                    }
                };
            });
    }

    public static void AddSwaggerExtension(this IServiceCollection services)
    {
        services.AddSwaggerGen(opt =>
        {
            OpenApiSecurityScheme scheme = new()
            {
                Description = "Authorization header.\r\nExample:'Bearer 123456abcdef'",
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Authorization"
                },
                Scheme = "oauth2",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey
            };
            opt.AddSecurityDefinition("Authorization", scheme);
            OpenApiSecurityRequirement requirment = new()
            {
                [scheme] = []
            };
            opt.AddSecurityRequirement(requirment);
        });
    }

    public static void AddMapsterServices(this IServiceCollection services)
    {
        // Register Mapster configuration and mapper
        TypeAdapterConfig config = TypeAdapterConfig.GlobalSettings;
        // Scan for Mapster configurations in this assembly
        config.Scan(typeof(Program).Assembly);
        MapsterConfig.RegisterMappings();

        services.AddSingleton(config);
    }
}