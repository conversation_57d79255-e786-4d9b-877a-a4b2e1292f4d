2025-04-30 09:39:33.602 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-04-30 09:39:33.745 -04:00 [INF] Now listening on: http://localhost:5275
2025-04-30 09:39:33.785 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-04-30 09:39:33.787 -04:00 [INF] Hosting environment: Development
2025-04-30 09:39:33.788 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-04-30 09:40:13.941 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-04-30 09:40:14.142 -04:00 [INF] Now listening on: http://localhost:5275
2025-04-30 09:40:14.189 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-04-30 09:40:14.191 -04:00 [INF] Hosting environment: Development
2025-04-30 09:40:14.192 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-04-30 09:42:26.339 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-04-30 09:42:26.391 -04:00 [INF] CORS policy execution successful.
2025-04-30 09:42:26.398 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 32.5469 ms
2025-04-30 09:42:26.413 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 79.9117ms
2025-04-30 09:42:26.427 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-04-30 09:42:26.433 -04:00 [INF] CORS policy execution successful.
2025-04-30 09:42:28.015 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 09:42:28.036 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 09:42:30.410 -04:00 [INF] Executed DbCommand (99ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 09:42:30.451 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 09:42:30.789 -04:00 [INF] Executed DbCommand (111ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-04-30 09:42:30.828 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 09:42:30.843 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 2802.0848ms
2025-04-30 09:42:30.846 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 09:42:30.849 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 4416.1680 ms
2025-04-30 09:42:30.857 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 4430.6059ms
2025-04-30 09:44:24.685 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - application/json; charset=utf-8 0
2025-04-30 09:44:24.727 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 09:44:24.734 -04:00 [INF] Route matched with {action = "GetSequenceNoByNameExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoByNameExt(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 09:44:24.767 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 09:44:24.793 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 09:44:24.929 -04:00 [INF] Executed DbCommand (83ms) [Parameters=[@__name_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[CreateAt], [s].[CreateBy], [s].[CurrentNo], [s].[Length], [s].[Name], [s].[Prefix], [s].[Suffix], [s].[TenantId], [s].[UpdateAt], [s].[UpdateBy]
FROM [SequenceNo] AS [s]
WHERE [s].[Name] = @__name_0
2025-04-30 09:44:25.023 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 09:44:25.030 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api) in 291.8235ms
2025-04-30 09:44:25.033 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 09:44:25.035 -04:00 [INF] HTTP GET /External/SequenceNoByName responded 200 in 344.0494 ms
2025-04-30 09:44:25.039 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - 200 null application/json; charset=utf-8 354.8361ms
2025-04-30 09:46:13.770 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-04-30 09:46:13.776 -04:00 [INF] CORS policy execution successful.
2025-04-30 09:46:13.778 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.6825 ms
2025-04-30 09:46:13.781 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 36.6983ms
2025-04-30 09:46:13.791 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-04-30 09:46:13.795 -04:00 [INF] CORS policy execution successful.
2025-04-30 09:46:13.798 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 09:46:13.800 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 09:46:13.863 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 09:46:13.888 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 09:46:14.002 -04:00 [INF] Executed DbCommand (108ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-04-30 09:46:14.006 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 09:46:14.009 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 206.6658ms
2025-04-30 09:46:14.014 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 09:46:14.016 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 220.7657 ms
2025-04-30 09:46:14.018 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 227.3774ms
2025-04-30 09:46:18.514 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - application/json; charset=utf-8 0
2025-04-30 09:46:18.520 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 09:46:18.522 -04:00 [INF] Route matched with {action = "GetSequenceNoByNameExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoByNameExt(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 09:46:18.546 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 09:46:18.575 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 09:46:18.606 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__name_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[CreateAt], [s].[CreateBy], [s].[CurrentNo], [s].[Length], [s].[Name], [s].[Prefix], [s].[Suffix], [s].[TenantId], [s].[UpdateAt], [s].[UpdateBy]
FROM [SequenceNo] AS [s]
WHERE [s].[Name] = @__name_0
2025-04-30 09:46:18.662 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 09:46:18.666 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api) in 140.9089ms
2025-04-30 09:46:18.670 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 09:46:18.671 -04:00 [INF] HTTP GET /External/SequenceNoByName responded 200 in 152.8974 ms
2025-04-30 09:46:18.673 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - 200 null application/json; charset=utf-8 160.8003ms
2025-04-30 09:49:10.428 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-04-30 09:49:10.439 -04:00 [INF] CORS policy execution successful.
2025-04-30 09:49:10.441 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.7666 ms
2025-04-30 09:49:10.444 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 17.1796ms
2025-04-30 09:49:10.449 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-04-30 09:49:10.458 -04:00 [INF] CORS policy execution successful.
2025-04-30 09:49:10.460 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 09:49:10.461 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 09:49:10.489 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 09:49:10.516 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 09:49:10.616 -04:00 [INF] Executed DbCommand (94ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-04-30 09:49:10.623 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 09:49:10.626 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 162.5301ms
2025-04-30 09:49:10.628 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 09:49:10.630 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 171.4985 ms
2025-04-30 09:49:10.634 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 184.7158ms
2025-04-30 09:49:14.385 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - application/json; charset=utf-8 0
2025-04-30 09:49:14.394 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 09:49:14.398 -04:00 [INF] Route matched with {action = "GetSequenceNoByNameExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoByNameExt(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 09:49:14.429 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 09:49:14.457 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 09:49:14.486 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__name_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[CreateAt], [s].[CreateBy], [s].[CurrentNo], [s].[Length], [s].[Name], [s].[Prefix], [s].[Suffix], [s].[TenantId], [s].[UpdateAt], [s].[UpdateBy]
FROM [SequenceNo] AS [s]
WHERE [s].[Name] = @__name_0
2025-04-30 09:49:14.551 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 09:49:14.556 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api) in 151.9009ms
2025-04-30 09:49:14.560 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 09:49:14.561 -04:00 [INF] HTTP GET /External/SequenceNoByName responded 200 in 169.6376 ms
2025-04-30 09:49:14.565 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - 200 null application/json; charset=utf-8 179.5189ms
2025-04-30 14:21:55.435 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-04-30 14:21:55.449 -04:00 [INF] CORS policy execution successful.
2025-04-30 14:21:55.452 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 5.3473 ms
2025-04-30 14:21:55.456 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 22.6983ms
2025-04-30 14:21:55.468 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-04-30 14:21:55.474 -04:00 [INF] CORS policy execution successful.
2025-04-30 14:21:55.478 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 14:21:55.482 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 14:21:55.732 -04:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 14:21:55.758 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 14:21:55.809 -04:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-04-30 14:21:55.819 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 14:21:55.823 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 338.0691ms
2025-04-30 14:21:55.825 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 14:21:55.828 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 354.4884 ms
2025-04-30 14:21:55.831 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 363.0807ms
2025-04-30 14:22:00.638 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - application/json; charset=utf-8 0
2025-04-30 14:22:00.650 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 14:22:00.653 -04:00 [INF] Route matched with {action = "GetSequenceNoByNameExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoByNameExt(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 14:22:00.675 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 14:22:00.701 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 14:22:00.732 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[@__name_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[CreateAt], [s].[CreateBy], [s].[CurrentNo], [s].[Length], [s].[Name], [s].[Prefix], [s].[Suffix], [s].[TenantId], [s].[UpdateAt], [s].[UpdateBy]
FROM [SequenceNo] AS [s]
WHERE [s].[Name] = @__name_0
2025-04-30 14:22:00.844 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 14:22:00.850 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api) in 195.2655ms
2025-04-30 14:22:00.856 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 14:22:00.859 -04:00 [INF] HTTP GET /External/SequenceNoByName responded 200 in 212.4726 ms
2025-04-30 14:22:00.865 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - 200 null application/json; charset=utf-8 228.1162ms
2025-04-30 14:22:24.299 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-04-30 14:22:24.306 -04:00 [INF] CORS policy execution successful.
2025-04-30 14:22:24.308 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.9514 ms
2025-04-30 14:22:24.310 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 13.5929ms
2025-04-30 14:22:24.322 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-04-30 14:22:24.325 -04:00 [INF] CORS policy execution successful.
2025-04-30 14:22:24.326 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 14:22:24.329 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 14:22:24.352 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 14:22:24.387 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 14:22:24.485 -04:00 [INF] Executed DbCommand (93ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-04-30 14:22:24.489 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 14:22:24.491 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 161.1236ms
2025-04-30 14:22:24.493 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 14:22:24.495 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 170.1265 ms
2025-04-30 14:22:24.498 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 175.4171ms
2025-04-30 14:22:28.846 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - application/json; charset=utf-8 0
2025-04-30 14:22:28.851 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 14:22:28.853 -04:00 [INF] Route matched with {action = "GetSequenceNoByNameExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoByNameExt(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 14:22:28.875 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 14:22:28.966 -04:00 [INF] Executed DbCommand (86ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 14:22:28.994 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[@__name_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[CreateAt], [s].[CreateBy], [s].[CurrentNo], [s].[Length], [s].[Name], [s].[Prefix], [s].[Suffix], [s].[TenantId], [s].[UpdateAt], [s].[UpdateBy]
FROM [SequenceNo] AS [s]
WHERE [s].[Name] = @__name_0
2025-04-30 14:22:29.051 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 14:22:29.054 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api) in 198.8727ms
2025-04-30 14:22:29.056 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 14:22:29.057 -04:00 [INF] HTTP GET /External/SequenceNoByName responded 200 in 207.7217 ms
2025-04-30 14:22:29.059 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - 200 null application/json; charset=utf-8 213.3024ms
2025-04-30 14:27:27.636 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-04-30 14:27:27.643 -04:00 [INF] CORS policy execution successful.
2025-04-30 14:27:27.645 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.2714 ms
2025-04-30 14:27:27.648 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 12.385ms
2025-04-30 14:27:27.656 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-04-30 14:27:27.665 -04:00 [INF] CORS policy execution successful.
2025-04-30 14:27:27.667 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 14:27:27.669 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 14:27:27.693 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 14:27:27.723 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 14:27:27.815 -04:00 [INF] Executed DbCommand (86ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-04-30 14:27:27.820 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 14:27:27.822 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 151.1016ms
2025-04-30 14:27:27.823 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 14:27:27.825 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 160.2596 ms
2025-04-30 14:27:27.828 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 171.8023ms
2025-04-30 23:41:11.045 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-04-30 23:41:11.073 -04:00 [INF] CORS policy execution successful.
2025-04-30 23:41:11.078 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 9.3264 ms
2025-04-30 23:41:11.084 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 46.1708ms
2025-04-30 23:41:11.100 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-04-30 23:41:11.106 -04:00 [INF] CORS policy execution successful.
2025-04-30 23:41:11.120 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 23:41:11.131 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 23:41:11.409 -04:00 [INF] Executed DbCommand (79ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 23:41:11.444 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 23:41:11.571 -04:00 [INF] Executed DbCommand (93ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-04-30 23:41:11.586 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 23:41:11.594 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 458.9503ms
2025-04-30 23:41:11.598 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 23:41:11.601 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 495.1388 ms
2025-04-30 23:41:11.606 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 506.1026ms
2025-04-30 23:41:15.223 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - application/json; charset=utf-8 0
2025-04-30 23:41:15.259 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 23:41:15.266 -04:00 [INF] Route matched with {action = "GetSequenceNoByNameExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoByNameExt(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 23:41:15.304 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 23:41:15.347 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 23:41:15.391 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__name_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[CreateAt], [s].[CreateBy], [s].[CurrentNo], [s].[Length], [s].[Name], [s].[Prefix], [s].[Suffix], [s].[TenantId], [s].[UpdateAt], [s].[UpdateBy]
FROM [SequenceNo] AS [s]
WHERE [s].[Name] = @__name_0
2025-04-30 23:41:15.499 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 23:41:15.507 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api) in 232.3698ms
2025-04-30 23:41:15.514 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 23:41:15.522 -04:00 [INF] HTTP GET /External/SequenceNoByName responded 200 in 288.1253 ms
2025-04-30 23:41:15.539 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - 200 null application/json; charset=utf-8 316.4279ms
2025-04-30 23:48:18.717 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-04-30 23:48:18.926 -04:00 [INF] Now listening on: http://localhost:5275
2025-04-30 23:48:18.963 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-04-30 23:48:18.965 -04:00 [INF] Hosting environment: Development
2025-04-30 23:48:18.966 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-04-30 23:48:58.376 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-04-30 23:48:58.465 -04:00 [INF] CORS policy execution successful.
2025-04-30 23:48:58.477 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 57.5975 ms
2025-04-30 23:48:58.505 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 137.4331ms
2025-04-30 23:48:58.529 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-04-30 23:48:58.539 -04:00 [INF] CORS policy execution successful.
2025-04-30 23:49:00.433 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 23:49:00.498 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 23:49:04.771 -04:00 [INF] Executed DbCommand (133ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 23:49:04.868 -04:00 [INF] Executed DbCommand (69ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 23:49:05.637 -04:00 [INF] Executed DbCommand (142ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-04-30 23:49:05.701 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 23:49:05.750 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 5238.3545ms
2025-04-30 23:49:05.755 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 23:49:05.763 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 7225.0125 ms
2025-04-30 23:49:05.781 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 7252.5155ms
2025-04-30 23:49:08.837 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - application/json; charset=utf-8 0
2025-04-30 23:49:08.891 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 23:49:08.906 -04:00 [INF] Route matched with {action = "GetSequenceNoByNameExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoByNameExt(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-04-30 23:49:08.957 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-04-30 23:49:08.993 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-04-30 23:49:09.280 -04:00 [INF] Executed DbCommand (150ms) [Parameters=[@__name_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[CreateAt], [s].[CreateBy], [s].[CurrentNo], [s].[Length], [s].[Name], [s].[Prefix], [s].[Suffix], [s].[TenantId], [s].[UpdateAt], [s].[UpdateBy]
FROM [SequenceNo] AS [s]
WHERE [s].[Name] = @__name_0
2025-04-30 23:49:09.372 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-04-30 23:49:09.386 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api) in 473.2742ms
2025-04-30 23:49:09.390 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-04-30 23:49:09.394 -04:00 [INF] HTTP GET /External/SequenceNoByName responded 200 in 546.2915 ms
2025-04-30 23:49:09.401 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - 200 null application/json; charset=utf-8 566.7667ms
