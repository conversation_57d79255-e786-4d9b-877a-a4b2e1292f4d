﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;

public partial class GlobalAdminDto : BaseDto
{
    public string LoginName { get; set; } = null!;

    public string Name { get; set; } = null!;

    public string Password { get; set; } = null!;

    public string? Description { get; set; }

    public string Email { get; set; } = null!;

    public string? Mobile { get; set; }

    public bool MFA { get; set; }

    public bool IsActive { get; set; }

    public string? ImageId { get; set; }
}
