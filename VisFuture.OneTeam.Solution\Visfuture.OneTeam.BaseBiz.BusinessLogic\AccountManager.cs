﻿using Mapster;
using MapsterMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using StackExchange.Redis;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Interfaces;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests;
using Visfuture.OneTeam.BaseBiz.DataAccess;
using Visfuture.OneTeam.BaseBiz.DataAccess.DbContext;
using Visfuture.OneTeam.BaseBiz.DataAccess.Entities;
using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Core.Common.Helpers;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic;

public class AccountManager(
    IHttpContextAccessor httpContextAccessor,
    IJwtTokenManager tokenService,
    IConnectionMultiplexer redis,
    IHostEnvironment env,
    AppDataContext appDataContext,
    IMapper mapper) : BaseBizBaseManager(httpContextAccessor, appDataContext, mapper), IAccountManager
{
    public async Task<EntityResponse<UserJwtDto>> AuthenticateAsync(UserLoginRequest request, // login to oneteam
        CancellationToken cancellationToken = default)
    {
        try
        {
            // get user object from db
            UserAccount? userAccount = await DB.UserAccounts.AsNoTracking().FirstOrDefaultAsync(
                account => account.Name.Equals(request.Username), cancellationToken);

            if (userAccount == null)
            {
                if (env.IsDevelopment())
                    return EntityResponse<UserJwtDto>.Failed(
                        "Email address and/or username do not match. Please try again.");

                return EntityResponse<UserJwtDto>.Failed();
            }

            // implement hash & salt (salt is user id)
            bool isPasswordMatch = userAccount.PasswordHash.Equals(HashUtils.Hash(request.Password + userAccount.Id));
            if (!isPasswordMatch)
            {
                if (env.IsDevelopment())
                    return EntityResponse<UserJwtDto>.Failed(
                        "Email address and/or password do not match. Please try again.");

                return EntityResponse<UserJwtDto>.Failed();
            }

            if (!userAccount.IsActive)
                return EntityResponse<UserJwtDto>.Failed("Account has been deactivated!");

            Guid? tenantId = userAccount.Employees.FirstOrDefault()?.TenantId;
            string jwtToken = tokenService.GenerateToken(userAccount.Id, userAccount.Name, tenantId);
            string refreshToken = tokenService.GenerateRefreshToken(userAccount.Id);
            UserJwtDto entity = new()
            {
                UserId = userAccount.Id,
                JwtToken = jwtToken,
                RefreshToken = refreshToken,
                UserName = userAccount.Name
            };
            // no need to add idenity since claims are per-request and already added in the middleware
            //_httpContextAccessor.HttpContext?.User.AddIdentity(new ClaimsIdentity([
            //    new Claim(UserClaims.UserId, userAccount.Id.ToString()),
            //    new Claim(UserClaims.UserName, userAccount.Name),
            //    new Claim(UserClaims.RefreshToken, refreshToken)
            //]));

            return EntityResponse<UserJwtDto>.Success(entity);
        }
        catch (Exception ex)
        {
            return EntityResponse<UserJwtDto>.Failed(ex.Message);
        }
    }

    public async Task<EntityResponse<List<TenantDto>>> GetUserTenants()
    {
        string? userId = _httpContextAccessor.HttpContext?.User.Claims.FirstOrDefault(x => x.Type == "UserId")?.Value;
        if (string.IsNullOrEmpty(userId)) return EntityResponse<List<TenantDto>>.Failed();
        UserAccount? user = await DB.UserAccounts.AsNoTracking()
            .Include(userAccount => userAccount.Employees)
            .FirstOrDefaultAsync(x => x.Id.ToString() == userId);
        if (user == null) return EntityResponse<List<TenantDto>>.Failed();
        List<Guid> tenants = [.. user.Employees.Select(x => x.TenantId)];
        IQueryable<Tenant> query = DB.Tenants.Where(x => tenants.Contains(x.Id));
        return EntityResponse<List<TenantDto>>.Success(await query.ProjectToType<TenantDto>().ToListAsync());
    }

    public EntityResponse Deauthenticate(Guid userId)
    {
        tokenService.InvalidateRefreshToken(userId);
        return EntityResponse.Success();
    }

    public EntityResponse<UserJwtDto> RefreshToken(Guid userId, string tokenStr)
    {
        return tokenService.RenewJwtToken(userId, tokenStr);
    }

    public async Task<EntityResponse> SetTenant(GuidRequest tenantId, CancellationToken cancellationToken = default)
    {
        string? userId = _httpContextAccessor.HttpContext?.User.Claims.FirstOrDefault(x => x.Type == "UserId")?.Value;
        if (string.IsNullOrEmpty(userId)) return EntityResponse.Failed();
        HashSet<Guid> assignableRoles = [.. DB.Employees.AsNoTracking()
            .Where(x => x.UserAccountId.ToString() == userId && x.TenantId == tenantId.Id)
            .Include(x => x.RoleAssignments).SelectMany(x => x.RoleAssignments)
            .Include(x => x.AssignableRole).Select(x => x.AssignableRole).Select(x => x.RoleId)];
        List<AccessResource> accessResources = await DB.Roles.Where(x => assignableRoles.Contains(x.Id))
            .Include(x => x.RoleAccesses).SelectMany(x => x.RoleAccesses)
            .Include(x => x.Resource).Select(x => x.Resource)
            .ToListAsync(cancellationToken);

        IDatabase db = redis.GetDatabase();
        db.SetAdd(userId, [.. accessResources.Select(x => new RedisValue(x.ResourceCode))]);
        return EntityResponse.Success();
    }
}