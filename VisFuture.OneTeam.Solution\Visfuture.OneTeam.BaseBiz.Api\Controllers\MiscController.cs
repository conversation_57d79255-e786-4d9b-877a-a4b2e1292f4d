using Azure.Core;
using Microsoft.AspNetCore.Mvc;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests;
using Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.Api.Controllers;

public class MiscController(IMiscService miscService) : BaseController<MiscController>
{
    private readonly IMiscService miscService = miscService;

    #region I18nKey

    [HttpPost("I18nKeyList")]
    public async Task<IActionResult> GetI18nKeyList([FromBody] BaseQuery<I18nKeyDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<I18nKeyListItemDto>
            result = await miscService.QueryI18nKeyAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("I18nKeyDetails")]
    public async Task<IActionResult> GetI18nKeyDetails(Guid i18nKeyId, CancellationToken cancellationToken = default)
    {
        EntityResponse<I18nKeyDto>? result = await miscService.GetI18nKeyByIdAsync(i18nKeyId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddI18nKey")]
    public async Task<IActionResult> SaveI18nKey([FromBody] I18nKeyDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.AddI18nKeyAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateI18nKey")]
    public async Task<IActionResult> UpdateI18nKey([FromBody] I18nKeyDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.UpdateI18nKeyAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteI18nKey")]
    public async Task<IActionResult> DeleteI18nKey(Guid i18nKeyId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.DeleteI18nKeyAsync(i18nKeyId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddI18nKeyList")]
    public async Task<IActionResult> AddI18nKeyList([FromBody] BaseQuery<I18nKeyDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.AddI18nKeysAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateI18nKeyList")]
    public async Task<IActionResult> UpdateI18nKeyList([FromBody] BaseQuery<I18nKeyDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.UpdateI18nKeysAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteI18nKeyList")]
    public async Task<IActionResult> DeleteI18nKeyList([FromBody] BaseQuery<I18nKeyDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.DeleteI18nKeysAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportI18nKeyList")]
    public async Task<IActionResult> ExportI18nKeyExcel([FromBody] BaseQuery<I18nKeyDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File>? result = await miscService.ExportExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportI18nKeyList")]
    public async Task<IActionResult> ImportI18nKeyExcel(B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.ImportExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region I18nTranslation

    [HttpPost("I18nTranslationList")]
    public async Task<IActionResult> GetI18nTranslationList([FromBody] BaseQuery<I18nTranslationDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<I18nTranslationListItemDto> result =
            await miscService.QueryI18nTranslationAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("I18nTranslationDetails")]
    public async Task<IActionResult> GetI18nTranslationDetails(Guid i18nTranslationId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<I18nTranslationDto>? result =
            await miscService.GetI18nTranslationByIdAsync(i18nTranslationId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddI18nTranslation")]
    public async Task<IActionResult> SaveI18nTranslation([FromBody] I18nTranslationDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.AddI18nTranslationAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateI18nTranslation")]
    public async Task<IActionResult> UpdateI18nTranslation([FromBody] I18nTranslationDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.UpdateI18nTranslationAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteI18nTranslation")]
    public async Task<IActionResult> DeleteI18nTranslation(Guid i18nTranslationId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result =
            await miscService.DeleteI18nTranslationAsync(i18nTranslationId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddI18nTranslationList")]
    public async Task<IActionResult> AddI18nTranslationList([FromBody] BaseQuery<I18nTranslationDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.AddI18nTranslationsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateI18nTranslationList")]
    public async Task<IActionResult> UpdateI18nTranslationList([FromBody] BaseQuery<I18nTranslationDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.UpdateI18nTranslationsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteI18nTranslationList")]
    public async Task<IActionResult> DeleteI18nTranslationList([FromBody] BaseQuery<I18nTranslationDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.DeleteI18nTranslationsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportI18nTranslationList")]
    public async Task<IActionResult> ExportI18nTranslationExcel([FromBody] BaseQuery<I18nTranslationDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File>? result = await miscService.ExportExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportI18nTranslationList")]
    public async Task<IActionResult> ImportI18nTranslationExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.ImportExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region NotificationTemplate

    [HttpPost("NotificationTemplateList")]
    public async Task<IActionResult> GetNotificationTemplateList([FromBody] BaseQuery<NotificationTemplateDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<NotificationTemplateListItemDto> result =
            await miscService.QueryNotificationTemplateAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("NotificationTemplateDetails")]
    public async Task<IActionResult> GetNotificationTemplateDetails(Guid notificationTemplateId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<NotificationTemplateDto>? result =
            await miscService.GetNotificationTemplateByIdAsync(notificationTemplateId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddNotificationTemplate")]
    public async Task<IActionResult> SaveNotificationTemplate([FromBody] NotificationTemplateDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.AddNotificationTemplateAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateNotificationTemplate")]
    public async Task<IActionResult> UpdateNotificationTemplate([FromBody] NotificationTemplateDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.UpdateNotificationTemplateAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteNotificationTemplate")]
    public async Task<IActionResult> DeleteNotificationTemplate(Guid notificationTemplateId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result =
            await miscService.DeleteNotificationTemplateAsync(notificationTemplateId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddNotificationTemplateList")]
    public async Task<IActionResult> AddNotificationTemplateList([FromBody] BaseQuery<NotificationTemplateDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.AddNotificationTemplatesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateNotificationTemplateList")]
    public async Task<IActionResult> UpdateNotificationTemplateList(
        [FromBody] BaseQuery<NotificationTemplateDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.UpdateNotificationTemplatesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteNotificationTemplateList")]
    public async Task<IActionResult> DeleteNotificationTemplateList(
        [FromBody] BaseQuery<NotificationTemplateDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.DeleteNotificationTemplatesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportNotificationTemplateList")]
    public async Task<IActionResult> ExportNotificationTemplateExcel(
        [FromBody] BaseQuery<NotificationTemplateDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File>? result = await miscService.ExportExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportNotificationTemplateList")]
    public async Task<IActionResult> ImportNotificationTemplateExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.ImportExcel(file, cancellationToken);
        return Ok(result);
    }

    [HttpGet("GetNotificationTemplateByNotificationTypeAndNotificationMethod")]
    public async Task<IActionResult> GetNotificationTemplateByNotificationTypeAndNotificationMethod(
        string notificationType,
        string notificationMethod,
        CancellationToken cancellationToken = default
        )
    {
        EntityResponse<NotificationTemplateDto?> result = await miscService.GetNotificationTemplateByNotificationTypeAndNotificationMethod(
            notificationType,
            notificationMethod,
            cancellationToken
            );
        return Ok(result);
    }

    #endregion

    #region SequenceNo

    [HttpPost("SequenceNoList")]
    public async Task<IActionResult> GetSequenceNoList([FromBody] BaseQuery<SequenceNoDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<SequenceNoListItemDto> result =
            await miscService.QuerySequenceNoAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("SequenceNoByName")]
    public async Task<IActionResult> GetSequenceNoByName(string name, CancellationToken cancellationToken = default)
    {
        EntityResponse<SequenceNoDto>? result = await miscService.GetSequenceNoByNameAsync(name, cancellationToken);
        return Ok(result);
    }

    [HttpGet("SequenceNoDetails")]
    public async Task<IActionResult> GetSequenceNoDetails(Guid sequenceNoId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<SequenceNoDto>? result =
            await miscService.GetSequenceNoByIdAsync(sequenceNoId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddSequenceNo")]
    public async Task<IActionResult> SaveSequenceNo([FromBody] SequenceNoDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.AddSequenceNoAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateSequenceNo")]
    public async Task<IActionResult> UpdateSequenceNo([FromBody] SequenceNoDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.UpdateSequenceNoAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteSequenceNo")]
    public async Task<IActionResult> DeleteSequenceNo(Guid sequenceNoId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.DeleteSequenceNoAsync(sequenceNoId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddSequenceNoList")]
    public async Task<IActionResult> AddSequenceNoList([FromBody] BaseQuery<SequenceNoDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.AddSequenceNosAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateSequenceNoList")]
    public async Task<IActionResult> UpdateSequenceNoList([FromBody] BaseQuery<SequenceNoDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.UpdateSequenceNosAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteSequenceNoList")]
    public async Task<IActionResult> DeleteSequenceNoList([FromBody] BaseQuery<SequenceNoDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.DeleteSequenceNosAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportSequenceNoList")]
    public async Task<IActionResult> ExportSequenceNoExcel([FromBody] BaseQuery<SequenceNoDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File>? result = await miscService.ExportExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportSequenceNoList")]
    public async Task<IActionResult> ImportSequenceNoExcel(B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.ImportExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region CodeItem

    [HttpPost("CodeItemList")]
    public async Task<IActionResult> GetCodeItemList([FromBody] CodeItemRequest request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<CodeItemListItemDto> result =
            await miscService.QueryCodeItemAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("CodeItemDetails")]
    public async Task<IActionResult> GetCodeItemDetails(Guid codeItemId, CancellationToken cancellationToken = default)
    {
        EntityResponse<CodeItemDto>? result = await miscService.GetCodeItemByIdAsync(codeItemId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddCodeItem")]
    public async Task<IActionResult> SaveCodeItem([FromBody] CodeItemDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.AddCodeItemAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateCodeItem")]
    public async Task<IActionResult> UpdateCodeItem([FromBody] CodeItemDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.UpdateCodeItemAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteCodeItem")]
    public async Task<IActionResult> DeleteCodeItem(Guid codeItemId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.DeleteCodeItemAsync(codeItemId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddCodeItemList")]
    public async Task<IActionResult> AddCodeItemList([FromBody] BaseQuery<CodeItemDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.AddCodeItemsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateCodeItemList")]
    public async Task<IActionResult> UpdateCodeItemList([FromBody] BaseQuery<CodeItemDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.UpdateCodeItemsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteCodeItemList")]
    public async Task<IActionResult> DeleteCodeItemList([FromBody] BaseQuery<CodeItemDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.DeleteCodeItemsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportCodeItemList")]
    public async Task<IActionResult> ExportCodeItemExcel([FromBody] BaseQuery<CodeItemDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File>? result = await miscService.ExportExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportCodeItemList")]
    public async Task<IActionResult> ImportCodeItemExcel(B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.ImportExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region CodeItemAttribute

    [HttpPost("CodeItemAttributeList")]
    public async Task<IActionResult> GetCodeItemAttributeList([FromBody] BaseQuery<CodeItemAttributeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<CodeItemAttributeListItemDto> result =
            await miscService.QueryCodeItemAttributeAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("CodeItemAttributeDetails")]
    public async Task<IActionResult> GetCodeItemAttributeDetails(Guid codeItemAttributeId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<CodeItemAttributeDto>? result =
            await miscService.GetCodeItemAttributeByIdAsync(codeItemAttributeId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddCodeItemAttribute")]
    public async Task<IActionResult> SaveCodeItemAttribute([FromBody] CodeItemAttributeDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.AddCodeItemAttributeAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateCodeItemAttribute")]
    public async Task<IActionResult> UpdateCodeItemAttribute([FromBody] CodeItemAttributeDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.UpdateCodeItemAttributeAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteCodeItemAttribute")]
    public async Task<IActionResult> DeleteCodeItemAttribute(Guid codeItemAttributeId,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result =
            await miscService.DeleteCodeItemAttributeAsync(codeItemAttributeId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddCodeItemAttributeList")]
    public async Task<IActionResult> AddCodeItemAttributeList([FromBody] BaseQuery<CodeItemAttributeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.AddCodeItemAttributesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateCodeItemAttributeList")]
    public async Task<IActionResult> UpdateCodeItemAttributeList([FromBody] BaseQuery<CodeItemAttributeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.UpdateCodeItemAttributesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteCodeItemAttributeList")]
    public async Task<IActionResult> DeleteCodeItemAttributeList([FromBody] BaseQuery<CodeItemAttributeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.DeleteCodeItemAttributesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportCodeItemAttributeList")]
    public async Task<IActionResult> ExportCodeItemAttributeExcel([FromBody] BaseQuery<CodeItemAttributeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File>? result = await miscService.ExportExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportCodeItemAttributeList")]
    public async Task<IActionResult> ImportCodeItemAttributeExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.ImportExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion

    #region CodeType

    [HttpPost("CodeTypeList")]
    public async Task<IActionResult> GetCodeTypeList([FromBody] BaseQuery<CodeTypeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<CodeTypeListItemDto> result =
            await miscService.QueryCodeTypeAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("CodeTypeDetails")]
    public async Task<IActionResult> GetCodeTypeDetails(Guid codeTypeId, CancellationToken cancellationToken = default)
    {
        EntityResponse<CodeTypeDto>? result = await miscService.GetCodeTypeByIdAsync(codeTypeId, cancellationToken);
        return Ok(result);
    }

    [HttpGet("CodeTypeByTypeCode")]
    public async Task<IActionResult> GetCodeTypeByTypeCode(string typeCode,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<CodeTypeDto>? result = await miscService.GetCodeTypeByTypeCodeAsync(typeCode, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddCodeType")]
    public async Task<IActionResult> SaveCodeType([FromBody] CodeTypeDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.AddCodeTypeAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateCodeType")]
    public async Task<IActionResult> UpdateCodeType([FromBody] CodeTypeDto model,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.UpdateCodeTypeAsync(model, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteCodeType")]
    public async Task<IActionResult> DeleteCodeType(Guid codeTypeId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid>? result = await miscService.DeleteCodeTypeAsync(codeTypeId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddCodeTypeList")]
    public async Task<IActionResult> AddCodeTypeList([FromBody] BaseQuery<CodeTypeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.AddCodeTypesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("UpdateCodeTypeList")]
    public async Task<IActionResult> UpdateCodeTypeList([FromBody] BaseQuery<CodeTypeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.UpdateCodeTypesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("DeleteCodeTypeList")]
    public async Task<IActionResult> DeleteCodeTypeList([FromBody] BaseQuery<CodeTypeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.DeleteCodeTypesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportCodeTypeList")]
    public async Task<IActionResult> ExportCodeTypeExcel([FromBody] BaseQuery<CodeTypeDto> request,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<B64File>? result = await miscService.ExportExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportCodeTypeList")]
    public async Task<IActionResult> ImportCodeTypeExcel(B64File file, CancellationToken cancellationToken = default)
    {
        EntityResponse? result = await miscService.ImportExcel(file, cancellationToken);
        return Ok(result);
    }

    #endregion
}