2025-05-16 09:51:41.006 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-16 09:51:41.138 -04:00 [INF] Now listening on: http://localhost:5275
2025-05-16 09:51:41.174 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-16 09:51:41.177 -04:00 [INF] Hosting environment: Development
2025-05-16 09:51:41.179 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-05-16 09:54:25.879 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TimeLogTask - null null
2025-05-16 09:54:25.972 -04:00 [INF] CORS policy execution successful.
2025-05-16 09:54:25.980 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 50.9341 ms
2025-05-16 09:54:26.003 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TimeLogTask - 204 null null 134.7318ms
2025-05-16 09:54:26.033 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TimeLogTask - application/json null
2025-05-16 09:54:26.042 -04:00 [INF] CORS policy execution successful.
2025-05-16 09:54:27.716 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-16 09:54:27.773 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-16 09:54:31.741 -04:00 [INF] Executed DbCommand (101ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-16 09:54:31.818 -04:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-16 09:54:32.444 -04:00 [INF] Executed DbCommand (162ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-05-16 09:54:32.599 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-16 09:54:32.648 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 4863.4053ms
2025-05-16 09:54:32.653 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-16 09:54:32.658 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 6617.1765 ms
2025-05-16 09:54:32.676 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TimeLogTask - 200 null application/json; charset=utf-8 6642.8819ms
2025-05-16 09:54:32.688 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-05-16 09:54:32.695 -04:00 [INF] CORS policy execution successful.
2025-05-16 09:54:32.697 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 2.5774 ms
2025-05-16 09:54:32.704 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 16.7045ms
2025-05-16 09:54:32.715 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 110
2025-05-16 09:54:32.724 -04:00 [INF] CORS policy execution successful.
2025-05-16 09:54:32.728 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-16 09:54:32.741 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-16 09:54:32.804 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-16 09:54:32.839 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-16 09:54:32.974 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-05-16 09:54:33.007 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-16 09:54:33.026 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 276.084ms
2025-05-16 09:54:33.032 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-16 09:54:33.036 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 312.1615 ms
2025-05-16 09:54:33.040 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 325.4137ms
2025-05-16 09:54:33.049 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-05-16 09:54:33.056 -04:00 [INF] CORS policy execution successful.
2025-05-16 09:54:33.058 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.1797 ms
2025-05-16 09:54:33.062 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 13.5071ms
2025-05-16 09:54:33.071 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 168
2025-05-16 09:54:33.077 -04:00 [INF] CORS policy execution successful.
2025-05-16 09:54:33.083 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-16 09:54:33.094 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-05-16 09:54:33.239 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-16 09:54:33.275 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-05-16 09:54:33.472 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-05-16 09:54:33.485 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-16 09:54:33.503 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 405.0326ms
2025-05-16 09:54:33.508 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-05-16 09:54:33.510 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 433.0755 ms
2025-05-16 09:54:33.514 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 442.6697ms
