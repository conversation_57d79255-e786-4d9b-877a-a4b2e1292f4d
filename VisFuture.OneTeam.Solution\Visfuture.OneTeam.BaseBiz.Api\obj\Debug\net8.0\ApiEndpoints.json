[{"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "GetAccessResourceDetails", "RelativePath": "AccessResourceDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "accessResourceId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "QueryAccessResourceListAsync", "RelativePath": "AccessResourceList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AccessResourceDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "AddAccessResourceAsync", "RelativePath": "AddAccessResource", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AccessResourceDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "AddAccessResourceList", "RelativePath": "AddAccessResourceList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AccessResourceDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "AddAssignableRoleAsync", "RelativePath": "AddAssignableRole", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "AddAssignableRoleList", "RelativePath": "AddAssignableRoleList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "SaveCodeItem", "RelativePath": "AddCodeItem", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeItemDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "SaveCodeItemAttribute", "RelativePath": "AddCodeItemAttribute", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeItemAttributeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "AddCodeItemAttributeList", "RelativePath": "AddCodeItemAttributeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeItemAttributeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "AddCodeItemList", "RelativePath": "AddCodeItemList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "SaveCodeType", "RelativePath": "AddCodeType", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "AddCodeTypeList", "RelativePath": "AddCodeTypeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "AddEmployee", "RelativePath": "AddEmployee", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "AddEmployeeList", "RelativePath": "AddEmployeeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "AddGlobalAdmin", "RelativePath": "AddGlobalAdmin", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.GlobalAdminDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "AddGlobalAdminList", "RelativePath": "AddGlobalAdminList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.GlobalAdminDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "SaveI18nKey", "RelativePath": "AddI18nKey", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nKeyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "AddI18nKeyList", "RelativePath": "AddI18nKeyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nKeyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "SaveI18nTranslation", "RelativePath": "AddI18nTranslation", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nTranslationDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "AddI18nTranslationList", "RelativePath": "AddI18nTranslationList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nTranslationDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "SaveNotificationTemplate", "RelativePath": "AddNotificationTemplate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.NotificationTemplateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "AddNotificationTemplateList", "RelativePath": "AddNotificationTemplateList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.NotificationTemplateDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "AddOrganizationEmployee", "RelativePath": "AddOrganizationEmployee", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "AddOrganizationEmployeeList", "RelativePath": "AddOrganizationEmployeeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "AddOrganizationHierarchy", "RelativePath": "AddOrganizationHierarchy", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "AddOrganizationHierarchyList", "RelativePath": "AddOrganizationHierarchyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "AddRoleAsync", "RelativePath": "AddRole", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "AddRoleAccessAsync", "RelativePath": "AddRoleAccess", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAccessDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "AddRoleAccessList", "RelativePath": "AddRoleAccessList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAccessDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "AddRoleAssignmentAsync", "RelativePath": "AddRoleAssignment", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "AddRoleAssignmentList", "RelativePath": "AddRoleAssignmentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "AddRoleList", "RelativePath": "AddRoleList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "SaveSequenceNo", "RelativePath": "AddSequenceNo", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "AddSequenceNoList", "RelativePath": "AddSequenceNoList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "AddTenant", "RelativePath": "AddTenant", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.TenantDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "AddTenantList", "RelativePath": "AddTenantList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.TenantDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "AddUserAccount", "RelativePath": "AddUserAccount", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.UserAccountDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "AddUserAccountList", "RelativePath": "AddUserAccountList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.UserAccountDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "GetAssignableRoleDetails", "RelativePath": "AssignableRoleDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "assignableRoleId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "QueryAssignableRoleListAsync", "RelativePath": "AssignableRoleList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetCodeItemAttributeDetails", "RelativePath": "CodeItemAttributeDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "codeItemAttributeId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetCodeItemAttributeList", "RelativePath": "CodeItemAttributeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeItemAttributeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetCodeItemDetails", "RelativePath": "CodeItemDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "codeItemId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetCodeItemList", "RelativePath": "CodeItemList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetCodeTypeByTypeCode", "RelativePath": "CodeTypeByTypeCode", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "typeCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetCodeTypeDetails", "RelativePath": "CodeTypeDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "codeTypeId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetCodeTypeList", "RelativePath": "CodeTypeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "DeleteAccessResourceAsync", "RelativePath": "DeleteAccessResource", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "accessResourceId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "DeleteAccessResourceList", "RelativePath": "DeleteAccessResourceList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AccessResourceDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "DeleteAssignableRoleAsync", "RelativePath": "DeleteAssignableRole", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "assignableRoleId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "DeleteAssignableRoleList", "RelativePath": "DeleteAssignableRoleList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteCodeItem", "RelativePath": "DeleteCodeItem", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "codeItemId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteCodeItemAttribute", "RelativePath": "DeleteCodeItemAttribute", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "codeItemAttributeId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteCodeItemAttributeList", "RelativePath": "DeleteCodeItemAttributeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeItemAttributeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteCodeItemList", "RelativePath": "DeleteCodeItemList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteCodeType", "RelativePath": "DeleteCodeType", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "codeTypeId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteCodeTypeList", "RelativePath": "DeleteCodeTypeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "DeleteEmployee", "RelativePath": "DeleteEmployee", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "employeeId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "DeleteEmployeeList", "RelativePath": "DeleteEmployeeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "DeleteGlobalAdmin", "RelativePath": "DeleteGlobalAdmin", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "globalAdminId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "DeleteGlobalAdminList", "RelativePath": "DeleteGlobalAdminList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.GlobalAdminDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteI18nKey", "RelativePath": "DeleteI18nKey", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "i18nKeyId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteI18nKeyList", "RelativePath": "DeleteI18nKeyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nKeyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteI18nTranslation", "RelativePath": "DeleteI18nTranslation", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "i18nTranslationId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteI18nTranslationList", "RelativePath": "DeleteI18nTranslationList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nTranslationDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteNotificationTemplate", "RelativePath": "DeleteNotificationTemplate", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "notificationTemplateId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteNotificationTemplateList", "RelativePath": "DeleteNotificationTemplateList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.NotificationTemplateDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "DeleteOrganizationEmployee", "RelativePath": "DeleteOrganizationEmployee", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationEmployeeId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "DeleteOrganizationEmployeeList", "RelativePath": "DeleteOrganizationEmployeeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "DeleteOrganizationHierarchy", "RelativePath": "DeleteOrganizationHierarchy", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationHierarchyId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "DeleteOrganizationHierarchyList", "RelativePath": "DeleteOrganizationHierarchyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "DeleteRoleAsync", "RelativePath": "DeleteRole", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "DeleteRoleAccessAsync", "RelativePath": "DeleteRoleAccess", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleAccessId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "DeleteRoleAccessList", "RelativePath": "DeleteRoleAccessList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAccessDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "DeleteRoleAssignmentAsync", "RelativePath": "DeleteRoleAssignment", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleAssignmentId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "DeleteRoleAssignmentList", "RelativePath": "DeleteRoleAssignmentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "DeleteRoleList", "RelativePath": "DeleteRoleList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteSequenceNo", "RelativePath": "DeleteSequenceNo", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "sequenceNoId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "DeleteSequenceNoList", "RelativePath": "DeleteSequenceNoList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "DeleteTenant", "RelativePath": "DeleteTenant", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "DeleteTenantList", "RelativePath": "DeleteTenantList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.TenantDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "DeleteUserAccount", "RelativePath": "DeleteUserAccount", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "userAccountId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "DeleteUserAccountList", "RelativePath": "DeleteUserAccountList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.UserAccountDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "GetEmployeeDetails", "RelativePath": "EmployeeDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "employeeId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "GetEmployeeList", "RelativePath": "EmployeeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "GetEmployeeListByOrganization", "RelativePath": "EmployeeListByOrganizationList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "ExportAccessResourceExcel", "RelativePath": "ExportAccessResourceList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "ExportAssignableRoleExcel", "RelativePath": "ExportAssignableRoleList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ExportCodeItemAttributeExcel", "RelativePath": "ExportCodeItemAttributeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeItemAttributeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ExportCodeItemExcel", "RelativePath": "ExportCodeItemList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ExportCodeTypeExcel", "RelativePath": "ExportCodeTypeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "ExportEmployeeExcel", "RelativePath": "ExportEmployeeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "ExportGlobalAdminExcel", "RelativePath": "ExportGlobalAdminList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.GlobalAdminDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ExportI18nKeyExcel", "RelativePath": "ExportI18nKeyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nKeyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ExportI18nTranslationExcel", "RelativePath": "ExportI18nTranslationList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nTranslationDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ExportNotificationTemplateExcel", "RelativePath": "ExportNotificationTemplateList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.NotificationTemplateDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "ExportOrganizationEmployeeExcel", "RelativePath": "ExportOrganizationEmployeeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "ExportOrganizationHierarchyExcel", "RelativePath": "ExportOrganizationHierarchyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "ExportRoleAccessExcel", "RelativePath": "ExportRoleAccessList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "ExportRoleAssignmentExcel", "RelativePath": "ExportRoleAssignmentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "ExportRoleExcel", "RelativePath": "ExportRoleList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ExportSequenceNoExcel", "RelativePath": "ExportSequenceNoList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "ExportTenantExcel", "RelativePath": "ExportTenantList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.TenantDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "ExportUserAccountExcel", "RelativePath": "ExportUserAccountList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.UserAccountDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController", "Method": "GetSequenceNoByNameExt", "RelativePath": "External/SequenceNoByName", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController", "Method": "UpdateSequenceNoExt", "RelativePath": "External/UpdateSequenceNo", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.FileController", "Method": "Delete", "RelativePath": "File/Delete", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.FileController", "Method": "GetLink", "RelativePath": "File/GetLink", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.FileController", "Method": "Upload", "RelativePath": "File/Upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetNotificationTemplateByNotificationTypeAndNotificationMethod", "RelativePath": "GetNotificationTemplateByNotificationTypeAndNotificationMethod", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "notificationType", "Type": "System.String", "IsRequired": false}, {"Name": "notificationMethod", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "GetGlobalAdminDetails", "RelativePath": "GlobalAdminDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "globalAdminId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "GetGlobalAdminList", "RelativePath": "GlobalAdminList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.GlobalAdminDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetI18nKeyDetails", "RelativePath": "I18nKeyDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "i18nKeyId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetI18nKeyList", "RelativePath": "I18nKeyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nKeyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetI18nTranslationDetails", "RelativePath": "I18nTranslationDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "i18nTranslationId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetI18nTranslationList", "RelativePath": "I18nTranslationList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nTranslationDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "ImportAccessResourceExcel", "RelativePath": "ImportAccessResourceList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "ImportAssignableRoleExcel", "RelativePath": "ImportAssignableRoleList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ImportCodeItemAttributeExcel", "RelativePath": "ImportCodeItemAttributeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ImportCodeItemExcel", "RelativePath": "ImportCodeItemList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ImportCodeTypeExcel", "RelativePath": "ImportCodeTypeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "ImportEmployeeExcel", "RelativePath": "ImportEmployeeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "ImportGlobalAdminExcel", "RelativePath": "ImportGlobalAdminList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ImportI18nKeyExcel", "RelativePath": "ImportI18nKeyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ImportI18nTranslationExcel", "RelativePath": "ImportI18nTranslationList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ImportNotificationTemplateExcel", "RelativePath": "ImportNotificationTemplateList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "ImportOrganizationEmployeeExcel", "RelativePath": "ImportOrganizationEmployeeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "ImportOrganizationHierarchyExcel", "RelativePath": "ImportOrganizationHierarchyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "ImportRoleAccessExcel", "RelativePath": "ImportRoleAccessList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "ImportRoleAssignmentExcel", "RelativePath": "ImportRoleAssignmentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "ImportRoleExcel", "RelativePath": "ImportRoleList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "ImportSequenceNoExcel", "RelativePath": "ImportSequenceNoList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "ImportTenantExcel", "RelativePath": "ImportTenantList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "ImportUserAccountExcel", "RelativePath": "ImportUserAccountList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController", "Method": "<PERSON><PERSON>", "RelativePath": "<PERSON><PERSON>", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.UserLoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController", "Method": "Logout", "RelativePath": "Logout", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "MatchAccessResourceAsync", "RelativePath": "MatchAccessResource", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.StringListRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetNotificationTemplateDetails", "RelativePath": "NotificationTemplateDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "notificationTemplateId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetNotificationTemplateList", "RelativePath": "NotificationTemplateList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.NotificationTemplateDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "GetOrganizationEmployeeDetails", "RelativePath": "OrganizationEmployeeDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationEmployeeId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "GetOrganizationEmployeeList", "RelativePath": "OrganizationEmployeeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "GetOrganizationHierarchyDetails", "RelativePath": "OrganizationHierarchyDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationHierarchyId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "GetOrganizationHierarchyList", "RelativePath": "OrganizationHierarchyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController", "Method": "RefreshToken", "RelativePath": "Refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.UserJwtDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "GetRoleAccessDetails", "RelativePath": "RoleAccessDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleAccessId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "QueryRoleAccessListAsync", "RelativePath": "RoleAccessList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAccessDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "GetRoleAssignmentDetails", "RelativePath": "RoleAssignmentDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleAssignmentId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "QueryRoleAssignmentListAsync", "RelativePath": "RoleAssignmentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "GetRoleDetails", "RelativePath": "RoleDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "QueryRoleListAsync", "RelativePath": "RoleList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetSequenceNoByName", "RelativePath": "SequenceNoByName", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetSequenceNoDetails", "RelativePath": "SequenceNoDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sequenceNoId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "GetSequenceNoList", "RelativePath": "SequenceNoList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController", "Method": "Set<PERSON>enant", "RelativePath": "Set<PERSON>enant", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantId", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.GuidRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "GetTenantDetails", "RelativePath": "TenantDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "GetTenantList", "RelativePath": "TenantList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.TenantDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "UpdateAccessResourceAsync", "RelativePath": "UpdateAccessResource", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AccessResourceDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "UpdateAccessResourceList", "RelativePath": "UpdateAccessResourceList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AccessResourceDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "UpdateAssignableRoleAsync", "RelativePath": "UpdateAssignableRole", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "UpdateAssignableRoleList", "RelativePath": "UpdateAssignableRoleList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateCodeItem", "RelativePath": "UpdateCodeItem", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeItemDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateCodeItemAttribute", "RelativePath": "UpdateCodeItemAttribute", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeItemAttributeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateCodeItemAttributeList", "RelativePath": "UpdateCodeItemAttributeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeItemAttributeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateCodeItemList", "RelativePath": "UpdateCodeItemList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateCodeType", "RelativePath": "UpdateCodeType", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateCodeTypeList", "RelativePath": "UpdateCodeTypeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "UpdateEmployee", "RelativePath": "UpdateEmployee", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "UpdateEmployeeList", "RelativePath": "UpdateEmployeeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "UpdateGlobalAdmin", "RelativePath": "UpdateGlobalAdmin", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.GlobalAdminDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "UpdateGlobalAdminList", "RelativePath": "UpdateGlobalAdminList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.GlobalAdminDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateI18nKey", "RelativePath": "UpdateI18nKey", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nKeyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateI18nKeyList", "RelativePath": "UpdateI18nKeyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nKeyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateI18nTranslation", "RelativePath": "UpdateI18nTranslation", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nTranslationDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateI18nTranslationList", "RelativePath": "UpdateI18nTranslationList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.I18nTranslationDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateNotificationTemplate", "RelativePath": "UpdateNotificationTemplate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.NotificationTemplateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateNotificationTemplateList", "RelativePath": "UpdateNotificationTemplateList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.NotificationTemplateDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "UpdateOrganizationEmployee", "RelativePath": "UpdateOrganizationEmployee", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "UpdateOrganizationEmployeeList", "RelativePath": "UpdateOrganizationEmployeeList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "UpdateOrganizationHierarchy", "RelativePath": "UpdateOrganizationHierarchy", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "UpdateOrganizationHierarchyList", "RelativePath": "UpdateOrganizationHierarchyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "UpdateRoleAsync", "RelativePath": "UpdateRole", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "UpdateRoleAccessAsync", "RelativePath": "UpdateRoleAccess", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAccessDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "UpdateRoleAccessList", "RelativePath": "UpdateRoleAccessList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAccessDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "UpdateRoleAssignmentAsync", "RelativePath": "UpdateRoleAssignment", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "UpdateRoleAssignmentList", "RelativePath": "UpdateRoleAssignmentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController", "Method": "UpdateRoleList", "RelativePath": "UpdateRoleList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateSequenceNo", "RelativePath": "UpdateSequenceNo", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController", "Method": "UpdateSequenceNoList", "RelativePath": "UpdateSequenceNoList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "UpdateTenant", "RelativePath": "UpdateTenant", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.TenantDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "UpdateTenantList", "RelativePath": "UpdateTenantList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.TenantDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "UpdateUserAccount", "RelativePath": "UpdateUserAccount", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.UserAccountDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "UpdateUserAccountList", "RelativePath": "UpdateUserAccountList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.UserAccountDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "GetUserAccountDetails", "RelativePath": "UserAccountDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userAccountId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController", "Method": "GetUserAccountList", "RelativePath": "UserAccountList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.UserAccountDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController", "Method": "GetUser", "RelativePath": "UserTenantList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Visfuture.OneTeam.BaseBiz.Api.WeatherForecast, Visfuture.OneTeam.BaseBiz.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}, {"ContainingType": "Visfuture.OneTeam.BaseBiz.Api.Controllers.WeatherForecastController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "WeatherForecast/CacheUserId", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]