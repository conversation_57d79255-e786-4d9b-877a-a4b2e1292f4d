﻿using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Interfaces;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests;
using Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.InternalService;

public class MiscService(IMiscManager miscManager) : IMiscService
{
    private readonly IMiscManager _miscManager = miscManager;

    #region I18nKey

    public async Task<EntityResponse<I18nKeyDto>> GetI18nKeyByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.GetI18nKeyByIdAsync(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddI18nKeyAsync(I18nKeyDto i18nKeyDto,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddI18nKeyAsync(i18nKeyDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateI18nKeyAsync(I18nKeyDto i18nKeyDto,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateI18nKeyAsync(i18nKeyDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteI18nKeyAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteI18nKeyAsync(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<I18nKeyListItemDto>> QueryI18nKeyAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.QueryI18nKeyAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> AddI18nKeysAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddI18nKeysAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateI18nKeysAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateI18nKeysAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteI18nKeysAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteI18nKeysAsync(request, cancellationToken);
    }

    #endregion

    #region I18nTranslation

    public async Task<EntityResponse<I18nTranslationDto>> GetI18nTranslationByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.GetI18nTranslationByIdAsync(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddI18nTranslationAsync(I18nTranslationDto i18nTranslationDto,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddI18nTranslationAsync(i18nTranslationDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateI18nTranslationAsync(I18nTranslationDto i18nTranslationDto,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateI18nTranslationAsync(i18nTranslationDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteI18nTranslationAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteI18nTranslationAsync(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<I18nTranslationListItemDto>> QueryI18nTranslationAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.QueryI18nTranslationAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> AddI18nTranslationsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddI18nTranslationsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateI18nTranslationsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateI18nTranslationsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteI18nTranslationsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteI18nTranslationsAsync(request, cancellationToken);
    }

    #endregion

    #region NotificationTemplate

    public async Task<EntityResponse<NotificationTemplateDto>> GetNotificationTemplateByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.GetNotificationTemplateByIdAsync(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddNotificationTemplateAsync(
        NotificationTemplateDto notificationTemplateDto, CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddNotificationTemplateAsync(notificationTemplateDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateNotificationTemplateAsync(
        NotificationTemplateDto notificationTemplateDto, CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateNotificationTemplateAsync(notificationTemplateDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteNotificationTemplateAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteNotificationTemplateAsync(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<NotificationTemplateListItemDto>> QueryNotificationTemplateAsync(
        BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _miscManager.QueryNotificationTemplateAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> AddNotificationTemplatesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddNotificationTemplatesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateNotificationTemplatesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateNotificationTemplatesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteNotificationTemplatesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteNotificationTemplatesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<NotificationTemplateDto?>> GetNotificationTemplateByNotificationTypeAndNotificationMethod(
        string notificationType,
        string notificationMethod,
        CancellationToken cancellationToken = default
        )
    {
        return await _miscManager.GetNotificationTemplateByNotificationTypeAndNotificationMethod(
            notificationType,
            notificationMethod
            );
    }

    #endregion

    #region SequenceNo

    public async Task<EntityResponse<SequenceNoDto>> GetSequenceNoByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.GetSequenceNoByIdAsync(id, cancellationToken);
    }

    public async Task<EntityResponse<SequenceNoDto>> GetSequenceNoByNameAsync(string name,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.GetSequenceNoByNameAsync(name, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddSequenceNoAsync(SequenceNoDto sequenceNoDto,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddSequenceNoAsync(sequenceNoDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateSequenceNoAsync(SequenceNoDto sequenceNoDto,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateSequenceNoAsync(sequenceNoDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteSequenceNoAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteSequenceNoAsync(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<SequenceNoListItemDto>> QuerySequenceNoAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.QuerySequenceNoAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> AddSequenceNosAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddSequenceNosAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateSequenceNosAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateSequenceNosAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteSequenceNosAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteSequenceNosAsync(request, cancellationToken);
    }

    #endregion

    #region CodeItem

    public async Task<EntityResponse<CodeItemDto>> GetCodeItemByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.GetCodeItemByIdAsync(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddCodeItemAsync(CodeItemDto codeItemDto,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddCodeItemAsync(codeItemDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateCodeItemAsync(CodeItemDto codeItemDto,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateCodeItemAsync(codeItemDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteCodeItemAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteCodeItemAsync(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<CodeItemListItemDto>> QueryCodeItemAsync(CodeItemRequest request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.QueryCodeItemAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> AddCodeItemsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddCodeItemsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateCodeItemsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateCodeItemsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteCodeItemsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteCodeItemsAsync(request, cancellationToken);
    }

    #endregion

    #region CodeItemAttribute

    public async Task<EntityResponse<CodeItemAttributeDto>> GetCodeItemAttributeByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.GetCodeItemAttributeByIdAsync(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddCodeItemAttributeAsync(CodeItemAttributeDto codeItemAttributeDto,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddCodeItemAttributeAsync(codeItemAttributeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateCodeItemAttributeAsync(CodeItemAttributeDto codeItemAttributeDto,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateCodeItemAttributeAsync(codeItemAttributeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteCodeItemAttributeAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteCodeItemAttributeAsync(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<CodeItemAttributeListItemDto>> QueryCodeItemAttributeAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.QueryCodeItemAttributeAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> AddCodeItemAttributesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddCodeItemAttributesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateCodeItemAttributesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateCodeItemAttributesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteCodeItemAttributesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteCodeItemAttributesAsync(request, cancellationToken);
    }

    #endregion

    #region CodeType

    public async Task<EntityResponse<CodeTypeDto>> GetCodeTypeByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.GetCodeTypeByIdAsync(id, cancellationToken);
    }

    public async Task<EntityResponse<CodeTypeDto>> GetCodeTypeByTypeCodeAsync(string typeCode,
        CancellationToken cancellationToken)
    {
        return await _miscManager.GetCodeTypeByTypeCodeAsync(typeCode, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddCodeTypeAsync(CodeTypeDto codeTypeDto,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddCodeTypeAsync(codeTypeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateCodeTypeAsync(CodeTypeDto codeTypeDto,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateCodeTypeAsync(codeTypeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteCodeTypeAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteCodeTypeAsync(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<CodeTypeListItemDto>> QueryCodeTypeAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.QueryCodeTypeAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> AddCodeTypesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.AddCodeTypesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateCodeTypesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.UpdateCodeTypesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteCodeTypesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.DeleteCodeTypesAsync(request, cancellationToken);
    }

    #endregion

    #region Excel Operations

    public async Task<EntityResponse<B64File>> ExportExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.ExportI18nKeyExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _miscManager.ImportI18nKeyExcel(file, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportI18nTranslationExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.ExportI18nTranslationExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportI18nTranslationExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.ImportI18nTranslationExcel(file, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportNotificationTemplateExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.ExportNotificationTemplateExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportNotificationTemplateExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.ImportNotificationTemplateExcel(file, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportSequenceNoExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.ExportSequenceNoExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportSequenceNoExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _miscManager.ImportSequenceNoExcel(file, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportCodeItemExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.ExportCodeItemExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportCodeItemExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _miscManager.ImportCodeItemExcel(file, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportCodeItemAttributeExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.ExportCodeItemAttributeExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportCodeItemAttributeExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.ImportCodeItemAttributeExcel(file, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportCodeTypeExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _miscManager.ExportCodeTypeExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportCodeTypeExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _miscManager.ImportCodeTypeExcel(file, cancellationToken);
    }

    #endregion
}