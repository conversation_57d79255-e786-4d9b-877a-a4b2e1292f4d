﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.DataAccess.Entities;

public partial class AssignableRole : TenantEntity
{
    public Guid OrganizationId { get; set; }

    public Guid RoleId { get; set; }

    public bool IsInheritable { get; set; }

    public virtual OrganizationHierarchy Organization { get; set; } = null!;

    public virtual Role Role { get; set; } = null!;

    public virtual ICollection<RoleAssignment> RoleAssignments { get; set; } = [];
}
