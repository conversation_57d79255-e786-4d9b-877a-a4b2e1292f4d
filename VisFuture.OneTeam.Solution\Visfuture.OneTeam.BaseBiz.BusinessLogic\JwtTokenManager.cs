﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Interfaces;
using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Core.Common.Helpers;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic;

public class JwtTokenManager(IConfiguration configuration)
    : IJwtTokenManager
{
    private readonly IConfiguration _configuration = configuration;

    private readonly Dictionary<Guid, string>
        _userRefreshTokens = []; // This stores userId-refreshToken pairs in memory.

    public string GenerateToken(Guid userId, string userName, Guid? tenantId)
    {
        var claims = new List<Claim>
    {
        new Claim("UserId", userId.ToString()),
        new Claim("UserName", userName),
    };

        if (tenantId.HasValue)
            claims.Add(new Claim("TenantId", tenantId.Value.ToString()));

        var key = new SymmetricSecurityKey(
        Encoding.UTF8.GetBytes(_configuration["JWT:key"]!)
);
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var token = new JwtSecurityToken(
            issuer: _configuration["JWT:Issuer"],
            audience: _configuration["JWT:Audience"],
            claims: claims,
            expires: DateTime.UtcNow.AddMinutes(
                int.TryParse(_configuration["JWT:DurationInMinutes"], out var duration) ? duration : 15
            ),
            signingCredentials: creds
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    public string GenerateRefreshToken(Guid userId)
    {
        byte[] randomNumber = new byte[32];
        using RandomNumberGenerator rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomNumber);
        string randomBase64 = Convert.ToBase64String(randomNumber);

        // only store the hash of the refresh token
        if (!_userRefreshTokens.ContainsKey(userId))
            _userRefreshTokens.Add(userId, HashUtils.Hash(randomBase64));
        else
            _userRefreshTokens[userId] = HashUtils.Hash(randomBase64);
        return randomBase64;
    }

    public bool ValidateRefreshToken(Guid userId, string refreshToken)
    {
        // Check if the refresh token is valid for the given userId
        return _userRefreshTokens.TryGetValue(userId, out string? storedToken) &&
               storedToken.Equals(HashUtils.Hash(refreshToken));
    }

    public void InvalidateRefreshToken(Guid userId)
    {
        _userRefreshTokens.Remove(userId); // Remove the refresh token hash for the user
    }

    public EntityResponse<UserJwtDto> RenewJwtToken(Guid userId, string refreshToken)
    {
        if (!ValidateRefreshToken(userId, refreshToken))
            return EntityResponse<UserJwtDto>.Failed("Refresh token is invalid");
        return new EntityResponse<UserJwtDto>(new UserJwtDto
        {
            JwtToken = GenerateToken(userId),
            RefreshToken = GenerateRefreshToken(userId),
            UserId = userId
        });
    }
    public string GenerateToken(Guid userId)
    {
        return GenerateToken(userId, "Unknown", null);
    }
}