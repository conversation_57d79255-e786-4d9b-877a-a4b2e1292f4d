﻿"restore":{"projectUniqueName":"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.Api\\Visfuture.OneTeam.Project.Api.csproj","projectName":"Visfuture.OneTeam.Project.Api","projectPath":"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.Api\\Visfuture.OneTeam.Project.Api.csproj","outputPath":"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.Api\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages","C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages","C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"],"originalTargetFrameworks":["net8.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0":{"targetAlias":"net8.0","projectReferences":{"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.BaseBiz.BusinessLogic\\Visfuture.OneTeam.BaseBiz.BusinessLogic.csproj":{"projectPath":"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.BaseBiz.BusinessLogic\\Visfuture.OneTeam.BaseBiz.BusinessLogic.csproj"},"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.ExternalService\\Visfuture.OneTeam.Project.ExternalService.csproj":{"projectPath":"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.ExternalService\\Visfuture.OneTeam.Project.ExternalService.csproj"},"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.InternalService\\Visfuture.OneTeam.Project.InternalService.csproj":{"projectPath":"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.InternalService\\Visfuture.OneTeam.Project.InternalService.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net8.0":{"targetAlias":"net8.0","dependencies":{"Newtonsoft.Json":{"target":"Package","version":"[13.0.3, )"},"Swashbuckle.AspNetCore":{"target":"Package","version":"[6.6.2, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}