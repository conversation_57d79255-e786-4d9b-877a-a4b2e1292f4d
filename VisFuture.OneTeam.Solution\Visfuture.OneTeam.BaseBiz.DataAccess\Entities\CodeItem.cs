﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.DataAccess.Entities;

public partial class CodeItem : TenantEntity
{
    public Guid CodeTypeId { get; set; }

    public string Name { get; set; } = null!;

    public string Value { get; set; } = null!;

    public string? SeqNo { get; set; }

    public Guid? SuperiorId { get; set; }

    public string? ItemField1 { get; set; }

    public string? ItemField2 { get; set; }

    public string? ItemField3 { get; set; }

    public virtual ICollection<CodeItemAttribute> CodeItemAttributes { get; set; } = [];

    public virtual CodeType CodeType { get; set; } = null!;
}
