﻿using System.Security.Cryptography;
using System.Text;

namespace Visfuture.OneTeam.Core.Common.Helpers;

public static class HashUtils
{
    public static string Hash(string input)
    {
        return Convert.ToBase64String(SHA256.HashData(Encoding.UTF8.GetBytes(input)));
    }

    public static string HashHex(string input)
    {
        return Convert.ToHexString(SHA256.HashData(Encoding.UTF8.GetBytes(input)));
    }
}