[{"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "AddCompanyAsync", "RelativePath": "AddCompany", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.CompanyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "AddCompaniesAsync", "RelativePath": "AddCompanyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.CompanyDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "AddContactAsync", "RelativePath": "AddContact", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "contactDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContactDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "AddContactsAsync", "RelativePath": "AddContactList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContactDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddContractAsync", "RelativePath": "AddContract", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "contractDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContractDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddContractListAsync", "RelativePath": "AddContractList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContractDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddProjectAsync", "RelativePath": "AddProject", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddProjectDocumentAsync", "RelativePath": "AddProjectDocument", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectDocumentDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDocumentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddProjectDocumentListAsync", "RelativePath": "AddProjectDocumentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDocumentDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddProjectListAsync", "RelativePath": "AddProjectList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddProjectPaymentFactAsync", "RelativePath": "AddProjectPaymentFact", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectPaymentFactDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectPaymentFactDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddProjectPaymentFactListAsync", "RelativePath": "AddProjectPaymentFactList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectPaymentFactDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddProjectPaymentPlanAsync", "RelativePath": "AddProjectPaymentPlan", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectPaymentPlanDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectPaymentPlanDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddProjectPaymentPlanListAsync", "RelativePath": "AddProjectPaymentPlanList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectPaymentPlanDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddRoleAssignmentAsync", "RelativePath": "AddProjectRoleAssignment", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleAssignmentDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectRoleAssignmentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddRoleAssignmentListAsync", "RelativePath": "AddProjectRoleAssignmentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectRoleAssignmentDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddRelatedPartyAsync", "RelativePath": "AddRelatedParty", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "relatedPartyDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.RelatedPartyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddRelatedPartyListAsync", "RelativePath": "AddRelatedPartyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.RelatedPartyDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddTaskAsync", "RelativePath": "AddTask", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.TaskDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddTaskListAsync", "RelativePath": "AddTaskList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.TaskDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddTimeLogAsync", "RelativePath": "AddTimeLog", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "timeLogDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.TimeLogDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "AddTimeLogListAsync", "RelativePath": "AddTimeLogList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.TimeLogDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "GetCompanyAsync", "RelativePath": "CompanyDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "QueryCompanyListAsync", "RelativePath": "CompanyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.CompanyDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "GetContactAsync", "RelativePath": "ContactDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "contactId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "QueryContactListAsync", "RelativePath": "ContactList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContactDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "GetContractAsync", "RelativePath": "ContractDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "contractId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "QueryContractListAsync", "RelativePath": "ContractList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContractDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "DeleteCompanyAsync", "RelativePath": "DeleteCompany", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "DeleteCompaniesAsync", "RelativePath": "DeleteCompanyList", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.CompanyDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "DeleteContactAsync", "RelativePath": "DeleteContact", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "contactId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "DeleteContactsAsync", "RelativePath": "DeleteContactList", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContactDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteContractAsync", "RelativePath": "DeleteContract", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "contractId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteContractListAsync", "RelativePath": "DeleteContractList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContractDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteProjectAsync", "RelativePath": "DeleteProject", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteProjectDocumentAsync", "RelativePath": "DeleteProjectDocument", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectDocumentId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteProjectDocumentListAsync", "RelativePath": "DeleteProjectDocumentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDocumentDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteProjectListAsync", "RelativePath": "DeleteProjectList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteProjectPaymentFactAsync", "RelativePath": "DeleteProjectPaymentFact", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectPaymentFactId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteProjectPaymentFactListAsync", "RelativePath": "DeleteProjectPaymentFactList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectPaymentFactDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteProjectPaymentPlanAsync", "RelativePath": "DeleteProjectPaymentPlan", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectPaymentPlanId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteProjectPaymentPlanListAsync", "RelativePath": "DeleteProjectPaymentPlanList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectPaymentPlanDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteRoleAssignmentAsync", "RelativePath": "DeleteProjectRoleAssignment", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectRoleAssignmentId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteRoleAssignmentListAsync", "RelativePath": "DeleteProjectRoleAssignmentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectRoleAssignmentDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteRelatedPartyAsync", "RelativePath": "DeleteRelatedParty", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "relatedPartyId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteRelatedPartyListAsync", "RelativePath": "DeleteRelatedPartyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.RelatedPartyDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteTaskAsync", "RelativePath": "DeleteTask", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteTaskListAsync", "RelativePath": "DeleteTaskList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.TaskDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteTimeLogAsync", "RelativePath": "DeleteTimeLog", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "timeLogId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "DeleteTimeLogListAsync", "RelativePath": "DeleteTimeLogList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.TimeLogDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "ExportCompanyExcel", "RelativePath": "ExportCompanyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.CompanyDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "ExportContactExcel", "RelativePath": "ExportContactList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContactDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "ExportContractListAsync", "RelativePath": "ExportContractList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContractDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "ExportProjectDocumentListAsync", "RelativePath": "ExportProjectDocumentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDocumentDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "ExportProjectListAsync", "RelativePath": "ExportProjectList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "ExportRoleAssignmentListAsync", "RelativePath": "ExportProjectRoleAssignmentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectRoleAssignmentDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "ExportRelatedPartyListAsync", "RelativePath": "ExportRelatedPartyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.RelatedPartyDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "ExportTimeLogListAsync", "RelativePath": "ExportTimeLogList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.TimeLogDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ExternalController", "Method": "GetWorkTimeByTicketIdExt", "RelativePath": "External/GetWorkTimeByTicketId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ticketId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "GetContactsByEmailAsync", "RelativePath": "GetContactsByEmail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "ImportCompanyExcel", "RelativePath": "ImportCompanyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "ImportContactExcel", "RelativePath": "ImportContactList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "ImportContractListAsync", "RelativePath": "ImportContractList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "ImportProjectDocumentListAsync", "RelativePath": "ImportProjectDocumentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "ImportProjectListAsync", "RelativePath": "ImportProjectList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "ImportRoleAssignmentListAsync", "RelativePath": "ImportProjectRoleAssignmentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "ImportRelatedPartyListAsync", "RelativePath": "ImportRelatedPartyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "ImportTimeLogListAsync", "RelativePath": "ImportTimeLogList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.B64File", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "GetProjectAsync", "RelativePath": "ProjectDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "GetProjectDocumentAsync", "RelativePath": "ProjectDocumentDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectDocumentId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "QueryProjectDocumentListAsync", "RelativePath": "ProjectDocumentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDocumentDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "QueryProjectListAsync", "RelativePath": "ProjectList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "GetProjectPaymentFactAsync", "RelativePath": "ProjectPaymentFactDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectPaymentFactId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "QueryProjectPaymentFactListAsync", "RelativePath": "ProjectPaymentFactList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectPaymentFactDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "GetProjectPaymentPlanAsync", "RelativePath": "ProjectPaymentPlanDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectPaymentPlanId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "QueryProjectPaymentPlanListAsync", "RelativePath": "ProjectPaymentPlanList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectPaymentPlanDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "GetRoleAssignmentAsync", "RelativePath": "ProjectRoleAssignmentDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleAssignmentId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "QueryRoleAssignmentListAsync", "RelativePath": "ProjectRoleAssignmentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectRoleAssignmentDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "GetRelatedPartyAsync", "RelativePath": "RelatedPartyDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "relatedPartyId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "QueryRelatedPartyListAsync", "RelativePath": "RelatedPartyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.RelatedPartyDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "GetTaskAsync", "RelativePath": "TaskDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "QueryTaskListAsync", "RelativePath": "TaskList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.TaskDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "GetTimeLogAsync", "RelativePath": "TimeLogDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "timeLogId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "QueryTimeLogListAsync", "RelativePath": "TimeLogList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Project.BusinessLogic.Requests.InvoiceQueueQuery", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "UpdateCompanyAsync", "RelativePath": "UpdateCompany", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.CompanyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "UpdateCompaniesAsync", "RelativePath": "UpdateCompanyList", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.CompanyDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "UpdateContactAsync", "RelativePath": "UpdateContact", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "contactDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContactDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.CompanyController", "Method": "UpdateContactsAsync", "RelativePath": "UpdateContactList", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContactDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateContractAsync", "RelativePath": "UpdateContract", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "contractDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContractDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateContractListAsync", "RelativePath": "UpdateContractList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ContractDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateProjectAsync", "RelativePath": "UpdateProject", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateProjectDocumentAsync", "RelativePath": "UpdateProjectDocument", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectDocumentDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDocumentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateProjectDocumentListAsync", "RelativePath": "UpdateProjectDocumentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDocumentDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateProjectListAsync", "RelativePath": "UpdateProjectList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateProjectPaymentFactAsync", "RelativePath": "UpdateProjectPaymentFact", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectPaymentFactDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectPaymentFactDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateProjectPaymentFactListAsync", "RelativePath": "UpdateProjectPaymentFactList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectPaymentFactDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateProjectPaymentPlanAsync", "RelativePath": "UpdateProjectPaymentPlan", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectPaymentPlanDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectPaymentPlanDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateProjectPaymentPlanListAsync", "RelativePath": "UpdateProjectPaymentPlanList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectPaymentPlanDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateRoleAssignmentAsync", "RelativePath": "UpdateProjectRoleAssignment", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleAssignmentDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectRoleAssignmentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateRoleAssignmentListAsync", "RelativePath": "UpdateProjectRoleAssignmentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.ProjectRoleAssignmentDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateRelatedPartyAsync", "RelativePath": "UpdateRelatedParty", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "relatedPartyDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.RelatedPartyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateRelatedPartyListAsync", "RelativePath": "UpdateRelatedPartyList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.RelatedPartyDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateTaskAsync", "RelativePath": "UpdateTask", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.TaskDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateTaskListAsync", "RelativePath": "UpdateTaskList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.TaskDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateTimeLogAsync", "RelativePath": "UpdateTimeLog", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "timeLogDto", "Type": "Visfuture.OneTeam.Project.BusinessLogic.DTOs.TimeLogDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.ProjectController", "Method": "UpdateTimeLogListAsync", "RelativePath": "UpdateTimeLogList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[[Visfuture.OneTeam.Project.BusinessLogic.DTOs.TimeLogDto, Visfuture.OneTeam.Project.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Visfuture.OneTeam.Project.Api.WeatherForecast, Visfuture.OneTeam.Project.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}, {"ContainingType": "Visfuture.OneTeam.Project.Api.Controllers.WeatherForecastController", "Method": "PlaceOrder", "RelativePath": "WeatherForecast", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}]