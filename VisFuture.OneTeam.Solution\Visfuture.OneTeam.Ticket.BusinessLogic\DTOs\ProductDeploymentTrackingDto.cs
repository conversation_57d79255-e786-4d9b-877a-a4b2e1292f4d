﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.Ticket.BusinessLogic.DTOs;

public class ProductDeploymentTrackingDto : TenantBaseDto
{
    public Guid ProductReleaseId { get; set; }

    public Guid ProductId { get; set; }

    public string ClientId { get; set; } = null!;

    public string? Status { get; set; }

    public string? SiteId { get; set; }

    public string? SiteName { get; set; }

    public DateTime? DateDeployed { get; set; }

    public string? TargetEnvironment { get; set; }

    public string? Description { get; set; }

}