{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Backend": "BaseBiz", "AllowedHosts": "*", "ConnectionStrings": {"SqlConnection": "Server=vfsql01.mayerglobal.com;Database=OneTeam.BaseBiz;User Id=sa;Password=*******;MultipleActiveResultSets=True;TrustServerCertificate=True;"}, "Minio": {"Endpoint": "*************:9000", "AccessKey": "o3gfDF7loOFVhEWzab25", "SecretKey": "mD7dcjyLP2yjUHq88lgp3LFXtsDoQLVqSETBhfqS"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": "Information", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/applog-.txt", "rollingInterval": "Day"}}], "Enrich": ["FromLogContext", "WithMachineName"], "Properties": {"ApplicationName": "Your ASP.NET Core App"}}, "JwtSettings": {"validIssuer": "OneTeam", "validAudience": "https://localhost:7250", "expires": 5}, "JWT": {"key": "C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4", "Issuer": "SecureApi", "Audience": "SecureApiUser", "DurationInMinutes": 1}}