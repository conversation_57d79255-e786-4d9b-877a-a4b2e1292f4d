﻿using Mapster;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.DataAccess.Entities;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.Mapper;

public class MapsterConfig
{
    public static void RegisterMappings()
    {
        TypeAdapterConfig<UserAccount, UserDto>.NewConfig()
            .Map(dest => dest.Id, src => src.Id)
            //.Map(dest => dest.EmployeeId, src => src.EmployeeId)
            //.Map(dest => dest.RoleAssignments, src => src.Employee.RoleAssignments)
            .Map(dest => dest.Resource, src => TransformNUserAccount(src));
    }

    private static List<AccessResourceDto> TransformNUserAccount(UserAccount userAccount)
    {
        List<AccessResourceDto> resources = [];
        // foreach (RoleAssignment roleAssignment in userAccount!.Employee.RoleAssignments)
        // {
        //     foreach (RoleAccess roleAccess in roleAssignment.AssignableRole.Role.RoleAccesses)
        //     {
        //         resources.Add(new AccessResourceDto
        //         {
        //             Id = roleAccess.Resource.Id,
        //             Code = roleAccess.Resource.Code,
        //             //Name = roleAccess.Resource.Name,
        //             Description = roleAccess.Resource.Description,
        //         });
        //     }
        // }
        return resources;
    }
}