2025-06-25 14:35:49.430 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TimeLogTask - null null
2025-06-25 14:35:49.510 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:49.517 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 8.8354 ms
2025-06-25 14:35:49.525 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TimeLogTask - 204 null null 100.7517ms
2025-06-25 14:35:49.533 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TimeLogTask - application/json null
2025-06-25 14:35:49.544 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:49.555 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:49.560 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 14:35:49.789 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketType - null null
2025-06-25 14:35:49.795 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:49.797 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.2613 ms
2025-06-25 14:35:49.800 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketType - 204 null null 10.3845ms
2025-06-25 14:35:49.812 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketType - application/json null
2025-06-25 14:35:49.816 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:49.818 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:49.821 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 14:35:49.847 -04:00 [INF] Executed DbCommand (65ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 14:35:49.887 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 14:35:49.938 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-25 14:35:49.943 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:35:49.948 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 383.6406ms
2025-06-25 14:35:49.951 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:49.956 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 413.1543 ms
2025-06-25 14:35:49.963 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TimeLogTask - 200 null application/json; charset=utf-8 430.4764ms
2025-06-25 14:35:49.976 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-25 14:35:49.982 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:49.984 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 2.2458 ms
2025-06-25 14:35:49.987 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 10.5347ms
2025-06-25 14:35:50.001 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 110
2025-06-25 14:35:50.008 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:50.009 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.011 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 14:35:50.064 -04:00 [INF] Executed DbCommand (48ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 14:35:50.089 -04:00 [INF] Executed DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 14:35:50.110 -04:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 14:35:50.136 -04:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 14:35:50.144 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-25 14:35:50.161 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-25 14:35:50.165 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:35:50.166 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:35:50.168 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 153.9356ms
2025-06-25 14:35:50.169 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 344.2494ms
2025-06-25 14:35:50.171 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.173 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.175 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 167.5340 ms
2025-06-25 14:35:50.177 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 361.3195 ms
2025-06-25 14:35:50.180 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 178.5209ms
2025-06-25 14:35:50.182 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketType - 200 null application/json; charset=utf-8 370.5236ms
2025-06-25 14:35:50.187 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 96
2025-06-25 14:35:50.191 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-25 14:35:50.195 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:50.198 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:50.199 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.200 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.1794 ms
2025-06-25 14:35:50.202 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 14:35:50.204 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 13.2835ms
2025-06-25 14:35:50.212 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 168
2025-06-25 14:35:50.215 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:50.216 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.218 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 14:35:50.239 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 14:35:50.262 -04:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 14:35:50.282 -04:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 14:35:50.303 -04:00 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 14:35:50.308 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-25 14:35:50.334 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:35:50.337 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 130.8054ms
2025-06-25 14:35:50.340 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.342 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-25 14:35:50.344 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 148.8544 ms
2025-06-25 14:35:50.348 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:35:50.350 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 163.1085ms
2025-06-25 14:35:50.351 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 130.2518ms
2025-06-25 14:35:50.356 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketStatus - null null
2025-06-25 14:35:50.357 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.361 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:50.362 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 147.0980 ms
2025-06-25 14:35:50.363 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 2.3958 ms
2025-06-25 14:35:50.366 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 154.0085ms
2025-06-25 14:35:50.369 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketStatus - 204 null null 12.9841ms
2025-06-25 14:35:50.380 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketStatus - application/json null
2025-06-25 14:35:50.384 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:50.386 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.389 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 14:35:50.457 -04:00 [INF] Executed DbCommand (63ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 14:35:50.485 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 14:35:50.509 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-25 14:35:50.513 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:35:50.516 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 122.8789ms
2025-06-25 14:35:50.518 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.520 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 136.1125 ms
2025-06-25 14:35:50.523 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketStatus - 200 null application/json; charset=utf-8 143.5938ms
2025-06-25 14:35:50.530 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 86
2025-06-25 14:35:50.534 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:50.535 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.537 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 14:35:50.562 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 14:35:50.591 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 14:35:50.632 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-25 14:35:50.652 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:35:50.656 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 115.9124ms
2025-06-25 14:35:50.658 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.661 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 127.1091 ms
2025-06-25 14:35:50.664 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 134.1859ms
2025-06-25 14:35:50.674 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - null null
2025-06-25 14:35:50.678 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:50.679 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.4023 ms
2025-06-25 14:35:50.682 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - 204 null null 8.0166ms
2025-06-25 14:35:50.687 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - application/json null
2025-06-25 14:35:50.697 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:50.698 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.702 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 14:35:50.774 -04:00 [INF] Executed DbCommand (68ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 14:35:50.799 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 14:35:50.823 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-25 14:35:50.825 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:35:50.827 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 121.7469ms
2025-06-25 14:35:50.829 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.831 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 134.3891 ms
2025-06-25 14:35:50.834 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - 200 null application/json; charset=utf-8 146.9191ms
2025-06-25 14:35:50.838 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 89
2025-06-25 14:35:50.841 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:50.842 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.844 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 14:35:50.871 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 14:35:50.900 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 14:35:50.928 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-25 14:35:50.943 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:35:50.945 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 99.1405ms
2025-06-25 14:35:50.947 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.949 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 108.0681 ms
2025-06-25 14:35:50.951 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 113.3371ms
2025-06-25 14:35:50.954 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-25 14:35:50.958 -04:00 [INF] CORS policy execution successful.
2025-06-25 14:35:50.959 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:50.960 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 14:35:51.041 -04:00 [INF] Executed DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 14:35:51.070 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 14:35:51.162 -04:00 [INF] Executed DbCommand (89ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-25 14:35:51.168 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:35:51.171 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 208.6212ms
2025-06-25 14:35:51.173 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 14:35:51.174 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 216.9741 ms
2025-06-25 14:35:51.178 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 223.9645ms
2025-06-25 15:09:11.082 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketType - null null
2025-06-25 15:09:11.087 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketStatus - null null
2025-06-25 15:09:11.113 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.098 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - null null
2025-06-25 15:09:11.107 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketSource - null null
2025-06-25 15:09:11.110 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-25 15:09:11.123 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.124 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 11.0016 ms
2025-06-25 15:09:11.127 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.130 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.134 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.135 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 12.3642 ms
2025-06-25 15:09:11.137 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketType - 204 null null 55.8338ms
2025-06-25 15:09:11.139 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 11.2023 ms
2025-06-25 15:09:11.141 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 10.3228 ms
2025-06-25 15:09:11.143 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 9.0504 ms
2025-06-25 15:09:11.162 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketStatus - 204 null null 74.8383ms
2025-06-25 15:09:11.167 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketType - application/json null
2025-06-25 15:09:11.170 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - 204 null null 71.6832ms
2025-06-25 15:09:11.172 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketSource - 204 null null 65.479ms
2025-06-25 15:09:11.175 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 64.4387ms
2025-06-25 15:09:11.179 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketStatus - application/json null
2025-06-25 15:09:11.184 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.187 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-06-25 15:09:11.190 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - application/json null
2025-06-25 15:09:11.195 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketSource - application/json null
2025-06-25 15:09:11.198 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.199 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.203 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.206 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.210 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.211 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.213 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:09:11.214 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.215 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.216 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.217 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:09:11.222 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:09:11.223 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:09:11.224 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:09:11.397 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:09:11.431 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:09:11.456 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:09:11.481 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:09:11.508 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:09:11.511 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:09:11.534 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:09:11.538 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:09:11.568 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:09:11.568 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-06-25 15:09:11.575 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:09:11.578 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 348.8139ms
2025-06-25 15:09:11.580 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.581 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 378.3027 ms
2025-06-25 15:09:11.584 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 397.522ms
2025-06-25 15:09:11.607 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-25 15:09:11.607 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-25 15:09:11.610 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:09:11.613 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:09:11.615 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:09:11.621 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 390.0527ms
2025-06-25 15:09:11.622 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 388.8059ms
2025-06-25 15:09:11.623 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.625 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.626 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 419.7969 ms
2025-06-25 15:09:11.627 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 417.4533 ms
2025-06-25 15:09:11.629 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Priority - 200 null application/json; charset=utf-8 438.9326ms
2025-06-25 15:09:11.631 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketSource - 200 null application/json; charset=utf-8 436.3885ms
2025-06-25 15:09:11.639 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-25 15:09:11.642 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.643 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.5507 ms
2025-06-25 15:09:11.644 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-25 15:09:11.646 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 7.2614ms
2025-06-25 15:09:11.649 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:09:11.649 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-25 15:09:11.654 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-25 15:09:11.654 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 434.267ms
2025-06-25 15:09:11.657 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.660 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.661 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.663 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 5.4737 ms
2025-06-25 15:09:11.664 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.665 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 481.3210 ms
2025-06-25 15:09:11.668 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 18.4719ms
2025-06-25 15:09:11.669 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:09:11.671 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketType - 200 null application/json; charset=utf-8 503.6964ms
2025-06-25 15:09:11.675 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-25 15:09:11.680 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-25 15:09:11.683 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.687 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.688 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.687 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.689 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:09:11.691 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:09:11.725 -04:00 [INF] Executed DbCommand (48ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:09:11.725 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:09:11.725 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:09:11.745 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-25 15:09:11.749 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:09:11.750 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:09:11.751 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 524.4353ms
2025-06-25 15:09:11.756 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.758 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 560.0162 ms
2025-06-25 15:09:11.760 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=TicketStatus - 200 null application/json; charset=utf-8 580.7907ms
2025-06-25 15:09:11.760 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:09:11.760 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:09:11.764 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-25 15:09:11.775 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:09:11.776 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.777 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-25 15:09:11.777 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:09:11.790 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-25 15:09:11.794 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-25 15:09:11.794 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:09:11.799 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 106.3598ms
2025-06-25 15:09:11.799 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:09:11.801 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.803 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:09:11.803 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 126.254ms
2025-06-25 15:09:11.804 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 117.3039 ms
2025-06-25 15:09:11.808 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.811 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 130.3545ms
2025-06-25 15:09:11.812 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 151.8938 ms
2025-06-25 15:09:11.815 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:09:11.818 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 164.8054ms
2025-06-25 15:09:11.820 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 125.2881ms
2025-06-25 15:09:11.825 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.826 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 142.9044 ms
2025-06-25 15:09:11.828 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 153.574ms
2025-06-25 15:09:11.829 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:09:11.864 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-25 15:09:11.889 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:09:11.891 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 108.9018ms
2025-06-25 15:09:11.893 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:09:11.894 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 119.4509 ms
2025-06-25 15:09:11.896 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 132.0499ms
2025-06-25 15:10:18.034 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-25 15:10:18.216 -04:00 [INF] Now listening on: http://localhost:5275
2025-06-25 15:10:18.273 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 15:10:18.275 -04:00 [INF] Hosting environment: Development
2025-06-25 15:10:18.276 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-06-25 15:10:55.823 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-25 15:10:56.020 -04:00 [INF] Now listening on: http://localhost:5275
2025-06-25 15:10:56.088 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 15:10:56.109 -04:00 [INF] Hosting environment: Development
2025-06-25 15:10:56.125 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-06-25 15:11:20.145 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - null null
2025-06-25 15:11:20.266 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:11:20.273 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 82.5358 ms
2025-06-25 15:11:20.302 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - 204 null null 165.612ms
2025-06-25 15:11:20.328 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - application/json null
2025-06-25 15:11:20.345 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:11:20.519 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:11:20.570 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:11:23.467 -04:00 [INF] Executed DbCommand (118ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:11:23.551 -04:00 [INF] Executed DbCommand (54ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:11:24.198 -04:00 [INF] Executed DbCommand (84ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-25 15:11:24.399 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:11:24.468 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 3884.5651ms
2025-06-25 15:11:24.475 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:11:24.481 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 4137.8700 ms
2025-06-25 15:11:24.503 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - 200 null application/json; charset=utf-8 4174.8772ms
2025-06-25 15:11:24.514 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-25 15:11:24.524 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:11:24.526 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 2.6165 ms
2025-06-25 15:11:24.530 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 15.8937ms
2025-06-25 15:11:24.543 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-25 15:11:24.552 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:11:24.558 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:11:24.571 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:11:24.625 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:11:24.658 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:11:24.838 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-25 15:11:24.870 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:11:24.891 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 315.9605ms
2025-06-25 15:11:24.896 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:11:24.899 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 347.3186 ms
2025-06-25 15:11:24.904 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 361.0639ms
2025-06-25 15:14:27.241 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-06-25 15:14:27.246 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:27.248 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 1.7071 ms
2025-06-25 15:14:27.251 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 9.6362ms
2025-06-25 15:14:27.261 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 95
2025-06-25 15:14:27.265 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:27.270 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:27.276 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:27.392 -04:00 [INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:27.420 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:27.553 -04:00 [INF] Executed DbCommand (93ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:27.565 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:27.572 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 293.1026ms
2025-06-25 15:14:27.574 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:27.576 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 310.9932 ms
2025-06-25 15:14:27.579 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 317.1088ms
2025-06-25 15:14:27.592 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 90
2025-06-25 15:14:27.596 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:27.598 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:27.600 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:27.665 -04:00 [INF] Executed DbCommand (61ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:27.701 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:27.800 -04:00 [INF] Executed DbCommand (38ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-25 15:14:27.805 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:27.807 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 204.252ms
2025-06-25 15:14:27.809 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:27.811 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 214.3597 ms
2025-06-25 15:14:27.814 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 222.1518ms
2025-06-25 15:14:35.804 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationEmployeeList - null null
2025-06-25 15:14:35.810 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-06-25 15:14:35.825 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:35.816 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - null null
2025-06-25 15:14:35.813 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - null null
2025-06-25 15:14:35.827 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-06-25 15:14:35.827 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-06-25 15:14:35.835 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:35.836 -04:00 [INF] HTTP OPTIONS /OrganizationEmployeeList responded 204 in 11.0406 ms
2025-06-25 15:14:35.840 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:35.853 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 18.2683 ms
2025-06-25 15:14:35.860 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationEmployeeList - 204 null null 56.6921ms
2025-06-25 15:14:35.866 -04:00 [INF] HTTP OPTIONS /RoleAssignmentList responded 204 in 25.6255 ms
2025-06-25 15:14:35.871 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 61.4494ms
2025-06-25 15:14:35.885 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - application/json 158
2025-06-25 15:14:35.845 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:35.848 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:35.852 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:35.890 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - 204 null null 74.2452ms
2025-06-25 15:14:35.896 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 148
2025-06-25 15:14:35.899 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:35.900 -04:00 [INF] HTTP OPTIONS /AssignableRoleList responded 204 in 55.9054 ms
2025-06-25 15:14:35.901 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 53.4720 ms
2025-06-25 15:14:35.903 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 51.4295 ms
2025-06-25 15:14:35.909 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - application/json 164
2025-06-25 15:14:35.913 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:35.914 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:35.918 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - 204 null null 105.0052ms
2025-06-25 15:14:35.921 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 94.1413ms
2025-06-25 15:14:35.924 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 97.2079ms
2025-06-25 15:14:35.932 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:35.934 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:35.940 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AssignableRoleList - application/json 162
2025-06-25 15:14:35.940 -04:00 [INF] Route matched with {action = "GetOrganizationEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:35.944 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 51
2025-06-25 15:14:35.949 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 18
2025-06-25 15:14:35.950 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:35.952 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:35.955 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:35.960 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:35.964 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:35.973 -04:00 [INF] Route matched with {action = "QueryRoleAssignmentList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleAssignmentListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:35.972 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:35.970 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:35.973 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:35.977 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:35.980 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:35.983 -04:00 [INF] Route matched with {action = "QueryAssignableRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAssignableRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:36.012 -04:00 [INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:36.053 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:36.091 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:36.138 -04:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:36.177 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:36.209 -04:00 [INF] Executed DbCommand (48ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:36.237 -04:00 [INF] Executed DbCommand (54ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:36.259 -04:00 [INF] Executed DbCommand (46ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:36.276 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:36.301 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:36.304 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:36.321 -04:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:36.337 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:36.371 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:36.384 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 415.2222ms
2025-06-25 15:14:36.393 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:36.396 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 483.4788 ms
2025-06-25 15:14:36.399 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 503.0945ms
2025-06-25 15:14:36.494 -04:00 [INF] Executed DbCommand (37ms) [Parameters=[@__organizationId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Id], [o].[OrganizationId], [o].[EmployeeId], [o].[TenantId], [e].[Name] AS [EmployeeName], [o].[CreateAt], [o].[CreateBy], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationEmployees] AS [o]
INNER JOIN [Employee] AS [e] ON [o].[EmployeeId] = [e].[Id]
WHERE [o].[OrganizationId] = @__organizationId_0
2025-06-25 15:14:36.507 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationEmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:36.509 -04:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:36.509 -04:00 [INF] Executed DbCommand (45ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-25 15:14:36.512 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 555.0658ms
2025-06-25 15:14:36.513 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:36.516 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:36.517 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:36.519 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 534.3001ms
2025-06-25 15:14:36.520 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 538.0104ms
2025-06-25 15:14:36.521 -04:00 [INF] HTTP POST /OrganizationEmployeeList responded 200 in 621.8297 ms
2025-06-25 15:14:36.523 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:36.524 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:36.527 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - 200 null application/json; charset=utf-8 641.4556ms
2025-06-25 15:14:36.528 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 564.0226 ms
2025-06-25 15:14:36.529 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 568.9088 ms
2025-06-25 15:14:36.536 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 587.399ms
2025-06-25 15:14:36.539 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 594.4403ms
2025-06-25 15:14:36.627 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[OrganizationId], [a].[RoleId], [a].[IsInheritable], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AssignableRoles] AS [a]
2025-06-25 15:14:36.632 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[AssignableRoleId], [r].[EmployeeId], [r].[IsInheritable], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [RoleAssignments] AS [r]
2025-06-25 15:14:36.633 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AssignableRoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:36.636 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleAssignmentListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:36.639 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 651.2805ms
2025-06-25 15:14:36.640 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api) in 664.3918ms
2025-06-25 15:14:36.641 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:36.642 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:36.644 -04:00 [INF] HTTP POST /AssignableRoleList responded 200 in 689.2165 ms
2025-06-25 15:14:36.645 -04:00 [INF] HTTP POST /RoleAssignmentList responded 200 in 712.9368 ms
2025-06-25 15:14:36.710 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AssignableRoleList - 200 null application/json; charset=utf-8 770.3721ms
2025-06-25 15:14:36.710 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - 200 null application/json; charset=utf-8 801.3765ms
2025-06-25 15:14:40.732 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - null null
2025-06-25 15:14:40.732 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - null null
2025-06-25 15:14:40.736 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:40.740 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:40.741 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyDetails responded 204 in 4.5271 ms
2025-06-25 15:14:40.742 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyDetails responded 204 in 2.1083 ms
2025-06-25 15:14:40.744 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - 204 null null 12.2961ms
2025-06-25 15:14:40.747 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - 204 null null 15.8389ms
2025-06-25 15:14:40.756 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - application/json null
2025-06-25 15:14:40.759 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:40.760 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:40.767 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyDetails", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyDetails(System.Guid, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:40.792 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:40.818 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:40.855 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [o].[Id], [o].[Code], [o].[CreateAt], [o].[CreateBy], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Description], [o].[IsActive], [o].[MainType], [o].[Name], [o].[SubType], [o].[SuperiorId], [o].[TenantId], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] = @__id_0
2025-06-25 15:14:40.870 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:40.876 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api) in 106.0302ms
2025-06-25 15:14:40.878 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - application/json null
2025-06-25 15:14:40.880 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:40.884 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:40.885 -04:00 [INF] HTTP GET /OrganizationHierarchyDetails responded 200 in 126.0206 ms
2025-06-25 15:14:40.886 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:40.888 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - 200 null application/json; charset=utf-8 132.294ms
2025-06-25 15:14:40.889 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyDetails", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyDetails(System.Guid, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:40.894 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationEmployeeList - null null
2025-06-25 15:14:40.894 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AssignableRoleList - application/json 162
2025-06-25 15:14:40.894 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 148
2025-06-25 15:14:40.894 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - application/json 164
2025-06-25 15:14:40.900 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:40.903 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:40.907 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:40.910 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:40.911 -04:00 [INF] HTTP OPTIONS /OrganizationEmployeeList responded 204 in 10.6274 ms
2025-06-25 15:14:40.912 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:40.913 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:40.913 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:40.917 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationEmployeeList - 204 null null 22.9748ms
2025-06-25 15:14:40.918 -04:00 [INF] Route matched with {action = "QueryAssignableRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAssignableRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:40.920 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:40.921 -04:00 [INF] Route matched with {action = "QueryRoleAssignmentList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleAssignmentListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:40.925 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - application/json 158
2025-06-25 15:14:40.932 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:40.934 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:40.937 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:40.939 -04:00 [INF] Route matched with {action = "GetOrganizationEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:40.956 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:40.972 -04:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:40.972 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:40.972 -04:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:40.992 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:41.002 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:41.023 -04:00 [INF] Executed DbCommand (40ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [o].[Id], [o].[Code], [o].[CreateAt], [o].[CreateBy], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Description], [o].[IsActive], [o].[MainType], [o].[Name], [o].[SubType], [o].[SuperiorId], [o].[TenantId], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] = @__id_0
2025-06-25 15:14:41.029 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:41.034 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api) in 137.5351ms
2025-06-25 15:14:41.038 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.040 -04:00 [INF] HTTP GET /OrganizationHierarchyDetails responded 200 in 156.5523 ms
2025-06-25 15:14:41.044 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - 200 null application/json; charset=utf-8 166.6529ms
2025-06-25 15:14:41.053 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - application/json 158
2025-06-25 15:14:41.056 -04:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:41.056 -04:00 [INF] Executed DbCommand (67ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:41.054 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-06-25 15:14:41.070 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:41.067 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:41.077 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:41.079 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 149.8559ms
2025-06-25 15:14:41.080 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.081 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 4.2062 ms
2025-06-25 15:14:41.082 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.084 -04:00 [INF] Route matched with {action = "GetOrganizationEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:41.086 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 32.0817ms
2025-06-25 15:14:41.087 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 180.8140 ms
2025-06-25 15:14:41.093 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - null null
2025-06-25 15:14:41.095 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 200.8581ms
2025-06-25 15:14:41.100 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:41.103 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - null null
2025-06-25 15:14:41.104 -04:00 [INF] HTTP OPTIONS /AssignableRoleList responded 204 in 4.1400 ms
2025-06-25 15:14:41.106 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:41.107 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:41.109 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - 204 null null 15.8971ms
2025-06-25 15:14:41.110 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:41.112 -04:00 [INF] HTTP OPTIONS /RoleAssignmentList responded 204 in 5.6693 ms
2025-06-25 15:14:41.116 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-06-25 15:14:41.121 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - 204 null null 17.9453ms
2025-06-25 15:14:41.125 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[AssignableRoleId], [r].[EmployeeId], [r].[IsInheritable], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [RoleAssignments] AS [r]
2025-06-25 15:14:41.124 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:41.128 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 148
2025-06-25 15:14:41.131 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleAssignmentListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:41.131 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 7.3048 ms
2025-06-25 15:14:41.135 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:41.137 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api) in 206.0644ms
2025-06-25 15:14:41.137 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:41.139 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 23.1591ms
2025-06-25 15:14:41.140 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.142 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.141 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:41.150 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AssignableRoleList - application/json 162
2025-06-25 15:14:41.151 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:41.152 -04:00 [INF] HTTP POST /RoleAssignmentList responded 200 in 242.6501 ms
2025-06-25 15:14:41.160 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:41.165 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.167 -04:00 [INF] Route matched with {action = "QueryAssignableRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAssignableRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:41.168 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[@__organizationId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Id], [o].[OrganizationId], [o].[EmployeeId], [o].[TenantId], [e].[Name] AS [EmployeeName], [o].[CreateAt], [o].[CreateBy], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationEmployees] AS [o]
INNER JOIN [Employee] AS [e] ON [o].[EmployeeId] = [e].[Id]
WHERE [o].[OrganizationId] = @__organizationId_0
2025-06-25 15:14:41.175 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationEmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:41.177 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 236.4441ms
2025-06-25 15:14:41.179 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__organizationId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Id], [o].[OrganizationId], [o].[EmployeeId], [o].[TenantId], [e].[Name] AS [EmployeeName], [o].[CreateAt], [o].[CreateBy], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationEmployees] AS [o]
INNER JOIN [Employee] AS [e] ON [o].[EmployeeId] = [e].[Id]
WHERE [o].[OrganizationId] = @__organizationId_0
2025-06-25 15:14:41.179 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.182 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationEmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:41.182 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:41.183 -04:00 [INF] HTTP POST /OrganizationEmployeeList responded 200 in 249.5851 ms
2025-06-25 15:14:41.185 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 95.8114ms
2025-06-25 15:14:41.190 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - 200 null application/json; charset=utf-8 265.1079ms
2025-06-25 15:14:41.192 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.196 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 18
2025-06-25 15:14:41.197 -04:00 [INF] HTTP POST /OrganizationEmployeeList responded 200 in 130.2848 ms
2025-06-25 15:14:41.198 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:41.201 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - 200 null application/json; charset=utf-8 306.485ms
2025-06-25 15:14:41.201 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:41.202 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[OrganizationId], [a].[RoleId], [a].[IsInheritable], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AssignableRoles] AS [a]
2025-06-25 15:14:41.204 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - 200 null application/json; charset=utf-8 150.6733ms
2025-06-25 15:14:41.210 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - application/json 164
2025-06-25 15:14:41.212 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.219 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:41.221 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AssignableRoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:41.234 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:41.231 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:41.231 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:41.244 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 317.2274ms
2025-06-25 15:14:41.246 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.251 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.252 -04:00 [INF] Route matched with {action = "QueryRoleAssignmentList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleAssignmentListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:41.254 -04:00 [INF] HTTP POST /AssignableRoleList responded 200 in 350.3630 ms
2025-06-25 15:14:41.266 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:41.269 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:41.273 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:41.276 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 114.0526ms
2025-06-25 15:14:41.279 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.282 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 146.1687 ms
2025-06-25 15:14:41.286 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:41.288 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 159.7801ms
2025-06-25 15:14:41.302 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:41.302 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AssignableRoleList - 200 null application/json; charset=utf-8 407.9762ms
2025-06-25 15:14:41.318 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:41.338 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:41.342 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:41.344 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 99.4887ms
2025-06-25 15:14:41.346 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.348 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 147.1751 ms
2025-06-25 15:14:41.352 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 156.0281ms
2025-06-25 15:14:41.361 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[OrganizationId], [a].[RoleId], [a].[IsInheritable], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AssignableRoles] AS [a]
2025-06-25 15:14:41.365 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AssignableRoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:41.368 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 197.4473ms
2025-06-25 15:14:41.370 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.373 -04:00 [INF] HTTP POST /AssignableRoleList responded 200 in 213.7470 ms
2025-06-25 15:14:41.406 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AssignableRoleList - 200 null application/json; charset=utf-8 256.3222ms
2025-06-25 15:14:41.436 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[AssignableRoleId], [r].[EmployeeId], [r].[IsInheritable], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [RoleAssignments] AS [r]
2025-06-25 15:14:41.440 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleAssignmentListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:41.442 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api) in 186.3117ms
2025-06-25 15:14:41.445 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:41.447 -04:00 [INF] HTTP POST /RoleAssignmentList responded 200 in 216.4245 ms
2025-06-25 15:14:41.501 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - 200 null application/json; charset=utf-8 290.6694ms
2025-06-25 15:14:43.164 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 51
2025-06-25 15:14:43.170 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:43.171 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:43.173 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:43.203 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:43.238 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:43.270 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-25 15:14:43.275 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:43.280 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 104.8146ms
2025-06-25 15:14:43.284 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:43.287 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 117.0341 ms
2025-06-25 15:14:43.291 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 126.6444ms
2025-06-25 15:14:45.573 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 51
2025-06-25 15:14:45.576 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:45.577 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:45.579 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:45.604 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:45.636 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:45.670 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-25 15:14:45.675 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:45.678 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 97.4178ms
2025-06-25 15:14:45.681 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:45.682 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 106.3403 ms
2025-06-25 15:14:45.685 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 111.9276ms
2025-06-25 15:14:46.311 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=40f8888e-a811-43e4-8e93-a10fa65e999d - null null
2025-06-25 15:14:46.311 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=40f8888e-a811-43e4-8e93-a10fa65e999d - null null
2025-06-25 15:14:46.315 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.318 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.319 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyDetails responded 204 in 4.1232 ms
2025-06-25 15:14:46.320 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyDetails responded 204 in 1.9890 ms
2025-06-25 15:14:46.323 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=40f8888e-a811-43e4-8e93-a10fa65e999d - 204 null null 11.6064ms
2025-06-25 15:14:46.325 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=40f8888e-a811-43e4-8e93-a10fa65e999d - 204 null null 13.9373ms
2025-06-25 15:14:46.330 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=40f8888e-a811-43e4-8e93-a10fa65e999d - application/json null
2025-06-25 15:14:46.337 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.338 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.339 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyDetails", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyDetails(System.Guid, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:46.369 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:46.398 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:46.433 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [o].[Id], [o].[Code], [o].[CreateAt], [o].[CreateBy], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Description], [o].[IsActive], [o].[MainType], [o].[Name], [o].[SubType], [o].[SuperiorId], [o].[TenantId], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] = @__id_0
2025-06-25 15:14:46.437 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:46.439 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api) in 97.3757ms
2025-06-25 15:14:46.440 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=40f8888e-a811-43e4-8e93-a10fa65e999d - application/json null
2025-06-25 15:14:46.441 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.445 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.446 -04:00 [INF] HTTP GET /OrganizationHierarchyDetails responded 200 in 109.3192 ms
2025-06-25 15:14:46.447 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.450 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=40f8888e-a811-43e4-8e93-a10fa65e999d - 200 null application/json; charset=utf-8 120.3875ms
2025-06-25 15:14:46.451 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyDetails", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyDetails(System.Guid, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:46.455 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-06-25 15:14:46.455 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - null null
2025-06-25 15:14:46.455 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - null null
2025-06-25 15:14:46.455 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationEmployeeList - null null
2025-06-25 15:14:46.461 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.464 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.467 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.470 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.471 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 9.9239 ms
2025-06-25 15:14:46.471 -04:00 [INF] HTTP OPTIONS /AssignableRoleList responded 204 in 7.8471 ms
2025-06-25 15:14:46.472 -04:00 [INF] HTTP OPTIONS /RoleAssignmentList responded 204 in 5.8124 ms
2025-06-25 15:14:46.473 -04:00 [INF] HTTP OPTIONS /OrganizationEmployeeList responded 204 in 3.7506 ms
2025-06-25 15:14:46.475 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 20.9369ms
2025-06-25 15:14:46.478 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - 204 null null 23.0557ms
2025-06-25 15:14:46.478 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:46.480 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - 204 null null 25.2582ms
2025-06-25 15:14:46.482 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationEmployeeList - 204 null null 27.4224ms
2025-06-25 15:14:46.486 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 148
2025-06-25 15:14:46.491 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AssignableRoleList - application/json 162
2025-06-25 15:14:46.497 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - application/json 164
2025-06-25 15:14:46.501 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - application/json 158
2025-06-25 15:14:46.504 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.507 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.510 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.513 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.514 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.515 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.516 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.517 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.519 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:46.520 -04:00 [INF] Route matched with {action = "QueryAssignableRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAssignableRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:46.522 -04:00 [INF] Route matched with {action = "QueryRoleAssignmentList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleAssignmentListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:46.523 -04:00 [INF] Route matched with {action = "GetOrganizationEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:46.524 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:46.549 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:46.555 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:46.555 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:46.555 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:46.566 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [o].[Id], [o].[Code], [o].[CreateAt], [o].[CreateBy], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Description], [o].[IsActive], [o].[MainType], [o].[Name], [o].[SubType], [o].[SuperiorId], [o].[TenantId], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] = @__id_0
2025-06-25 15:14:46.569 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:46.571 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api) in 113.6516ms
2025-06-25 15:14:46.573 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.574 -04:00 [INF] HTTP GET /OrganizationHierarchyDetails responded 200 in 129.5783 ms
2025-06-25 15:14:46.576 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=40f8888e-a811-43e4-8e93-a10fa65e999d - 200 null application/json; charset=utf-8 136.0364ms
2025-06-25 15:14:46.578 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:46.580 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:46.580 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 148
2025-06-25 15:14:46.582 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - application/json 158
2025-06-25 15:14:46.592 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.593 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:46.593 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:46.596 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.595 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.604 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:46.605 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.609 -04:00 [INF] Route matched with {action = "GetOrganizationEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:46.626 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:46.626 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__organizationId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Id], [o].[OrganizationId], [o].[EmployeeId], [o].[TenantId], [e].[Name] AS [EmployeeName], [o].[CreateAt], [o].[CreateBy], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationEmployees] AS [o]
INNER JOIN [Employee] AS [e] ON [o].[EmployeeId] = [e].[Id]
WHERE [o].[OrganizationId] = @__organizationId_0
2025-06-25 15:14:46.630 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:46.633 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationEmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:46.634 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 108.8662ms
2025-06-25 15:14:46.635 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 103.3563ms
2025-06-25 15:14:46.637 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.638 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:46.638 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:46.639 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.640 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 136.0522 ms
2025-06-25 15:14:46.646 -04:00 [INF] HTTP POST /OrganizationEmployeeList responded 200 in 133.0135 ms
2025-06-25 15:14:46.649 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 162.4866ms
2025-06-25 15:14:46.651 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - 200 null application/json; charset=utf-8 150.164ms
2025-06-25 15:14:46.655 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AssignableRoleList - application/json 162
2025-06-25 15:14:46.659 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - application/json 164
2025-06-25 15:14:46.662 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.665 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.666 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:46.672 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.666 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.675 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:46.676 -04:00 [INF] Route matched with {action = "QueryRoleAssignmentList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleAssignmentListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:46.681 -04:00 [INF] Route matched with {action = "QueryAssignableRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAssignableRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:46.688 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[AssignableRoleId], [r].[EmployeeId], [r].[IsInheritable], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [RoleAssignments] AS [r]
2025-06-25 15:14:46.688 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[OrganizationId], [a].[RoleId], [a].[IsInheritable], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AssignableRoles] AS [a]
2025-06-25 15:14:46.693 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleAssignmentListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:46.695 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AssignableRoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:46.696 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api) in 166.3418ms
2025-06-25 15:14:46.698 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 170.0677ms
2025-06-25 15:14:46.699 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.701 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.702 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[@__organizationId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Id], [o].[OrganizationId], [o].[EmployeeId], [o].[TenantId], [e].[Name] AS [EmployeeName], [o].[CreateAt], [o].[CreateBy], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationEmployees] AS [o]
INNER JOIN [Employee] AS [e] ON [o].[EmployeeId] = [e].[Id]
WHERE [o].[OrganizationId] = @__organizationId_0
2025-06-25 15:14:46.702 -04:00 [INF] HTTP POST /RoleAssignmentList responded 200 in 192.6795 ms
2025-06-25 15:14:46.704 -04:00 [INF] HTTP POST /AssignableRoleList responded 200 in 196.9335 ms
2025-06-25 15:14:46.705 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:46.707 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationEmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:46.713 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:46.715 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:46.717 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 106.0647ms
2025-06-25 15:14:46.723 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 115.5843ms
2025-06-25 15:14:46.726 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.727 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.728 -04:00 [INF] HTTP POST /OrganizationEmployeeList responded 200 in 133.3852 ms
2025-06-25 15:14:46.730 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 137.4840 ms
2025-06-25 15:14:46.732 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - 200 null application/json; charset=utf-8 149.8518ms
2025-06-25 15:14:46.736 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 155.6625ms
2025-06-25 15:14:46.751 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:46.751 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - 200 null application/json; charset=utf-8 254.2196ms
2025-06-25 15:14:46.751 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AssignableRoleList - 200 null application/json; charset=utf-8 260.4776ms
2025-06-25 15:14:46.759 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 51
2025-06-25 15:14:46.763 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 18
2025-06-25 15:14:46.765 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.769 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:46.770 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.771 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.773 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:46.774 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:46.809 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:46.809 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:46.826 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:46.834 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:46.850 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:46.854 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:46.870 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-25 15:14:46.870 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[AssignableRoleId], [r].[EmployeeId], [r].[IsInheritable], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [RoleAssignments] AS [r]
2025-06-25 15:14:46.873 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:46.875 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:46.875 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleAssignmentListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:46.877 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 100.4553ms
2025-06-25 15:14:46.880 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:46.881 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api) in 194.2688ms
2025-06-25 15:14:46.883 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.886 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 106.8962ms
2025-06-25 15:14:46.887 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.889 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 123.5179 ms
2025-06-25 15:14:46.890 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.891 -04:00 [INF] HTTP POST /RoleAssignmentList responded 200 in 226.5493 ms
2025-06-25 15:14:46.894 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 135.0143ms
2025-06-25 15:14:46.896 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 126.5133 ms
2025-06-25 15:14:46.905 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 142.366ms
2025-06-25 15:14:46.945 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - 200 null application/json; charset=utf-8 286.7559ms
2025-06-25 15:14:46.971 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[OrganizationId], [a].[RoleId], [a].[IsInheritable], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AssignableRoles] AS [a]
2025-06-25 15:14:46.974 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AssignableRoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:46.976 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 286.2201ms
2025-06-25 15:14:46.979 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:46.981 -04:00 [INF] HTTP POST /AssignableRoleList responded 200 in 319.1388 ms
2025-06-25 15:14:47.021 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AssignableRoleList - 200 null application/json; charset=utf-8 365.9905ms
2025-06-25 15:14:47.866 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - null null
2025-06-25 15:14:47.868 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - null null
2025-06-25 15:14:47.870 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:47.873 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:47.874 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyDetails responded 204 in 3.8789 ms
2025-06-25 15:14:47.875 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyDetails responded 204 in 2.0111 ms
2025-06-25 15:14:47.878 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - 204 null null 11.9744ms
2025-06-25 15:14:47.880 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - 204 null null 11.848ms
2025-06-25 15:14:47.886 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - application/json null
2025-06-25 15:14:47.893 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:47.895 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:47.897 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyDetails", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyDetails(System.Guid, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:47.925 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:47.951 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:47.982 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [o].[Id], [o].[Code], [o].[CreateAt], [o].[CreateBy], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Description], [o].[IsActive], [o].[MainType], [o].[Name], [o].[SubType], [o].[SuperiorId], [o].[TenantId], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] = @__id_0
2025-06-25 15:14:47.988 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:47.990 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api) in 90.628ms
2025-06-25 15:14:47.991 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - application/json null
2025-06-25 15:14:47.992 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:47.996 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:47.998 -04:00 [INF] HTTP GET /OrganizationHierarchyDetails responded 200 in 104.3326 ms
2025-06-25 15:14:47.999 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.001 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - 200 null application/json; charset=utf-8 114.7607ms
2025-06-25 15:14:48.002 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyDetails", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyDetails(System.Guid, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:48.005 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 148
2025-06-25 15:14:48.005 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AssignableRoleList - application/json 162
2025-06-25 15:14:48.005 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - application/json 164
2025-06-25 15:14:48.006 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - application/json 158
2025-06-25 15:14:48.011 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:48.014 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:48.017 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:48.020 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:48.021 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.022 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.023 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.024 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.025 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:48.026 -04:00 [INF] Route matched with {action = "QueryAssignableRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAssignableRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:48.027 -04:00 [INF] Route matched with {action = "QueryRoleAssignmentList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleAssignmentListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:48.029 -04:00 [INF] Route matched with {action = "GetOrganizationEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:48.029 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:48.054 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:48.054 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:48.057 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:48.069 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:48.069 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:48.084 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:48.090 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:48.090 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:48.103 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:48.103 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [o].[Id], [o].[Code], [o].[CreateAt], [o].[CreateBy], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Description], [o].[IsActive], [o].[MainType], [o].[Name], [o].[SubType], [o].[SuperiorId], [o].[TenantId], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] = @__id_0
2025-06-25 15:14:48.110 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:48.112 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api) in 103.4141ms
2025-06-25 15:14:48.113 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.115 -04:00 [INF] HTTP GET /OrganizationHierarchyDetails responded 200 in 118.5660 ms
2025-06-25 15:14:48.117 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=3bf9c649-3717-40a4-88c6-9513fa70e06d - 200 null application/json; charset=utf-8 125.676ms
2025-06-25 15:14:48.120 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:48.121 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - application/json 158
2025-06-25 15:14:48.122 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 148
2025-06-25 15:14:48.124 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:48.128 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:48.131 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:48.133 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 102.1241ms
2025-06-25 15:14:48.134 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.135 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.137 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.139 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:48.138 -04:00 [INF] Route matched with {action = "GetOrganizationEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:48.140 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[@__organizationId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Id], [o].[OrganizationId], [o].[EmployeeId], [o].[TenantId], [e].[Name] AS [EmployeeName], [o].[CreateAt], [o].[CreateBy], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationEmployees] AS [o]
INNER JOIN [Employee] AS [e] ON [o].[EmployeeId] = [e].[Id]
WHERE [o].[OrganizationId] = @__organizationId_0
2025-06-25 15:14:48.141 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 129.2040 ms
2025-06-25 15:14:48.149 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationEmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:48.151 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 145.9141ms
2025-06-25 15:14:48.153 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 114.8571ms
2025-06-25 15:14:48.156 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AssignableRoleList - application/json 162
2025-06-25 15:14:48.158 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.161 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:48.162 -04:00 [INF] HTTP POST /OrganizationEmployeeList responded 200 in 141.9099 ms
2025-06-25 15:14:48.163 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.166 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - 200 null application/json; charset=utf-8 159.8995ms
2025-06-25 15:14:48.168 -04:00 [INF] Route matched with {action = "QueryAssignableRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAssignableRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:48.170 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:48.170 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:48.172 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - application/json 164
2025-06-25 15:14:48.187 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:48.189 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.190 -04:00 [INF] Route matched with {action = "QueryRoleAssignmentList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleAssignmentListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:48.195 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:48.196 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[OrganizationId], [a].[RoleId], [a].[IsInheritable], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AssignableRoles] AS [a]
2025-06-25 15:14:48.196 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[AssignableRoleId], [r].[EmployeeId], [r].[IsInheritable], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [RoleAssignments] AS [r]
2025-06-25 15:14:48.200 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AssignableRoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:48.203 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleAssignmentListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:48.205 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 171.2084ms
2025-06-25 15:14:48.206 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api) in 170.8068ms
2025-06-25 15:14:48.208 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.209 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.210 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:48.210 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:48.211 -04:00 [INF] HTTP POST /AssignableRoleList responded 200 in 196.7080 ms
2025-06-25 15:14:48.213 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:48.212 -04:00 [INF] HTTP POST /RoleAssignmentList responded 200 in 195.0097 ms
2025-06-25 15:14:48.229 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:48.237 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__organizationId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Id], [o].[OrganizationId], [o].[EmployeeId], [o].[TenantId], [e].[Name] AS [EmployeeName], [o].[CreateAt], [o].[CreateBy], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationEmployees] AS [o]
INNER JOIN [Employee] AS [e] ON [o].[EmployeeId] = [e].[Id]
WHERE [o].[OrganizationId] = @__organizationId_0
2025-06-25 15:14:48.241 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationEmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:48.241 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:48.243 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 97.7633ms
2025-06-25 15:14:48.246 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:48.247 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.248 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:48.249 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 105.5586ms
2025-06-25 15:14:48.250 -04:00 [INF] HTTP POST /OrganizationEmployeeList responded 200 in 122.3662 ms
2025-06-25 15:14:48.255 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.256 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AssignableRoleList - 200 null application/json; charset=utf-8 251.1443ms
2025-06-25 15:14:48.258 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - 200 null application/json; charset=utf-8 136.9102ms
2025-06-25 15:14:48.263 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 18
2025-06-25 15:14:48.264 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - 200 null application/json; charset=utf-8 258.9374ms
2025-06-25 15:14:48.271 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:48.259 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 128.1007 ms
2025-06-25 15:14:48.279 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.281 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 159.3649ms
2025-06-25 15:14:48.282 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:48.322 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:48.338 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[OrganizationId], [a].[RoleId], [a].[IsInheritable], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AssignableRoles] AS [a]
2025-06-25 15:14:48.343 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AssignableRoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:48.346 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 171.3108ms
2025-06-25 15:14:48.347 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.349 -04:00 [INF] HTTP POST /AssignableRoleList responded 200 in 188.1600 ms
2025-06-25 15:14:48.357 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:48.365 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[AssignableRoleId], [r].[EmployeeId], [r].[IsInheritable], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [RoleAssignments] AS [r]
2025-06-25 15:14:48.368 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleAssignmentListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:48.371 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api) in 178.2971ms
2025-06-25 15:14:48.373 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.374 -04:00 [INF] HTTP POST /RoleAssignmentList responded 200 in 186.8052 ms
2025-06-25 15:14:48.380 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:48.383 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:48.386 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 96.8037ms
2025-06-25 15:14:48.388 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:48.389 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 118.1565 ms
2025-06-25 15:14:48.391 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 127.952ms
2025-06-25 15:14:48.392 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AssignableRoleList - 200 null application/json; charset=utf-8 236.1464ms
2025-06-25 15:14:48.424 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - 200 null application/json; charset=utf-8 251.4849ms
2025-06-25 15:14:49.827 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 51
2025-06-25 15:14:49.832 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:49.834 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:49.836 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:49.866 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:49.894 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:49.922 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-25 15:14:49.928 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:49.931 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 92.1237ms
2025-06-25 15:14:49.935 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:49.937 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 104.7633 ms
2025-06-25 15:14:49.939 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 111.8317ms
2025-06-25 15:14:50.462 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=40f8888e-a811-43e4-8e93-a10fa65e999d - application/json null
2025-06-25 15:14:50.466 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:50.467 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.469 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyDetails", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyDetails(System.Guid, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:50.499 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:50.525 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:50.570 -04:00 [INF] Executed DbCommand (37ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [o].[Id], [o].[Code], [o].[CreateAt], [o].[CreateBy], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Description], [o].[IsActive], [o].[MainType], [o].[Name], [o].[SubType], [o].[SuperiorId], [o].[TenantId], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] = @__id_0
2025-06-25 15:14:50.575 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:50.577 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api) in 106.4814ms
2025-06-25 15:14:50.578 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=40f8888e-a811-43e4-8e93-a10fa65e999d - application/json null
2025-06-25 15:14:50.579 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.582 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:50.583 -04:00 [INF] HTTP GET /OrganizationHierarchyDetails responded 200 in 117.0304 ms
2025-06-25 15:14:50.585 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.587 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=40f8888e-a811-43e4-8e93-a10fa65e999d - 200 null application/json; charset=utf-8 125.183ms
2025-06-25 15:14:50.588 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyDetails", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyDetails(System.Guid, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:50.591 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 148
2025-06-25 15:14:50.591 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AssignableRoleList - application/json 162
2025-06-25 15:14:50.592 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - application/json 164
2025-06-25 15:14:50.593 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - application/json 158
2025-06-25 15:14:50.598 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:50.601 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:50.604 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:50.607 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:50.608 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.609 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.610 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.611 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.612 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:50.613 -04:00 [INF] Route matched with {action = "QueryAssignableRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAssignableRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:50.615 -04:00 [INF] Route matched with {action = "QueryRoleAssignmentList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleAssignmentListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:50.616 -04:00 [INF] Route matched with {action = "GetOrganizationEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:50.618 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:50.641 -04:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:50.641 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:50.648 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:50.648 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:50.656 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:50.670 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:50.670 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:50.699 -04:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:50.699 -04:00 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:50.699 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:50.699 -04:00 [INF] Executed DbCommand (41ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [o].[Id], [o].[Code], [o].[CreateAt], [o].[CreateBy], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Description], [o].[IsActive], [o].[MainType], [o].[Name], [o].[SubType], [o].[SuperiorId], [o].[TenantId], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] = @__id_0
2025-06-25 15:14:50.712 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:50.714 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:50.717 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 96.4113ms
2025-06-25 15:14:50.719 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api) in 124.3202ms
2025-06-25 15:14:50.721 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.723 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyDetails (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.724 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 125.8564 ms
2025-06-25 15:14:50.725 -04:00 [INF] HTTP GET /OrganizationHierarchyDetails responded 200 in 143.2064 ms
2025-06-25 15:14:50.728 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 136.9668ms
2025-06-25 15:14:50.731 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/OrganizationHierarchyDetails?OrganizationHierarchyId=40f8888e-a811-43e4-8e93-a10fa65e999d - 200 null application/json; charset=utf-8 152.3446ms
2025-06-25 15:14:50.731 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__organizationId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Id], [o].[OrganizationId], [o].[EmployeeId], [o].[TenantId], [e].[Name] AS [EmployeeName], [o].[CreateAt], [o].[CreateBy], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationEmployees] AS [o]
INNER JOIN [Employee] AS [e] ON [o].[EmployeeId] = [e].[Id]
WHERE [o].[OrganizationId] = @__organizationId_0
2025-06-25 15:14:50.735 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AssignableRoleList - application/json 162
2025-06-25 15:14:50.735 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 148
2025-06-25 15:14:50.739 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - application/json 158
2025-06-25 15:14:50.742 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationEmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:50.745 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:50.748 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:50.754 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 127.2855ms
2025-06-25 15:14:50.752 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:50.755 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.756 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.758 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.760 -04:00 [INF] Route matched with {action = "QueryAssignableRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAssignableRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:50.762 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:50.763 -04:00 [INF] HTTP POST /OrganizationEmployeeList responded 200 in 156.4134 ms
2025-06-25 15:14:50.759 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.771 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - 200 null application/json; charset=utf-8 178.5122ms
2025-06-25 15:14:50.773 -04:00 [INF] Route matched with {action = "GetOrganizationEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:50.776 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - application/json 164
2025-06-25 15:14:50.782 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:50.782 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[OrganizationId], [a].[RoleId], [a].[IsInheritable], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AssignableRoles] AS [a]
2025-06-25 15:14:50.783 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.785 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AssignableRoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:50.786 -04:00 [INF] Route matched with {action = "QueryRoleAssignmentList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleAssignmentListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:50.788 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 165.3856ms
2025-06-25 15:14:50.792 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.794 -04:00 [INF] HTTP POST /AssignableRoleList responded 200 in 192.5956 ms
2025-06-25 15:14:50.795 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:50.795 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:50.811 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:50.817 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:50.817 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[AssignableRoleId], [r].[EmployeeId], [r].[IsInheritable], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [RoleAssignments] AS [r]
2025-06-25 15:14:50.823 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleAssignmentListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:50.825 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api) in 200.2398ms
2025-06-25 15:14:50.827 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.828 -04:00 [INF] HTTP POST /RoleAssignmentList responded 200 in 223.6626 ms
2025-06-25 15:14:50.830 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:50.830 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:50.837 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:50.838 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AssignableRoleList - 200 null application/json; charset=utf-8 246.4395ms
2025-06-25 15:14:50.847 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 51
2025-06-25 15:14:50.850 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:50.851 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.852 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:50.854 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:50.855 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:50.861 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:50.863 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 93.9362ms
2025-06-25 15:14:50.865 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.866 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 118.2663 ms
2025-06-25 15:14:50.869 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 134.1394ms
2025-06-25 15:14:50.875 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - 200 null application/json; charset=utf-8 283.0928ms
2025-06-25 15:14:50.879 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:50.874 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[@__organizationId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Id], [o].[OrganizationId], [o].[EmployeeId], [o].[TenantId], [e].[Name] AS [EmployeeName], [o].[CreateAt], [o].[CreateBy], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationEmployees] AS [o]
INNER JOIN [Employee] AS [e] ON [o].[EmployeeId] = [e].[Id]
WHERE [o].[OrganizationId] = @__organizationId_0
2025-06-25 15:14:50.882 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 18
2025-06-25 15:14:50.888 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationEmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:50.891 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:50.893 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 114.2529ms
2025-06-25 15:14:50.894 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.897 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.898 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:50.900 -04:00 [INF] HTTP POST /OrganizationEmployeeList responded 200 in 147.8687 ms
2025-06-25 15:14:50.904 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - 200 null application/json; charset=utf-8 165.122ms
2025-06-25 15:14:50.911 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:50.927 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:50.935 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[OrganizationId], [a].[RoleId], [a].[IsInheritable], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AssignableRoles] AS [a]
2025-06-25 15:14:50.942 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-25 15:14:50.943 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AssignableRoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:50.946 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:50.947 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 180.4578ms
2025-06-25 15:14:50.949 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 93.6213ms
2025-06-25 15:14:50.951 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.953 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:50.954 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.956 -04:00 [INF] HTTP POST /AssignableRoleList responded 200 in 210.7231 ms
2025-06-25 15:14:50.960 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 110.3518 ms
2025-06-25 15:14:50.962 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[AssignableRoleId], [r].[EmployeeId], [r].[IsInheritable], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [RoleAssignments] AS [r]
2025-06-25 15:14:50.966 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 119.7741ms
2025-06-25 15:14:50.970 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleAssignmentListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:50.975 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api) in 185.1494ms
2025-06-25 15:14:50.977 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.979 -04:00 [INF] HTTP POST /RoleAssignmentList responded 200 in 197.0760 ms
2025-06-25 15:14:50.979 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-25 15:14:50.986 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:50.988 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 86.112ms
2025-06-25 15:14:50.990 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:50.991 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 100.4180 ms
2025-06-25 15:14:50.994 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 112.1466ms
2025-06-25 15:14:50.996 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AssignableRoleList - 200 null application/json; charset=utf-8 260.5885ms
2025-06-25 15:14:51.022 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - 200 null application/json; charset=utf-8 245.8602ms
2025-06-25 15:14:57.205 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-06-25 15:14:57.210 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:57.211 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 1.1391 ms
2025-06-25 15:14:57.214 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 8.7042ms
2025-06-25 15:14:57.220 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 51
2025-06-25 15:14:57.223 -04:00 [INF] CORS policy execution successful.
2025-06-25 15:14:57.224 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:57.225 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-25 15:14:57.276 -04:00 [INF] Executed DbCommand (48ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-25 15:14:57.304 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-25 15:14:57.330 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-25 15:14:57.334 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 15:14:57.335 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 108.2357ms
2025-06-25 15:14:57.337 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-25 15:14:57.338 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 115.5537 ms
2025-06-25 15:14:57.341 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 121.5176ms
