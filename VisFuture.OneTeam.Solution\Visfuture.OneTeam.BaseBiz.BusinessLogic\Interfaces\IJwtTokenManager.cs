﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.Interfaces;

public interface IJwtTokenManager
{
    EntityResponse<UserJwtDto> RenewJwtToken(Guid userId, string refreshToken);
    string GenerateToken(Guid userId, string userName, Guid? tenantId = null);
    string GenerateRefreshToken(Guid userId);
    bool ValidateRefreshToken(Guid userId, string refreshToken);
    public void InvalidateRefreshToken(Guid userId);
}
