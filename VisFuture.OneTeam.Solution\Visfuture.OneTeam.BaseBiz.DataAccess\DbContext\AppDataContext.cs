﻿﻿using Microsoft.EntityFrameworkCore;
using Visfuture.OneTeam.BaseBiz.DataAccess.Entities;
using Visfuture.OneTeam.Core.Common.Base.DatabaseContext;

namespace Visfuture.OneTeam.BaseBiz.DataAccess.DbContext;

public partial class AppDataContext(DbContextOptions<AppDataContext> options) : DatabaseContext<AppDataContext>(options)
{
    public virtual DbSet<AccessResource> AccessResources { get; set; }

    public virtual DbSet<AssignableRole> AssignableRoles { get; set; }

    public virtual DbSet<CodeItem> CodeItems { get; set; }

    public virtual DbSet<CodeItemAttribute> CodeItemAttributes { get; set; }

    public virtual DbSet<CodeType> CodeTypes { get; set; }

    public virtual DbSet<Employee> Employees { get; set; }

    public virtual DbSet<GlobalAdmin> GlobalAdmins { get; set; }

    public virtual DbSet<I18nKey> I18nKeys { get; set; }

    public virtual DbSet<I18nTranslation> I18nTranslations { get; set; }

    public virtual DbSet<NotificationTemplate> NotificationTemplates { get; set; }

    public virtual DbSet<OrganizationEmployee> OrganizationEmployees { get; set; }

    public virtual DbSet<OrganizationHierarchy> OrganizationHierarchies { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<RoleAccess> RoleAccesses { get; set; }

    public virtual DbSet<RoleAssignment> RoleAssignments { get; set; }

    public virtual DbSet<SequenceNo> SequenceNos { get; set; }

    public virtual DbSet<Tenant> Tenants { get; set; }

    public virtual DbSet<UserAccount> UserAccounts { get; set; }


    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        #region AccessResource

        modelBuilder.Entity<AccessResource>(entity =>
        {
            entity.ToTable("AccessResource");

            entity.HasIndex(e => new { e.TenantId, e.ResourceCode }, "Idx_AccessResource_Code");

            entity.HasIndex(e => new { e.TenantId, e.SuperiorId }, "Idx_AccessResource_SuperiorId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ResourceCode).HasMaxLength(255);
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsPublic).HasDefaultValue(true);
            entity.Property(e => e.ModuleId).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.SubType).HasMaxLength(50);
            entity.Property(e => e.SystemId).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);
        });
        #endregion

        #region AssignableRole
        modelBuilder.Entity<AssignableRole>(entity =>
        {
            entity.ToTable("AssignableRoles");
            entity.HasKey(e => e.Id).HasName("PK_AssignableRole");

            entity.HasIndex(e => new { e.TenantId, e.OrganizationId }, "Idx_AssignableRole_OrganizationId");

            entity.HasIndex(e => new { e.TenantId, e.OrganizationId, e.RoleId },
                "Idx_AssignableRole_OrganizationId_RoleId").IsUnique();

            entity.HasIndex(e => new { e.TenantId, e.RoleId }, "Idx_AssignableRole_RoleId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.IsInheritable).HasDefaultValue(true);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.Organization).WithMany(p => p.AssignableRoles)
                .HasForeignKey(d => d.OrganizationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AssignableRole_OrganizationHierarchy");

            entity.HasOne(d => d.Role).WithMany(p => p.AssignableRoles)
                .HasForeignKey(d => d.RoleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AssignableRole_Role");
        });
        #endregion

        #region CodeItem
        modelBuilder.Entity<CodeItem>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_CodeItem");

            entity.HasIndex(e => new { e.TenantId, e.CodeTypeId }, "Idx_CodeItem_CodeTypeId");

            entity.HasIndex(e => new { e.TenantId, e.CodeTypeId, e.Value }, "Idx_CodeItem_ItemCode").IsUnique();

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.ItemField1).HasMaxLength(255);
            entity.Property(e => e.ItemField2).HasMaxLength(255);
            entity.Property(e => e.ItemField3).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Value).HasMaxLength(255);
            entity.Property(e => e.SeqNo).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.CodeType).WithMany(p => p.CodeItems)
                .HasForeignKey(d => d.CodeTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CodeItems_CodeType");
        });
        #endregion

        #region CodeItemAttribute
        modelBuilder.Entity<CodeItemAttribute>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_CodeItemAttribute");

            entity.HasIndex(e => new { e.TenantId, e.CodeItemId }, "Idx_CodeItemAttribute_CodeItemId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Code).HasMaxLength(255);
            entity.Property(e => e.Value).HasMaxLength(255);
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.CodeItem).WithMany(p => p.CodeItemAttributes)
                .HasForeignKey(d => d.CodeItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CodeItemAttributes_CodeItems");
        });
        #endregion

        #region CodeType
        modelBuilder.Entity<CodeType>(entity =>
        {
            entity.ToTable("CodeType");

            entity.HasIndex(e => new { e.TenantId, e.TypeCode }, "Idx_CodeType_TypeCode").IsUnique();

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.MainType).HasMaxLength(50);
            entity.Property(e => e.SubType).HasMaxLength(50);
            entity.Property(e => e.TypeCode).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);
        });

        #endregion

        #region Employee
        modelBuilder.Entity<Employee>(entity =>
        {
            entity.ToTable("Employee");

            entity.HasIndex(e => new { e.TenantId, e.Code }, "Idx_Employee_EmployeeNo").IsUnique();

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.BusinessEmail).HasMaxLength(255);
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.Code).HasMaxLength(50);
            entity.Property(e => e.EmployeeStatus)
                .HasMaxLength(50)
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.JobTitle).HasMaxLength(255);
            entity.Property(e => e.MainType).HasMaxLength(50);
            entity.Property(e => e.PositionId).HasMaxLength(50);
            entity.Property(e => e.SubType).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);
            entity.Property(e => e.WorkScheduleId).HasMaxLength(50);

            entity.HasOne(d => d.UserAccount).WithMany(p => p.Employees)
                .HasForeignKey(d => d.UserAccountId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Employee_UserAccounts");
        });
        #endregion

        #region GlobalAdmin
        modelBuilder.Entity<GlobalAdmin>(entity =>
        {
            entity.ToTable("GlobalAdmin");

            entity.HasIndex(e => e.LoginName, "Idx_GlobalAdmin_LoginName");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.Email).HasMaxLength(255);
            entity.Property(e => e.ImageId).HasMaxLength(255);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LoginName).HasMaxLength(50);
            entity.Property(e => e.Mobile).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Password).HasMaxLength(255);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);
        });
        #endregion

        #region I18nKey
        modelBuilder.Entity<I18nKey>(entity =>
        {
            entity.ToTable("I18nKey");

            entity.HasIndex(e => new { e.TenantId, e.KeyCode }, "Idx_I18nKey_KeyCode").IsUnique();

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.KeyCode).HasMaxLength(255);
            entity.Property(e => e.KeyType).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);
        });

        #endregion

        #region I18nTranslation
        modelBuilder.Entity<I18nTranslation>(entity =>
        {
            entity.ToTable("I18nTranslation");

            entity.HasIndex(e => new { e.TenantId, e.KeyId }, "Idx_I18nTranslation_KeyId");

            entity.HasIndex(e => new { e.TenantId, e.KeyId, e.Locale }, "Idx_I18nTranslation_KeyId_Locale").IsUnique();

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.Locale).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.I18nKey).WithMany(p => p.I18NTranslations)
                .HasForeignKey(d => d.KeyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_I18nTranslation_I18nKey");
        });
        #endregion

        #region NotificationTemplate
        modelBuilder.Entity<NotificationTemplate>(entity =>
        {
            entity.ToTable("NotificationTemplate");

            entity.HasIndex(e => new { e.TenantId, e.NotificationType }, "Idx_NotificationTemplate_NotificationType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.NotificationMethod).HasMaxLength(50);
            entity.Property(e => e.NotificationType).HasMaxLength(50);
            entity.Property(e => e.Sender).HasMaxLength(255);
            entity.Property(e => e.Title).HasMaxLength(255);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);
        });
        #endregion

        #region OrganizationEmployee
        modelBuilder.Entity<OrganizationEmployee>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_OrganizationEmployee");

            entity.HasIndex(e => new { e.TenantId, e.EmployeeId }, "Idx_OrganizationEmployee_EmployeeId");

            entity.HasIndex(e => new { e.TenantId, e.OrganizationId }, "Idx_OrganizationEmployee_OrganizaitonId");

            entity.HasIndex(e => new { e.TenantId, e.OrganizationId, e.EmployeeId },
                "Idx_OrganizationEmployee_OrganizationId_EmployeeId").IsUnique();

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.Employee).WithMany(p => p.OrganizationEmployees)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrganizationEmployees_Employee");

            entity.HasOne(d => d.Organization).WithMany(p => p.OrganizationEmployees)
                .HasForeignKey(d => d.OrganizationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrganizationEmployees_OrganizationHierarchy");
        });
        #endregion

        #region OrganizationHierarchy
        modelBuilder.Entity<OrganizationHierarchy>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_OrganizationHierarchy");

            entity.HasIndex(e => new { e.TenantId, e.SuperiorId }, "Idx_OrganizationHierarchy_SuperiorId");

            entity.HasIndex(e => e.TenantId, "Idx_OrganizationHierarchy_TenantId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Code).HasMaxLength(50);
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.DefaultLocation).HasMaxLength(50);
            entity.Property(e => e.DefaultWorkSchedule).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.MainType).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.SubType).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.Superior).WithMany(p => p.InverseSuperior)
                .HasForeignKey(d => d.SuperiorId)
                .HasConstraintName("FK_OrganizationHierarchies_OrganizationHierarchies");
        });
        #endregion

        #region Role
        modelBuilder.Entity<Role>(entity =>
        {
            entity.ToTable("Role");

            entity.HasIndex(e => new { e.TenantId, e.Code }, "Idx_Role_Code");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Code).HasMaxLength(255);
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);
            entity.Property(e => e.Usage).HasMaxLength(50);
        });
        #endregion

        #region RoleAccess
        modelBuilder.Entity<RoleAccess>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_RoleAccess");

            entity.HasIndex(e => new { e.TenantId, e.ResourceId }, "Idx_RoleAccess_ResourceId");

            entity.HasIndex(e => new { e.TenantId, e.RoleId }, "Idx_RoleAccess_RoleId");

            entity.HasIndex(e => new { e.TenantId, e.RoleId, e.ResourceId }, "Idx_RoleAccess_RoleId_ResourceId")
                .IsUnique();

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AccessType)
                .HasMaxLength(50)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.Resource).WithMany(p => p.RoleAccesses)
                .HasForeignKey(d => d.ResourceId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RoleAccess_AccessResource");

            entity.HasOne(d => d.Role).WithMany(p => p.RoleAccesses)
                .HasForeignKey(d => d.RoleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RoleAccess_Role");
        });
        #endregion

        #region RoleAssignment
        modelBuilder.Entity<RoleAssignment>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_RoleAssignment");

            entity.HasIndex(e => new { e.TenantId, e.AssignableRoleId }, "Idx_RoleAssignment_AssignableRoleId");

            entity.HasIndex(e => new { e.TenantId, e.AssignableRoleId, e.EmployeeId },
                "Idx_RoleAssignment_AssignableRoleId_EmployeeId").IsUnique();

            entity.HasIndex(e => new { e.TenantId, e.EmployeeId }, "Idx_RoleAssignment_EmployeeId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.IsInheritable).HasDefaultValue(true);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);

            entity.HasOne(d => d.AssignableRole).WithMany(p => p.RoleAssignments)
                .HasForeignKey(d => d.AssignableRoleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RoleAssignments_AssignableRoles");

            entity.HasOne(d => d.Employee).WithMany(p => p.RoleAssignments)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RoleAssignments_Employee");
        });
        #endregion

        #region SequenceNo
        modelBuilder.Entity<SequenceNo>(entity =>
        {
            entity.ToTable("SequenceNo");

            entity.HasIndex(e => new { e.TenantId, e.Name }, "Idx_SequenceNo_SequenceCode").IsUnique();

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.Prefix).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Suffix).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);
        });
        #endregion

        #region Tenant
        modelBuilder.Entity<Tenant>(entity =>
        {
            entity.ToTable("Tenant");

            entity.HasIndex(e => e.Domain, "Idx_Tenant_Domain");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Address1).HasMaxLength(255);
            entity.Property(e => e.Address2).HasMaxLength(255);
            entity.Property(e => e.City).HasMaxLength(255);
            entity.Property(e => e.ContactEmail).HasMaxLength(255);
            entity.Property(e => e.ContactFax).HasMaxLength(50);
            entity.Property(e => e.ContactName).HasMaxLength(255);
            entity.Property(e => e.ContactPhone).HasMaxLength(50);
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.Domain).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Language).HasMaxLength(50);
            entity.Property(e => e.PostalCode).HasMaxLength(50);
            entity.Property(e => e.Province).HasMaxLength(50);
            entity.Property(e => e.TimeZone).HasMaxLength(50);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);
        });
        #endregion

        #region UserAccount
        modelBuilder.Entity<UserAccount>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_UserAccount");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CreateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.Email).HasMaxLength(255);
            entity.Property(e => e.ImageId).HasMaxLength(255);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Language).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Mobile).HasMaxLength(50);
            entity.Property(e => e.PasswordHash).HasMaxLength(64);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdateBy).HasMaxLength(50);
        });
        #endregion

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}