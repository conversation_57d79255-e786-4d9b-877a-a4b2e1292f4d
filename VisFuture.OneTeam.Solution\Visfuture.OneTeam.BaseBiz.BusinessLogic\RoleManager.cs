using MapsterMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using StackExchange.Redis;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Interfaces;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests;
using Visfuture.OneTeam.BaseBiz.DataAccess;
using Visfuture.OneTeam.BaseBiz.DataAccess.DbContext;
using Visfuture.OneTeam.BaseBiz.DataAccess.Entities;
using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Core.Common.Helpers;
using Role = Visfuture.OneTeam.BaseBiz.DataAccess.Entities.Role;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic;

public class RoleManager(
    IConfiguration configuration,
    IHttpClientFactory httpClientFactory,
    IHttpContextAccessor httpContextAccessor,
    AppDataContext appDataContext,
    IConnectionMultiplexer redis,
    IMapper mapper) : BaseBizBaseManager(httpContextAccessor, appDataContext, mapper), IRoleManager
{
    private readonly CrudHelper<AccessResource, AccessResourceDto, AccessResourceListItemDto>
        _accessResourceCrudHelper =
            new(configuration, httpClientFactory, appDataContext, appDataContext.AccessResources);

    private readonly CrudHelper<AssignableRole, AssignableRoleDto, AssignableRoleListItemDto>
        _assignableRoleCrudHelper =
            new(configuration, httpClientFactory, appDataContext, appDataContext.AssignableRoles);

    private readonly CrudHelper<RoleAccess, RoleAccessDto, RoleAccessListItemDto> _roleAccessCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.RoleAccesses);

    private readonly CrudHelper<RoleAssignment, RoleAssignmentDto, RoleAssignmentListItemDto>
        _roleAssignmentCrudHelper =
            new(configuration, httpClientFactory, appDataContext, appDataContext.RoleAssignments);

    private readonly CrudHelper<Role, RoleDto, RoleListItemDto> _roleCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.Roles);

    #region Role

    public async Task<EntityResponsePaged<RoleListItemDto>> QueryRoleAsync(BaseQuery request,
        CancellationToken cancellationToken)
    {
        return await _roleCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse<RoleDto>> GetRoleByIdAsync(Guid roleId, CancellationToken cancellationToken)
    {
        return await _roleCrudHelper.GetEntity(roleId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddRoleAsync(RoleDto model, CancellationToken cancellationToken)
    {
        return await _roleCrudHelper.AddEntity(GetCurrentUser(), model, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateRoleAsync(RoleDto model, CancellationToken cancellationToken)
    {
        return await _roleCrudHelper.UpdateEntity(GetCurrentUser(), model, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteRoleAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        return await _roleCrudHelper.DeleteEntity(roleId, cancellationToken);
    }

    public async Task<EntityResponse> AddRolesAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _roleCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateRolesAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _roleCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteRolesAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _roleCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportRoleExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _roleCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportRoleExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _roleCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region Role Access

    public async Task<EntityResponsePaged<RoleAccessListItemDto>> QueryRoleAccessAsync(BaseQuery request,
        CancellationToken cancellationToken)
    {
        return await _roleAccessCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse<RoleAccessDto>> GetRoleAccessByIdAsync(Guid roleAccessId,
        CancellationToken cancellationToken)
    {
        return await _roleAccessCrudHelper.GetEntity(roleAccessId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddRoleAccessAsync(RoleAccessDto model, CancellationToken cancellationToken)
    {
        return await _roleAccessCrudHelper.AddEntity(GetCurrentUser(), model, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateRoleAccessAsync(RoleAccessDto model,
        CancellationToken cancellationToken)
    {
        return await _roleAccessCrudHelper.UpdateEntity(GetCurrentUser(), model, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteRoleAccessAsync(Guid roleAccessId,
        CancellationToken cancellationToken = default)
    {
        return await _roleAccessCrudHelper.DeleteEntity(roleAccessId, cancellationToken);
    }

    public async Task<EntityResponse> AddRoleAccessesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _roleAccessCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateRoleAccessesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _roleAccessCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteRoleAccessesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _roleAccessCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportRoleAccessExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _roleAccessCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportRoleAccessExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _roleAccessCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region Assignable Role

    public async Task<EntityResponsePaged<AssignableRoleListItemDto>> QueryAssignableRoleAsync(BaseQuery request,
        CancellationToken cancellationToken)
    {
        return await _assignableRoleCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse<AssignableRoleDto>> GetAssignableRoleByIdAsync(Guid assignableRoleId,
        CancellationToken cancellationToken)
    {
        return await _assignableRoleCrudHelper.GetEntity(assignableRoleId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddAssignableRoleAsync(AssignableRoleDto model,
        CancellationToken cancellationToken)
    {
        return await _assignableRoleCrudHelper.AddEntity(GetCurrentUser(), model, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateAssignableRoleAsync(AssignableRoleDto model,
        CancellationToken cancellationToken)
    {
        return await _assignableRoleCrudHelper.UpdateEntity(GetCurrentUser(), model, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteAssignableRoleAsync(Guid assignableRoleId,
        CancellationToken cancellationToken = default)
    {
        return await _assignableRoleCrudHelper.DeleteEntity(assignableRoleId, cancellationToken);
    }

    public async Task<EntityResponse> AddAssignableRolesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _assignableRoleCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateAssignableRolesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _assignableRoleCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteAssignableRolesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _assignableRoleCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportAssignableRoleExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _assignableRoleCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportAssignableRoleExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _assignableRoleCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region Role Assignment

    public async Task<EntityResponsePaged<RoleAssignmentListItemDto>> QueryRoleAssignmentAsync(BaseQuery request,
        CancellationToken cancellationToken)
    {
        return await _roleAssignmentCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse<RoleAssignmentDto>> GetRoleAssignmentByIdAsync(Guid roleAssignmentId,
        CancellationToken cancellationToken)
    {
        return await _roleAssignmentCrudHelper.GetEntity(roleAssignmentId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddRoleAssignmentAsync(RoleAssignmentDto dto, CancellationToken cancellationToken = default)
    {
        // Resolve from OrganizationEmployee
        var orgEmployee = await DB.OrganizationEmployees
            .FirstOrDefaultAsync(x => x.EmployeeId == dto.EmployeeId, cancellationToken);

        if (orgEmployee != null)
        {
            dto.OrganizationId = orgEmployee.OrganizationId;
            // Make sure dto.EmployeeId is the real EmployeeId already (which it is)
        }

        return await _roleAssignmentCrudHelper.AddEntity(GetCurrentUser(), dto, cancellationToken);
    }


    public async Task<EntityResponse<Guid>> UpdateRoleAssignmentAsync(RoleAssignmentDto model,
        CancellationToken cancellationToken)
    {
        return await _roleAssignmentCrudHelper.UpdateEntity(GetCurrentUser(), model, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteRoleAssignmentAsync(Guid roleAssignmentId,
        CancellationToken cancellationToken = default)
    {
        return await _roleAssignmentCrudHelper.DeleteEntity(roleAssignmentId, cancellationToken);
    }

    public async Task<EntityResponse> AddRoleAssignmentsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _roleAssignmentCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateRoleAssignmentsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _roleAssignmentCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteRoleAssignmentsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _roleAssignmentCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportRoleAssignmentExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _roleAssignmentCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportRoleAssignmentExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _roleAssignmentCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region Access Resource

    public async Task<EntityResponsePaged<AccessResourceListItemDto>> QueryAccessResourceAsync(BaseQuery request,
        CancellationToken cancellationToken)
    {
        return await _accessResourceCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse<AccessResourceDto>> GetAccessResourceByIdAsync(Guid accessResourceId,
        CancellationToken cancellationToken)
    {
        return await _accessResourceCrudHelper.GetEntity(accessResourceId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddAccessResourceAsync(AccessResourceDto model,
        CancellationToken cancellationToken)
    {
        return await _accessResourceCrudHelper.AddEntity(GetCurrentUser(), model, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateAccessResourceAsync(AccessResourceDto model,
        CancellationToken cancellationToken)
    {
        return await _accessResourceCrudHelper.UpdateEntity(GetCurrentUser(), model, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteAccessResourceAsync(Guid accessResourceId,
        CancellationToken cancellationToken = default)
    {
        return await _accessResourceCrudHelper.DeleteEntity(accessResourceId, cancellationToken);
    }

    public async Task<EntityResponse> AddAccessResourcesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _accessResourceCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateAccessResourcesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _accessResourceCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteAccessResourcesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _accessResourceCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public EntityResponse<List<bool>> MatchAccessResourceAsync(StringListRequest request,
        CancellationToken cancellationToken = default)
    {
        // get userId from httpContext
        string? userId = _httpContextAccessor.HttpContext?.User.Claims.FirstOrDefault(x => x.Type == "UserId")?.Value;
        if (string.IsNullOrEmpty(userId)) return EntityResponse<List<bool>>.Failed();
        IDatabase redb = redis.GetDatabase();
        List<bool> matchedList = request.StringList.Select(x => redb.SetContains(userId, x)).ToList();
        return EntityResponse<List<bool>>.Success(matchedList);
    }

    public async Task<EntityResponse<B64File>> ExportAccessResourceExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _accessResourceCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportAccessResourceExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _accessResourceCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion
}