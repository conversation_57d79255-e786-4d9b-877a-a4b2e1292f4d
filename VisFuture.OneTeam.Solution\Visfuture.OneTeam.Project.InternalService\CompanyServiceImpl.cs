using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Project.BusinessLogic.DTOs;
using Visfuture.OneTeam.Project.BusinessLogic.Interfaces;
using Visfuture.OneTeam.Project.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.Project.InternalService.Interfaces;

namespace Visfuture.OneTeam.Project.InternalService;

public class CompanyServiceImpl(ICompanyManager companyManager) : ICompanyService
{
    private readonly ICompanyManager companyManager = companyManager;

    #region Company
    public async Task<EntityResponsePaged<CompanyListItemDto>> QueryCompanyAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await companyManager.QueryCompanyAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<CompanyDto>> GetCompanyAsync(Guid companyId, CancellationToken cancellationToken = default)
    {
        return await companyManager.GetCompanyByIdAsync(companyId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddCompanyAsync(CompanyDto companyDto, CancellationToken cancellationToken = default)
    {
        return await companyManager.AddCompanyAsync(companyDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateCompanyAsync(CompanyDto companyDto, CancellationToken cancellationToken = default)
    {
        return await companyManager.UpdateCompanyAsync(companyDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteCompanyAsync(Guid companyId, CancellationToken cancellationToken = default)
    {
        return await companyManager.DeleteCompanyAsync(companyId, cancellationToken);
    }

    public async Task<EntityResponse> AddCompaniesAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await companyManager.AddCompaniesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateCompaniesAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await companyManager.UpdateCompaniesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteCompaniesAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await companyManager.DeleteCompaniesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportCompanyExcel(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await companyManager.ExportCompanyExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportCompanyExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await companyManager.ImportCompanyExcel(file, cancellationToken);
    }
    #endregion

    #region Contact
    public async Task<EntityResponsePaged<ContactListItemDto>> QueryContactAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await companyManager.QueryContactAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<ContactDto>> GetContactAsync(Guid contactId, CancellationToken cancellationToken = default)
    {
        return await companyManager.GetContactByIdAsync(contactId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddContactAsync(ContactDto contactDto, CancellationToken cancellationToken = default)
    {
        return await companyManager.AddContactAsync(contactDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateContactAsync(ContactDto contactDto, CancellationToken cancellationToken = default)
    {
        return await companyManager.UpdateContactAsync(contactDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteContactAsync(Guid contactId, CancellationToken cancellationToken = default)
    {
        return await companyManager.DeleteContactAsync(contactId, cancellationToken);
    }

    public async Task<EntityResponse> AddContactsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await companyManager.AddContactsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateContactsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await companyManager.UpdateContactsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteContactsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await companyManager.DeleteContactsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportContactExcel(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await companyManager.ExportContactExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportContactExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await companyManager.ImportContactExcel(file, cancellationToken);
    }

    public async Task<EntityResponse<List<ContactDto>>> GetContactsByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        return await companyManager.GetContactsByEmailAsync(email, cancellationToken);
    }
    #endregion
}
