﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;

public class CodeTypeListItemDto : CodeDto
{
    public string TypeCode { get; set; } = null!;

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public string MainType { get; set; } = null!;

    public string? SubType { get; set; }

    public Guid? SuperiorId { get; set; }
}