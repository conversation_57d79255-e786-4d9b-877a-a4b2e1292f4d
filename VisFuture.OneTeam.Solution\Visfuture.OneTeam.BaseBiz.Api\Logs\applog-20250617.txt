2025-06-17 15:02:04.731 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 15:02:04.904 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:02:04.910 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 10.6166 ms
2025-06-17 15:02:04.918 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 188.6012ms
2025-06-17 15:02:04.930 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:02:04.937 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:02:04.944 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:02:04.955 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:02:05.275 -04:00 [INF] Executed DbCommand (106ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:02:05.321 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:02:05.440 -04:00 [INF] Executed DbCommand (98ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:02:05.451 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:02:05.454 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 492.3296ms
2025-06-17 15:02:05.457 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:02:05.459 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 522.6253 ms
2025-06-17 15:02:05.461 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 531.4476ms
2025-06-17 15:02:20.065 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 15:02:20.070 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:02:20.071 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.8260 ms
2025-06-17 15:02:20.074 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 9.686ms
2025-06-17 15:02:20.079 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:02:20.083 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:02:20.085 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:02:20.087 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:02:20.112 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:02:20.140 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:02:20.233 -04:00 [INF] Executed DbCommand (85ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:02:20.237 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:02:20.240 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 149.7617ms
2025-06-17 15:02:20.243 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:02:20.245 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 161.7809 ms
2025-06-17 15:02:20.248 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 168.1679ms
2025-06-17 15:03:19.321 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/UpdateSequenceNo - null null
2025-06-17 15:03:19.329 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:03:19.330 -04:00 [INF] HTTP OPTIONS /UpdateSequenceNo responded 204 in 2.8324 ms
2025-06-17 15:03:19.333 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/UpdateSequenceNo - 204 null null 12.2276ms
2025-06-17 15:03:19.344 -04:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5275/UpdateSequenceNo - application/json 253
2025-06-17 15:03:19.349 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:03:19.350 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:03:19.397 -04:00 [INF] Route matched with {action = "UpdateSequenceNo", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateSequenceNo(Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:03:19.455 -04:00 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:03:19.485 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:03:19.582 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[@p10='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (Size = 50), @p2='?' (DbType = Int64), @p3='?' (DbType = Int16), @p4='?' (Size = 255), @p5='?' (Size = 50), @p6='?' (Size = 50), @p7='?' (DbType = Guid), @p8='?' (DbType = DateTime2), @p9='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [SequenceNo] SET [CreateAt] = @p0, [CreateBy] = @p1, [CurrentNo] = @p2, [Length] = @p3, [Name] = @p4, [Prefix] = @p5, [Suffix] = @p6, [TenantId] = @p7, [UpdateAt] = @p8, [UpdateBy] = @p9
OUTPUT 1
WHERE [Id] = @p10;
2025-06-17 15:03:19.595 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-17 15:03:19.599 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api) in 192.0572ms
2025-06-17 15:03:19.603 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:03:19.605 -04:00 [INF] HTTP PUT /UpdateSequenceNo responded 200 in 257.4472 ms
2025-06-17 15:03:19.610 -04:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5275/UpdateSequenceNo - 200 null application/json; charset=utf-8 266.8476ms
2025-06-17 15:03:19.622 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 15:03:19.640 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:03:19.648 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 8.0145 ms
2025-06-17 15:03:19.652 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 31.4658ms
2025-06-17 15:03:19.658 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:03:19.662 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:03:19.663 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:03:19.664 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:03:19.696 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:03:19.725 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:03:19.763 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:03:19.769 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:03:19.772 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 104.4795ms
2025-06-17 15:03:19.775 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:03:19.779 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 117.5966 ms
2025-06-17 15:03:19.784 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 125.6805ms
2025-06-17 15:03:29.206 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-17 15:03:29.212 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:03:29.213 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.0045 ms
2025-06-17 15:03:29.216 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 9.4183ms
2025-06-17 15:03:29.221 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-17 15:03:29.226 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:03:29.228 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:03:29.230 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:03:29.254 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:03:29.282 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:03:29.376 -04:00 [INF] Executed DbCommand (80ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:03:29.383 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:03:29.388 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 154.5445ms
2025-06-17 15:03:29.392 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:03:29.394 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 167.9557 ms
2025-06-17 15:03:29.397 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 176.5667ms
2025-06-17 15:03:37.499 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-17 15:03:37.504 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:03:37.505 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.4161 ms
2025-06-17 15:03:37.509 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 9.491ms
2025-06-17 15:03:37.515 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-17 15:03:37.519 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:03:37.520 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:03:37.522 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:03:37.545 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:03:37.574 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:03:37.656 -04:00 [INF] Executed DbCommand (77ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:03:37.662 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:03:37.666 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 141.6698ms
2025-06-17 15:03:37.669 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:03:37.671 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 152.4014 ms
2025-06-17 15:03:37.674 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 159.8128ms
2025-06-17 15:04:24.154 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-17 15:04:24.163 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:04:24.165 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.1646 ms
2025-06-17 15:04:24.167 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 12.5736ms
2025-06-17 15:04:24.176 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-06-17 15:04:24.182 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:04:24.183 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:04:24.186 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:04:24.213 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:04:24.245 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:04:24.336 -04:00 [INF] Executed DbCommand (83ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-06-17 15:04:24.341 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:04:24.347 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 158.2181ms
2025-06-17 15:04:24.352 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:04:24.354 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 172.4260 ms
2025-06-17 15:04:24.357 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 180.9715ms
2025-06-17 15:04:31.835 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - application/json; charset=utf-8 0
2025-06-17 15:04:31.856 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:04:31.859 -04:00 [INF] Route matched with {action = "GetSequenceNoByNameExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoByNameExt(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:04:31.884 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:04:31.913 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:04:31.943 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[@__name_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[CreateAt], [s].[CreateBy], [s].[CurrentNo], [s].[Length], [s].[Name], [s].[Prefix], [s].[Suffix], [s].[TenantId], [s].[UpdateAt], [s].[UpdateBy]
FROM [SequenceNo] AS [s]
WHERE [s].[Name] = @__name_0
2025-06-17 15:04:31.947 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:04:31.950 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api) in 87.0972ms
2025-06-17 15:04:31.952 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:04:31.955 -04:00 [INF] HTTP GET /External/SequenceNoByName responded 200 in 108.2057 ms
2025-06-17 15:04:31.961 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - 200 null application/json; charset=utf-8 126.1924ms
2025-06-17 15:04:31.999 -04:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5275/External/UpdateSequenceNo - application/json; charset=utf-8 334
2025-06-17 15:04:32.008 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.UpdateSequenceNoExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:04:32.012 -04:00 [INF] Route matched with {action = "UpdateSequenceNoExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateSequenceNoExt(Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:04:32.036 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:04:32.063 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:04:32.098 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@p10='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (Size = 50), @p2='?' (DbType = Int64), @p3='?' (DbType = Int16), @p4='?' (Size = 255), @p5='?' (Size = 50), @p6='?' (Size = 50), @p7='?' (DbType = Guid), @p8='?' (DbType = DateTime2), @p9='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [SequenceNo] SET [CreateAt] = @p0, [CreateBy] = @p1, [CurrentNo] = @p2, [Length] = @p3, [Name] = @p4, [Prefix] = @p5, [Suffix] = @p6, [TenantId] = @p7, [UpdateAt] = @p8, [UpdateBy] = @p9
OUTPUT 1
WHERE [Id] = @p10;
2025-06-17 15:04:32.103 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-17 15:04:32.106 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.UpdateSequenceNoExt (Visfuture.OneTeam.BaseBiz.Api) in 90.6959ms
2025-06-17 15:04:32.111 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.UpdateSequenceNoExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:04:32.114 -04:00 [INF] HTTP PUT /External/UpdateSequenceNo responded 200 in 107.5433 ms
2025-06-17 15:04:32.120 -04:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5275/External/UpdateSequenceNo - 200 null application/json; charset=utf-8 120.8103ms
2025-06-17 15:04:32.420 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-17 15:04:32.426 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:04:32.428 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.8840 ms
2025-06-17 15:04:32.432 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 12.4925ms
2025-06-17 15:04:32.438 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-17 15:04:32.442 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:04:32.443 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:04:32.445 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:04:32.472 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:04:32.500 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:04:32.599 -04:00 [INF] Executed DbCommand (92ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:04:32.605 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:04:32.607 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 156.6164ms
2025-06-17 15:04:32.610 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:04:32.612 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 170.4138 ms
2025-06-17 15:04:32.616 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 177.6408ms
2025-06-17 15:04:44.834 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-17 15:04:44.841 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:04:44.843 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.1771 ms
2025-06-17 15:04:44.846 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 12.6103ms
2025-06-17 15:04:44.852 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-17 15:04:44.856 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:04:44.857 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:04:44.858 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:04:44.883 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:04:44.911 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:04:44.998 -04:00 [INF] Executed DbCommand (81ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:04:45.007 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:04:45.012 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 151.0282ms
2025-06-17 15:04:45.014 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:04:45.016 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 159.9387 ms
2025-06-17 15:04:45.022 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 170.2929ms
2025-06-17 15:05:43.370 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-17 15:05:43.392 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:05:43.394 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.1262 ms
2025-06-17 15:05:43.397 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 28.8844ms
2025-06-17 15:05:43.422 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-17 15:05:43.426 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:05:43.428 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:05:43.430 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:05:43.456 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:05:43.482 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:05:43.573 -04:00 [INF] Executed DbCommand (85ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:05:43.587 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:05:43.594 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 160.7175ms
2025-06-17 15:05:43.600 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:05:43.602 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 175.7961 ms
2025-06-17 15:05:43.606 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 184.0466ms
2025-06-17 15:05:43.690 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-17 15:05:43.698 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:05:43.700 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:05:43.702 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:05:43.726 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:05:43.758 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:05:43.844 -04:00 [INF] Executed DbCommand (80ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:05:43.849 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:05:43.852 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 145.8094ms
2025-06-17 15:05:43.856 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:05:43.859 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 161.0525 ms
2025-06-17 15:05:43.862 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 171.7024ms
2025-06-17 15:07:45.655 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 15:07:45.663 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:07:45.665 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 2.1826 ms
2025-06-17 15:07:45.669 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 14.1393ms
2025-06-17 15:07:45.675 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:07:45.679 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:07:45.680 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:07:45.681 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:07:45.704 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:07:45.729 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:07:45.813 -04:00 [INF] Executed DbCommand (79ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:07:45.821 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:07:45.824 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 140.4901ms
2025-06-17 15:07:45.828 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:07:45.830 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 151.4256 ms
2025-06-17 15:07:45.833 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 158.0831ms
2025-06-17 15:19:07.988 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-17 15:19:08.000 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:08.001 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 1.3802 ms
2025-06-17 15:19:08.003 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 14.9677ms
2025-06-17 15:19:08.009 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 99
2025-06-17 15:19:08.020 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:08.022 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:08.031 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:08.286 -04:00 [INF] Executed DbCommand (84ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:08.315 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:08.397 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-17 15:19:08.406 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:08.411 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 378.3222ms
2025-06-17 15:19:08.414 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:08.415 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 395.4043 ms
2025-06-17 15:19:08.417 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 408.7683ms
2025-06-17 15:19:08.434 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - null null
2025-06-17 15:19:08.438 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:08.439 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.7901 ms
2025-06-17 15:19:08.442 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 204 null null 8.0471ms
2025-06-17 15:19:08.447 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - application/json null
2025-06-17 15:19:08.451 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:08.452 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:08.454 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:08.484 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:08.511 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:08.540 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-17 15:19:08.545 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:08.548 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 90.9739ms
2025-06-17 15:19:08.552 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:08.553 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 101.8357 ms
2025-06-17 15:19:08.554 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 200 null application/json; charset=utf-8 107.696ms
2025-06-17 15:19:08.560 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-17 15:19:08.565 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:08.566 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.7125 ms
2025-06-17 15:19:08.568 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 7.8131ms
2025-06-17 15:19:08.572 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 97
2025-06-17 15:19:08.576 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:08.577 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:08.578 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:08.601 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:08.627 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:08.652 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-17 15:19:08.669 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:08.674 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 93.3217ms
2025-06-17 15:19:08.675 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:08.677 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 101.5131 ms
2025-06-17 15:19:08.679 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 107.0477ms
2025-06-17 15:19:14.745 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-17 15:19:14.748 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:14.749 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 0.7693 ms
2025-06-17 15:19:14.753 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 7.6811ms
2025-06-17 15:19:14.758 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - null null
2025-06-17 15:19:14.766 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 149
2025-06-17 15:19:14.767 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-17 15:19:14.768 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:14.771 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:14.774 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:14.775 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 7.3170 ms
2025-06-17 15:19:14.778 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:14.779 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 4.2602 ms
2025-06-17 15:19:14.781 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 204 null null 24.7454ms
2025-06-17 15:19:14.783 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:14.787 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 20.0379ms
2025-06-17 15:19:14.792 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - application/json null
2025-06-17 15:19:14.807 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-17 15:19:14.810 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:14.813 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:14.814 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:14.815 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:14.817 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:14.818 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:14.845 -04:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:14.884 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:14.923 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:14.966 -04:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:14.999 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-17 15:19:15.005 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:15.006 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 204.2972ms
2025-06-17 15:19:15.008 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:15.011 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 239.5184 ms
2025-06-17 15:19:15.016 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 250.156ms
2025-06-17 15:19:15.017 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:15.025 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-17 15:19:15.031 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:15.032 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:15.033 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:15.055 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:15.071 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:15.087 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-17 15:19:15.092 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:15.101 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 280.5183ms
2025-06-17 15:19:15.104 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:15.106 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 295.6940 ms
2025-06-17 15:19:15.107 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:15.109 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 200 null application/json; charset=utf-8 317.0274ms
2025-06-17 15:19:15.117 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-17 15:19:15.121 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-17 15:19:15.121 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:15.124 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:15.126 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 302.4241ms
2025-06-17 15:19:15.127 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:15.132 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:15.134 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:15.136 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 322.9842 ms
2025-06-17 15:19:15.141 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 334.1792ms
2025-06-17 15:19:15.151 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:19:15.155 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:15.158 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 121.2648ms
2025-06-17 15:19:15.160 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:15.163 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 132.0059 ms
2025-06-17 15:19:15.166 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 140.7587ms
2025-06-17 15:19:15.214 -04:00 [INF] Executed DbCommand (74ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:15.240 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:15.267 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-17 15:19:15.282 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:15.285 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 146.0421ms
2025-06-17 15:19:15.286 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:15.288 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 163.5413 ms
2025-06-17 15:19:15.290 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 169.5817ms
2025-06-17 15:19:22.975 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-17 15:19:22.976 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-17 15:19:22.984 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:22.987 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:22.989 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 7.3189 ms
2025-06-17 15:19:22.991 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 3.6379 ms
2025-06-17 15:19:22.993 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-17 15:19:22.994 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 18.3158ms
2025-06-17 15:19:22.996 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 20.3968ms
2025-06-17 15:19:23.000 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:23.005 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-17 15:19:23.008 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-17 15:19:23.009 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 9.6761 ms
2025-06-17 15:19:23.013 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:23.016 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:23.018 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 25.228ms
2025-06-17 15:19:23.019 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:23.020 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:23.024 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 76
2025-06-17 15:19:23.025 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:23.026 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:23.029 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:23.035 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:23.036 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:23.052 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:23.057 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:23.074 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:23.095 -04:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:23.095 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:23.120 -04:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:23.122 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:19:23.128 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:23.130 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 99.4163ms
2025-06-17 15:19:23.132 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:23.133 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 119.3416 ms
2025-06-17 15:19:23.135 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 130.2198ms
2025-06-17 15:19:23.138 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-17 15:19:23.142 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:23.144 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 110.1001ms
2025-06-17 15:19:23.146 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:23.146 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-17 15:19:23.148 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 131.6350 ms
2025-06-17 15:19:23.153 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 144.1013ms
2025-06-17 15:19:23.167 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:23.171 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 132.2605ms
2025-06-17 15:19:23.174 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:23.175 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 146.1044 ms
2025-06-17 15:19:23.178 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 153.7272ms
2025-06-17 15:19:35.825 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-17 15:19:35.829 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-17 15:19:35.829 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:35.833 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-17 15:19:35.833 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:35.834 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 5.1061 ms
2025-06-17 15:19:35.841 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:35.842 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 8.2665 ms
2025-06-17 15:19:35.844 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 18.6514ms
2025-06-17 15:19:35.845 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 4.6527 ms
2025-06-17 15:19:35.847 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 18.6808ms
2025-06-17 15:19:35.851 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-17 15:19:35.854 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 20.5546ms
2025-06-17 15:19:35.858 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-17 15:19:35.861 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:35.864 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 76
2025-06-17 15:19:35.867 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:35.868 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:35.872 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:35.873 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:35.874 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:35.875 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:35.876 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:35.879 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:35.899 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:35.920 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:35.920 -04:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:35.940 -04:00 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:35.961 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:35.961 -04:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:35.988 -04:00 [INF] Executed DbCommand (44ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:19:35.988 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-17 15:19:35.992 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-17 15:19:35.997 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:36.001 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:36.009 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:36.010 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 132.063ms
2025-06-17 15:19:36.013 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 131.6489ms
2025-06-17 15:19:36.016 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 132.1294ms
2025-06-17 15:19:36.017 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:36.019 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:36.022 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:36.023 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 162.0422 ms
2025-06-17 15:19:36.024 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 156.9985 ms
2025-06-17 15:19:36.025 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 153.3195 ms
2025-06-17 15:19:36.028 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 176.2889ms
2025-06-17 15:19:36.030 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 172.0353ms
2025-06-17 15:19:36.033 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 168.3518ms
2025-06-17 15:19:42.230 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 15:19:42.234 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:42.235 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 0.8970 ms
2025-06-17 15:19:42.237 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 6.7563ms
2025-06-17 15:19:42.242 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:19:42.246 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:19:42.246 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:42.247 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:19:42.308 -04:00 [INF] Executed DbCommand (58ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:19:42.332 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:19:42.416 -04:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:19:42.421 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:19:42.425 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 176.2338ms
2025-06-17 15:19:42.428 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:19:42.429 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 183.9430 ms
2025-06-17 15:19:42.432 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 190.1194ms
2025-06-17 15:20:31.812 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-17 15:20:31.815 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:31.817 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 1.3104 ms
2025-06-17 15:20:31.819 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 7.1017ms
2025-06-17 15:20:31.824 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 99
2025-06-17 15:20:31.836 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:31.837 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:31.839 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:31.892 -04:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:31.919 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:31.955 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-17 15:20:31.961 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:31.964 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 122.361ms
2025-06-17 15:20:31.966 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:31.968 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 132.6698 ms
2025-06-17 15:20:31.972 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 148.5437ms
2025-06-17 15:20:31.991 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - null null
2025-06-17 15:20:31.994 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:31.996 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.1306 ms
2025-06-17 15:20:32.000 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 204 null null 9.2696ms
2025-06-17 15:20:32.005 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - application/json null
2025-06-17 15:20:32.008 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:32.009 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:32.011 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:32.034 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:32.061 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:32.091 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-17 15:20:32.106 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:32.111 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 97.1312ms
2025-06-17 15:20:32.114 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:32.117 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 108.9648 ms
2025-06-17 15:20:32.122 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 200 null application/json; charset=utf-8 117.0819ms
2025-06-17 15:20:32.128 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-17 15:20:32.133 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:32.134 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.1319 ms
2025-06-17 15:20:32.137 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 8.5359ms
2025-06-17 15:20:32.142 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 97
2025-06-17 15:20:32.145 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:32.146 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:32.148 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:32.178 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:32.204 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:32.230 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-17 15:20:32.244 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:32.248 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 96.5866ms
2025-06-17 15:20:32.250 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:32.252 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 107.0661 ms
2025-06-17 15:20:32.256 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 114.4561ms
2025-06-17 15:20:34.924 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 149
2025-06-17 15:20:34.930 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - application/json null
2025-06-17 15:20:34.936 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-17 15:20:34.931 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:34.940 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:34.944 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:34.945 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:34.946 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:34.947 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:34.948 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:34.949 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:34.951 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:34.977 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:34.978 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:34.992 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:35.020 -04:00 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:35.020 -04:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:35.043 -04:00 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:35.046 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-17 15:20:35.050 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-17 15:20:35.052 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:35.057 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 99.1199ms
2025-06-17 15:20:35.057 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:35.059 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:35.061 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 105.7658ms
2025-06-17 15:20:35.062 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 121.4099 ms
2025-06-17 15:20:35.064 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:35.067 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 200 null application/json; charset=utf-8 136.284ms
2025-06-17 15:20:35.069 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-17 15:20:35.076 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-17 15:20:35.069 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 137.7845 ms
2025-06-17 15:20:35.082 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:35.085 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:35.088 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 163.3652ms
2025-06-17 15:20:35.090 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 129.4647ms
2025-06-17 15:20:35.091 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:35.097 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:35.098 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:35.098 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-17 15:20:35.100 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 155.6857 ms
2025-06-17 15:20:35.106 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:35.109 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 173.1892ms
2025-06-17 15:20:35.111 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:35.117 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:35.127 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:35.147 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:35.163 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:35.187 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:35.194 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-17 15:20:35.210 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:35.215 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 112.8094ms
2025-06-17 15:20:35.218 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:35.220 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 134.6310 ms
2025-06-17 15:20:35.221 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:20:35.223 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 149.3364ms
2025-06-17 15:20:35.226 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:35.233 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 112.492ms
2025-06-17 15:20:35.235 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:35.237 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 131.4817 ms
2025-06-17 15:20:35.240 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 142.0519ms
2025-06-17 15:20:40.420 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-17 15:20:40.423 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-17 15:20:40.426 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:40.429 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:40.430 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 4.2269 ms
2025-06-17 15:20:40.431 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 2.0954 ms
2025-06-17 15:20:40.433 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-17 15:20:40.433 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 13.4169ms
2025-06-17 15:20:40.436 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 13.1552ms
2025-06-17 15:20:40.439 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:40.443 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-17 15:20:40.447 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-17 15:20:40.448 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 8.4760 ms
2025-06-17 15:20:40.451 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:40.454 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:40.456 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 23.1521ms
2025-06-17 15:20:40.457 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:40.458 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:40.462 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 76
2025-06-17 15:20:40.463 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:40.464 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:40.467 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:40.473 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:40.475 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:40.510 -04:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:40.533 -04:00 [INF] Executed DbCommand (54ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:40.534 -04:00 [INF] Executed DbCommand (59ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:40.555 -04:00 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:40.566 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:40.579 -04:00 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:40.593 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:20:40.595 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-17 15:20:40.597 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:40.601 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 131.1717ms
2025-06-17 15:20:40.601 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:40.603 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:40.606 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 128.2105ms
2025-06-17 15:20:40.608 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 157.7778 ms
2025-06-17 15:20:40.612 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:40.613 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-17 15:20:40.615 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 172.2606ms
2025-06-17 15:20:40.617 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 149.5438 ms
2025-06-17 15:20:40.621 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:40.628 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 166.727ms
2025-06-17 15:20:40.630 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 157.7948ms
2025-06-17 15:20:40.642 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:40.671 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 217.7192 ms
2025-06-17 15:20:40.676 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 229.5954ms
2025-06-17 15:20:56.613 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-17 15:20:56.620 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:56.623 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 2.5249 ms
2025-06-17 15:20:56.623 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - null null
2025-06-17 15:20:56.627 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 13.8925ms
2025-06-17 15:20:56.632 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:56.633 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 149
2025-06-17 15:20:56.636 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-17 15:20:56.637 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 5.4707 ms
2025-06-17 15:20:56.641 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:56.644 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:56.647 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 204 null null 23.7442ms
2025-06-17 15:20:56.647 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:56.648 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 4.3529 ms
2025-06-17 15:20:56.652 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - application/json null
2025-06-17 15:20:56.654 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:56.656 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 19.7226ms
2025-06-17 15:20:56.659 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:56.667 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-17 15:20:56.668 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:56.672 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:56.674 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:56.674 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:56.678 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:56.682 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:56.702 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:56.706 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:56.721 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:56.737 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:56.737 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:56.746 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-17 15:20:56.753 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:56.756 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 93.9245ms
2025-06-17 15:20:56.759 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:56.760 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 119.4019 ms
2025-06-17 15:20:56.763 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-17 15:20:56.763 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 130.1165ms
2025-06-17 15:20:56.766 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-17 15:20:56.767 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:56.773 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-17 15:20:56.775 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:56.777 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 99.8932ms
2025-06-17 15:20:56.780 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:56.783 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 101.8063ms
2025-06-17 15:20:56.784 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:56.786 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:56.788 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:56.789 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 129.9895 ms
2025-06-17 15:20:56.794 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:56.796 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 124.2271 ms
2025-06-17 15:20:56.798 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 200 null application/json; charset=utf-8 145.772ms
2025-06-17 15:20:56.804 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 137.1763ms
2025-06-17 15:20:56.813 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-17 15:20:56.818 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:56.820 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:56.822 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:56.822 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:56.847 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:56.868 -04:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:56.890 -04:00 [INF] Executed DbCommand (39ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:20:56.894 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:56.897 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 96.295ms
2025-06-17 15:20:56.900 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:56.901 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 120.7432 ms
2025-06-17 15:20:56.904 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 131.2664ms
2025-06-17 15:20:56.946 -04:00 [INF] Executed DbCommand (75ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:56.973 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-17 15:20:56.988 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:56.991 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 163.6737ms
2025-06-17 15:20:56.994 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:56.996 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 178.5987 ms
2025-06-17 15:20:57.000 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 187.3739ms
2025-06-17 15:20:58.227 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-17 15:20:58.228 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-17 15:20:58.233 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:58.235 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 76
2025-06-17 15:20:58.236 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:58.238 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:58.242 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:20:58.243 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:58.244 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:58.246 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:58.248 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:58.253 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:20:58.274 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:58.300 -04:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:58.300 -04:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:20:58.323 -04:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:58.354 -04:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:58.354 -04:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:20:58.407 -04:00 [INF] Executed DbCommand (73ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:20:58.411 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-17 15:20:58.411 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:58.416 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 165.1268ms
2025-06-17 15:20:58.417 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:58.418 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:58.420 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 161.5705ms
2025-06-17 15:20:58.422 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 189.0010 ms
2025-06-17 15:20:58.423 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-17 15:20:58.424 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:58.427 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 199.9391ms
2025-06-17 15:20:58.429 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:20:58.430 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 188.4733 ms
2025-06-17 15:20:58.437 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 180.4382ms
2025-06-17 15:20:58.440 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 204.4848ms
2025-06-17 15:20:58.441 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:20:58.447 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 210.8520 ms
2025-06-17 15:20:58.450 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 222.506ms
2025-06-17 15:21:04.222 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-17 15:21:04.225 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-17 15:21:04.228 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:21:04.232 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:21:04.233 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 4.8487 ms
2025-06-17 15:21:04.234 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 2.2747 ms
2025-06-17 15:21:04.237 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 14.7961ms
2025-06-17 15:21:04.239 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-17 15:21:04.239 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 13.6078ms
2025-06-17 15:21:04.243 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-17 15:21:04.247 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:21:04.250 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-17 15:21:04.255 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:21:04.257 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 9.3192 ms
2025-06-17 15:21:04.260 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:21:04.261 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:21:04.264 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 25.5706ms
2025-06-17 15:21:04.265 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:21:04.266 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:21:04.270 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 76
2025-06-17 15:21:04.271 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:21:04.277 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:21:04.280 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:21:04.282 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:21:04.295 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:21:04.302 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:21:04.315 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:21:04.326 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:21:04.336 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:21:04.353 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:21:04.357 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:21:04.361 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:21:04.363 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 89.8779ms
2025-06-17 15:21:04.365 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:21:04.367 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 111.4821 ms
2025-06-17 15:21:04.370 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 127.2849ms
2025-06-17 15:21:04.373 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-17 15:21:04.377 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:21:04.380 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 100.5607ms
2025-06-17 15:21:04.382 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-17 15:21:04.382 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:21:04.386 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 126.4633 ms
2025-06-17 15:21:04.387 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:21:04.390 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 139.5121ms
2025-06-17 15:21:04.391 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 107.2259ms
2025-06-17 15:21:04.397 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:21:04.399 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 122.6794 ms
2025-06-17 15:21:04.402 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 132.3393ms
2025-06-17 15:38:16.171 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 15:38:16.176 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:38:16.177 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.3638 ms
2025-06-17 15:38:16.180 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 9.583ms
2025-06-17 15:38:16.186 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:38:16.196 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:38:16.198 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:38:16.200 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:38:16.431 -04:00 [INF] Executed DbCommand (83ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:38:16.458 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:38:16.552 -04:00 [INF] Executed DbCommand (85ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:38:16.561 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:38:16.565 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 361.5538ms
2025-06-17 15:38:16.570 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:38:16.573 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 377.5347 ms
2025-06-17 15:38:16.576 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 390.7798ms
2025-06-17 15:41:20.398 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 15:41:20.421 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:41:20.424 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 2.8118 ms
2025-06-17 15:41:20.431 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 32.9208ms
2025-06-17 15:41:20.439 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:41:20.451 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-17 15:41:20.457 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:41:20.467 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:41:20.468 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:41:20.469 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.6221 ms
2025-06-17 15:41:20.471 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:41:20.473 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 21.7738ms
2025-06-17 15:41:20.491 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-17 15:41:20.496 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:41:20.497 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:41:20.497 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:41:20.502 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:41:20.521 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:41:20.553 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:41:20.558 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:41:20.560 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 84.4943ms
2025-06-17 15:41:20.564 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:41:20.566 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 110.5373 ms
2025-06-17 15:41:20.569 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 130.4309ms
2025-06-17 15:41:20.770 -04:00 [INF] Executed DbCommand (88ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:41:20.798 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:41:20.899 -04:00 [INF] Executed DbCommand (89ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:41:20.907 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:41:20.911 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 405.3351ms
2025-06-17 15:41:20.916 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:41:20.918 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 422.8005 ms
2025-06-17 15:41:20.921 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 430.077ms
2025-06-17 15:41:26.971 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 15:41:26.975 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:41:26.976 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.1907 ms
2025-06-17 15:41:26.980 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 9.0645ms
2025-06-17 15:41:26.985 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:41:26.989 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:41:26.990 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:41:26.992 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:41:27.016 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:41:27.041 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:41:27.125 -04:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:41:27.130 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:41:27.133 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 138.29ms
2025-06-17 15:41:27.136 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:41:27.137 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 148.4659 ms
2025-06-17 15:41:27.141 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 155.1746ms
2025-06-17 15:41:40.833 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-17 15:41:40.837 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:41:40.839 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.5138 ms
2025-06-17 15:41:40.841 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 8.6218ms
2025-06-17 15:41:40.850 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-17 15:41:40.853 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:41:40.854 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:41:40.856 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:41:40.878 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:41:40.902 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:41:40.998 -04:00 [INF] Executed DbCommand (89ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:41:41.007 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:41:41.009 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 151.0985ms
2025-06-17 15:41:41.011 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:41:41.014 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 160.3975 ms
2025-06-17 15:41:41.019 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 168.864ms
2025-06-17 15:41:42.809 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-06-17 15:41:42.811 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-06-17 15:41:42.813 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:41:42.816 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:41:42.817 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:41:42.819 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:41:42.820 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:41:42.821 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:41:42.843 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:41:42.857 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:41:42.876 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:41:42.896 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:41:42.915 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-17 15:41:42.919 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:41:42.921 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 97.7584ms
2025-06-17 15:41:42.925 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-06-17 15:41:42.925 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:41:42.930 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:41:42.931 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 117.9888 ms
2025-06-17 15:41:42.934 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 108.1509ms
2025-06-17 15:41:42.937 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 127.8164ms
2025-06-17 15:41:42.940 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:41:42.947 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 131.1148 ms
2025-06-17 15:41:42.951 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 140.028ms
2025-06-17 15:42:05.424 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 15:42:05.439 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:42:05.441 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.9905 ms
2025-06-17 15:42:05.444 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 19.84ms
2025-06-17 15:42:05.450 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:42:05.459 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:42:05.460 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:05.462 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:42:05.513 -04:00 [INF] Executed DbCommand (48ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:42:05.541 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:42:05.632 -04:00 [INF] Executed DbCommand (85ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:42:05.638 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:42:05.641 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 176.7234ms
2025-06-17 15:42:05.643 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:05.646 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 186.4140 ms
2025-06-17 15:42:05.651 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 201.1365ms
2025-06-17 15:42:09.454 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:42:09.460 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:42:09.461 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:09.462 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:42:09.486 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:42:09.513 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:42:09.611 -04:00 [INF] Executed DbCommand (87ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:42:09.621 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:42:09.625 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 159.793ms
2025-06-17 15:42:09.629 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:09.632 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 172.9350 ms
2025-06-17 15:42:09.637 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 183.2336ms
2025-06-17 15:42:11.113 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 15:42:11.118 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:42:11.119 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.1143 ms
2025-06-17 15:42:11.122 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 9.0348ms
2025-06-17 15:42:11.128 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:42:11.134 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:42:11.135 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:11.136 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:42:11.162 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:42:11.190 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:42:11.287 -04:00 [INF] Executed DbCommand (88ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:42:11.294 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:42:11.297 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 158.2669ms
2025-06-17 15:42:11.301 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:11.303 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 168.9014 ms
2025-06-17 15:42:11.306 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 178.7158ms
2025-06-17 15:42:12.946 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:42:12.953 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:42:12.955 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:12.957 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:42:12.982 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:42:13.020 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:42:13.097 -04:00 [INF] Executed DbCommand (68ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:42:13.103 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:42:13.108 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 147.0889ms
2025-06-17 15:42:13.111 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:13.114 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 160.1727 ms
2025-06-17 15:42:13.116 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 169.926ms
2025-06-17 15:42:14.144 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:42:14.151 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:42:14.152 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:14.154 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:42:14.179 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:42:14.208 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:42:14.296 -04:00 [INF] Executed DbCommand (79ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:42:14.305 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:42:14.309 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 152.867ms
2025-06-17 15:42:14.314 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:14.318 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 166.2731 ms
2025-06-17 15:42:14.321 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 177.5392ms
2025-06-17 15:42:51.271 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 15:42:51.286 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:42:51.287 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.6299 ms
2025-06-17 15:42:51.291 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 20.4008ms
2025-06-17 15:42:51.298 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:42:51.309 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:42:51.311 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:51.313 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:42:51.358 -04:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:42:51.384 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:42:51.471 -04:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:42:51.477 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:42:51.480 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 163.2621ms
2025-06-17 15:42:51.485 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:51.487 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 177.6892 ms
2025-06-17 15:42:51.490 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 191.6526ms
2025-06-17 15:42:53.496 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 15:42:53.503 -04:00 [INF] CORS policy execution successful.
2025-06-17 15:42:53.505 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:53.508 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 15:42:53.532 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 15:42:53.565 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 15:42:53.658 -04:00 [INF] Executed DbCommand (87ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 15:42:53.663 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 15:42:53.666 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 154.4061ms
2025-06-17 15:42:53.669 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 15:42:53.671 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 168.6886 ms
2025-06-17 15:42:53.675 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 178.5522ms
2025-06-17 21:34:27.041 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 21:34:27.060 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:34:27.063 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 5.0499 ms
2025-06-17 21:34:27.067 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 29.5432ms
2025-06-17 21:34:27.084 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 21:34:27.090 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:34:27.095 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:34:27.100 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 21:34:27.434 -04:00 [INF] Executed DbCommand (81ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 21:34:27.467 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 21:34:27.583 -04:00 [INF] Executed DbCommand (92ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 21:34:27.598 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 21:34:27.606 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 502.0594ms
2025-06-17 21:34:27.610 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:34:27.616 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 526.7849 ms
2025-06-17 21:34:27.620 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 536.8616ms
2025-06-17 21:34:49.915 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 21:34:49.920 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:34:49.922 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.6208 ms
2025-06-17 21:34:49.925 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 9.4965ms
2025-06-17 21:34:49.931 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 21:34:49.946 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:34:49.947 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:34:49.949 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 21:34:49.980 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 21:34:50.010 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 21:34:50.106 -04:00 [INF] Executed DbCommand (91ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 21:34:50.142 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 21:34:50.158 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 204.9809ms
2025-06-17 21:34:50.161 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:34:50.163 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 217.3320 ms
2025-06-17 21:34:50.170 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 239.1768ms
2025-06-17 21:37:32.014 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 21:37:32.025 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:37:32.027 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 2.2568 ms
2025-06-17 21:37:32.031 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 17.4068ms
2025-06-17 21:37:32.045 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 21:37:32.052 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:37:32.055 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:37:32.057 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 21:37:32.087 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 21:37:32.116 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 21:37:32.220 -04:00 [INF] Executed DbCommand (95ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 21:37:32.226 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 21:37:32.229 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 168.1523ms
2025-06-17 21:37:32.231 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:37:32.234 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 184.3405 ms
2025-06-17 21:37:32.238 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 192.904ms
2025-06-17 21:41:33.487 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 21:41:33.500 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:41:33.503 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 2.9559 ms
2025-06-17 21:41:33.508 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 22.9115ms
2025-06-17 21:41:33.518 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 21:41:33.523 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:41:33.527 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:41:33.531 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 21:41:33.817 -04:00 [INF] Executed DbCommand (81ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 21:41:33.843 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 21:41:33.950 -04:00 [INF] Executed DbCommand (92ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 21:41:33.958 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 21:41:33.960 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 425.5716ms
2025-06-17 21:41:33.962 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:41:33.964 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 441.5217 ms
2025-06-17 21:41:33.970 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 452.4635ms
2025-06-17 21:45:02.325 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-17 21:45:02.773 -04:00 [INF] Now listening on: http://localhost:5275
2025-06-17 21:45:02.842 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-17 21:45:02.844 -04:00 [INF] Hosting environment: Development
2025-06-17 21:45:02.846 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-06-17 21:45:14.844 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 21:45:14.960 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:45:14.970 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 70.2543 ms
2025-06-17 21:45:14.996 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 166.4157ms
2025-06-17 21:45:15.007 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 21:45:15.018 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:45:15.193 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:45:15.241 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 21:45:18.130 -04:00 [INF] Executed DbCommand (115ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 21:45:18.196 -04:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 21:45:18.309 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 3058.2635ms
2025-06-17 21:45:18.316 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:45:18.363 -04:00 [INF] HTTP POST /SequenceNoList responded 499 in 3346.6986 ms
2025-06-17 21:45:18.385 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 499 null application/problem+json 3377.9985ms
2025-06-17 21:45:19.158 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 21:45:19.168 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:45:19.173 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:45:19.176 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 21:45:19.234 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 21:45:19.268 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 21:45:19.978 -04:00 [INF] Executed DbCommand (116ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 21:45:20.033 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 21:45:20.063 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 883.1653ms
2025-06-17 21:45:20.066 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:45:20.072 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 904.7968 ms
2025-06-17 21:45:20.079 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 921.1506ms
2025-06-17 21:45:32.651 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 21:45:32.657 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:45:32.660 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 3.5610 ms
2025-06-17 21:45:32.667 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 15.3958ms
2025-06-17 21:45:32.674 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 21:45:32.679 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:45:32.687 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:45:32.691 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 21:45:32.824 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 21:45:32.852 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 21:45:32.937 -04:00 [INF] Executed DbCommand (76ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 21:45:32.947 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 21:45:32.951 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 254.97ms
2025-06-17 21:45:32.954 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:45:32.959 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 279.4421 ms
2025-06-17 21:45:32.964 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 289.7064ms
2025-06-17 21:45:49.583 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 21:45:49.590 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:45:49.593 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 2.6538 ms
2025-06-17 21:45:49.598 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 14.7531ms
2025-06-17 21:45:49.604 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 21:45:49.611 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:45:49.614 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:45:49.616 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 21:45:49.642 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 21:45:49.673 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 21:45:49.781 -04:00 [INF] Executed DbCommand (98ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 21:45:49.788 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 21:45:49.790 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 170.0804ms
2025-06-17 21:45:49.793 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:45:49.796 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 185.4147 ms
2025-06-17 21:45:49.801 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 196.829ms
2025-06-17 21:47:20.310 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/AddSequenceNo - null null
2025-06-17 21:47:20.337 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:47:20.339 -04:00 [INF] HTTP OPTIONS /AddSequenceNo responded 204 in 2.0450 ms
2025-06-17 21:47:20.341 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/AddSequenceNo - 204 null null 36.0149ms
2025-06-17 21:47:20.345 -04:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5275/AddSequenceNo - application/json 57
2025-06-17 21:47:20.350 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:47:20.351 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.SaveSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:47:20.361 -04:00 [INF] Route matched with {action = "SaveSequenceNo", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] SaveSequenceNo(Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 21:47:20.388 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 21:47:20.416 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 21:47:20.451 -04:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-17 21:47:20.490 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.SaveSequenceNo (Visfuture.OneTeam.BaseBiz.Api) in 125.0855ms
2025-06-17 21:47:20.497 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.SaveSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:47:20.500 -04:00 [INF] HTTP PUT /AddSequenceNo responded 400 in 150.1007 ms
2025-06-17 21:47:20.503 -04:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5275/AddSequenceNo - 400 null application/problem+json; charset=utf-8 157.3239ms
2025-06-17 21:48:36.536 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 21:48:36.550 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:48:36.554 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 4.0835 ms
2025-06-17 21:48:36.559 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 27.4794ms
2025-06-17 21:48:36.580 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 21:48:36.586 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:48:36.588 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:48:36.589 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 21:48:36.644 -04:00 [INF] Executed DbCommand (49ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 21:48:36.669 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 21:48:36.764 -04:00 [INF] Executed DbCommand (87ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 21:48:36.773 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 21:48:36.775 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 182.2053ms
2025-06-17 21:48:36.777 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:48:36.780 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 194.6082 ms
2025-06-17 21:48:36.784 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 204.3487ms
2025-06-17 21:48:44.486 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 21:48:44.492 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:48:44.494 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.8932 ms
2025-06-17 21:48:44.498 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 12.2312ms
2025-06-17 21:48:44.505 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 21:48:44.512 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:48:44.514 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:48:44.516 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 21:48:44.572 -04:00 [INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 21:48:44.600 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 21:48:44.695 -04:00 [INF] Executed DbCommand (87ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 21:48:44.700 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 21:48:44.703 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 182.9683ms
2025-06-17 21:48:44.705 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:48:44.709 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 196.9323 ms
2025-06-17 21:48:44.712 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 206.6864ms
2025-06-17 21:49:39.663 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-17 21:49:39.683 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:49:39.685 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 2.5580 ms
2025-06-17 21:49:39.690 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 27.7398ms
2025-06-17 21:49:39.712 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-17 21:49:39.718 -04:00 [INF] CORS policy execution successful.
2025-06-17 21:49:39.723 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:49:39.726 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-17 21:49:39.752 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-17 21:49:39.783 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-17 21:49:39.868 -04:00 [INF] Executed DbCommand (77ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-17 21:49:39.877 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 21:49:39.887 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 157.5993ms
2025-06-17 21:49:39.891 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-17 21:49:39.894 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 176.4697 ms
2025-06-17 21:49:39.900 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 187.8693ms
