﻿using System.Text;

namespace Visfuture.OneTeam.Core.Common.Helpers;

public class ExternalRequestUtils
{
    public static async Task<HttpContent> Send(IHttpClientFactory httpClientFactory, string url, string method,
        string body)
    {
        HttpClient client = httpClientFactory.CreateClient();
        client.BaseAddress = new Uri(url);
        RsaHelper rsaHelper = new(true);
        string signature = rsaHelper.Sign(body);
        rsaHelper.Dispose();
        HttpRequestMessage request = new(new HttpMethod(method), url)
        {
            Headers =
            {
                { "Accept", "application/json" },
                //{ "Content-Type", "application/json" },
                { "Signature", signature }
            },
            Content = new StringContent(body, Encoding.UTF8, "application/json")
        };
        HttpResponseMessage response = await client.SendAsync(request);
        if (!response.IsSuccessStatusCode) throw new Exception($"Error: {response.StatusCode}");
        return response.Content;
    }
}