using Microsoft.AspNetCore.Mvc;
using Visfuture.OneTeam.Core.Common.Base.Controller;
using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Project.BusinessLogic.DTOs;
using Visfuture.OneTeam.Project.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.Project.InternalService.Interfaces;

namespace Visfuture.OneTeam.Project.Api.Controllers;

public class CompanyController(ICompanyService companyService) : BaseController<CompanyController>()
{
    private readonly ICompanyService companyService = companyService;

    #region Company
    [HttpPost("CompanyList")]
    public async Task<IActionResult> QueryCompanyListAsync([FromBody] BaseQuery<CompanyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<CompanyListItemDto> result = await companyService.QueryCompanyAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("CompanyDetail")]
    public async Task<IActionResult> GetCompanyAsync(Guid companyId, CancellationToken cancellationToken = default)
    {
        EntityResponse<CompanyDto> result = await companyService.GetCompanyAsync(companyId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddCompany")]
    public async Task<IActionResult> AddCompanyAsync(CompanyDto companyDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await companyService.AddCompanyAsync(companyDto, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateCompany")]
    public async Task<IActionResult> UpdateCompanyAsync(CompanyDto companyDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await companyService.UpdateCompanyAsync(companyDto, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteCompany")]
    public async Task<IActionResult> DeleteCompanyAsync(Guid companyId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await companyService.DeleteCompanyAsync(companyId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddCompanyList")]
    public async Task<IActionResult> AddCompaniesAsync([FromBody] BaseQuery<CompanyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await companyService.AddCompaniesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateCompanyList")]
    public async Task<IActionResult> UpdateCompaniesAsync([FromBody] BaseQuery<CompanyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await companyService.UpdateCompaniesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteCompanyList")]
    public async Task<IActionResult> DeleteCompaniesAsync([FromBody] BaseQuery<CompanyDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await companyService.DeleteCompaniesAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportCompanyList")]
    public async Task<IActionResult> ExportCompanyExcel([FromBody] BaseQuery<CompanyDto> request, CancellationToken cancellationToken = default)
    {
        var result = await companyService.ExportCompanyExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportCompanyList")]
    public async Task<IActionResult> ImportCompanyExcel(B64File file, CancellationToken cancellationToken = default)
    {
        var result = await companyService.ImportCompanyExcel(file, cancellationToken);
        return Ok(result);
    }
    #endregion

    #region Company Contact
    [HttpPost("ContactList")]
    public async Task<IActionResult> QueryContactListAsync([FromBody] BaseQuery<ContactDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<ContactListItemDto> result = await companyService.QueryContactAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("ContactDetail")]
    public async Task<IActionResult> GetContactAsync(Guid contactId, CancellationToken cancellationToken = default)
    {
        EntityResponse<ContactDto> result = await companyService.GetContactAsync(contactId, cancellationToken);
        return Ok(result);
    }

    [HttpPut("AddContact")]
    public async Task<IActionResult> AddContactAsync(ContactDto contactDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await companyService.AddContactAsync(contactDto, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateContact")]
    public async Task<IActionResult> UpdateContactAsync(ContactDto contactDto, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await companyService.UpdateContactAsync(contactDto, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteContact")]
    public async Task<IActionResult> DeleteContactAsync(Guid contactId, CancellationToken cancellationToken = default)
    {
        EntityResponse<Guid> result = await companyService.DeleteContactAsync(contactId, cancellationToken);
        return Ok(result);
    }

    [HttpPost("AddContactList")]
    public async Task<IActionResult> AddContactsAsync([FromBody] BaseQuery<ContactDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await companyService.AddContactsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPut("UpdateContactList")]
    public async Task<IActionResult> UpdateContactsAsync([FromBody] BaseQuery<ContactDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await companyService.UpdateContactsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpDelete("DeleteContactList")]
    public async Task<IActionResult> DeleteContactsAsync([FromBody] BaseQuery<ContactDto> request, CancellationToken cancellationToken = default)
    {
        EntityResponse result = await companyService.DeleteContactsAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ExportContactList")]
    public async Task<IActionResult> ExportContactExcel([FromBody] BaseQuery<ContactDto> request, CancellationToken cancellationToken = default)
    {
        var result = await companyService.ExportContactExcel(request, cancellationToken);
        return Ok(result);
    }

    [HttpPost("ImportContactList")]
    public async Task<IActionResult> ImportContactExcel(B64File file, CancellationToken cancellationToken = default)
    {
        var result = await companyService.ImportContactExcel(file, cancellationToken);
        return Ok(result);
    }

    [HttpGet("GetContactsByEmail")]
    public async Task<IActionResult> GetContactsByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        var result = await companyService.GetContactsByEmailAsync(email, cancellationToken);
        return Ok(result);
    }
    #endregion
}
