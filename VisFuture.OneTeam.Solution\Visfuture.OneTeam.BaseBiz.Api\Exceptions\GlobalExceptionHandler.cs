﻿using System.Diagnostics;
using Microsoft.AspNetCore.Diagnostics;


namespace Visfuture.OneTeam.BaseBiz.Api.Exceptions;

internal sealed class GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger, IHostEnvironment e) : IExceptionHandler
{
    private readonly ILogger<GlobalExceptionHandler> _logger = logger;

    public async ValueTask<bool> TryHandleAsync(
        HttpContext httpContext,
        Exception exception,
        CancellationToken cancellationToken)
    {
        var traceId = Activity.Current?.Id ?? httpContext.TraceIdentifier;
        _logger.LogError(
            exception,
            "Could not process a request on machine {MachineName}. TraceId: {TraceId}",
            Environment.MachineName,
            traceId
            );

        var (statusCode, title) = MapException(exception);

        httpContext.Response.ContentType = "application/problem+json";

        await Results.Problem(
            detail: (e.IsDevelopment() || e.IsStaging()) ? exception.StackTrace : null,
            type: exception.GetType().Name,
            title: title,
            statusCode: statusCode,
            extensions: new Dictionary<string, object?>
            {
                {"traceId", traceId}
            }
            ).ExecuteAsync(httpContext);

        return true;
    }

    private static (int StatusCode, string Title) MapException(Exception exception)
    {
        // Handles exceptions that you thrown from the application.
        return exception switch
        {
            ArgumentOutOfRangeException => (StatusCodes.Status400BadRequest, exception.Message),
            AccessViolationException => (StatusCodes.Status401Unauthorized, "Access Denied."),
            UnauthorizedAccessException => (StatusCodes.Status403Forbidden, "Forbidden."),
            KeyNotFoundException => (StatusCodes.Status404NotFound, "Resource not found."),
            _ => (StatusCodes.Status500InternalServerError, "Internal Server Error.")
        };
    }
}

// This class can only existed in the webapi project as it requires Microsoft.AspNetCore.Http framework 