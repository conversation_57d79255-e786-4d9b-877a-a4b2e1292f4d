﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;

public partial class OrganizationHierarchyDto : TenantBaseDto
{
    public string Code { get; set; } = null!;

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public bool IsActive { get; set; }

    public string MainType { get; set; } = null!;

    public string SubType { get; set; } = null!;

    public Guid? SuperiorId { get; set; }

    public string? DefaultLocation { get; set; }

    public string? DefaultWorkSchedule { get; set; }

    public virtual ICollection<AssignableRoleDto> AssignableRoles { get; set; } = [];

    public virtual ICollection<OrganizationHierarchyDto> InverseSuperior { get; set; } = [];

    public virtual ICollection<OrganizationEmployeeDto> OrganizationEmployees { get; set; } = [];

}
