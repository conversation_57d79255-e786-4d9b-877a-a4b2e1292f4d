﻿using System.Security.Cryptography;
using Microsoft.AspNetCore.Mvc;
using Minio;
using Minio.DataModel;
using Minio.DataModel.Args;
using Minio.DataModel.Encryption;
using Minio.DataModel.Response;
using Minio.DataModel.Tags;
using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Core.Common.Helpers;

namespace Visfuture.OneTeam.BaseBiz.Api.Controllers;

[ApiController]
[Route("[controller]")]
public class FileController(IMinioClient minioClient, IConfiguration configuration) : ControllerBase
{
    [HttpPost("Upload")]
    public async Task<IActionResult> Upload(B64File file)
    {
        try
        {
            // string ext = file.FileName.Split('.').Last();
            // if (ext.Length == file.FileName.Length) ext = "";
            string
                fileId = HashUtils.HashHex(file.Data); //+ ext;
            // TODO: test whether file preview works without extension
            StatObjectArgs statObjectArgs = new StatObjectArgs()
                .WithBucket(configuration["Minio:BucketName"])
                .WithObject(fileId);
            ObjectStat objectStat = await minioClient.StatObjectAsync(statObjectArgs);
            if (objectStat.ETag != null) return Ok(fileId);

            Aes aesEncryption = Aes.Create();
            aesEncryption.KeySize = 256;
            aesEncryption.GenerateKey();
            SSEC ssec = new(aesEncryption.Key);
            Progress<ProgressReport> progress = new(progressReport =>
            {
                // Progress events are delivered asynchronously (see remark below)
                Console.WriteLine(
                    $"Percentage: {progressReport.Percentage}% TotalBytesTransferred: {progressReport.TotalBytesTransferred} bytes");
                if (progressReport.Percentage != 100)
                    Console.SetCursorPosition(0, Console.CursorTop - 1);
                else Console.WriteLine();
            });
            MemoryStream stream = new(Convert.FromBase64String(file.Data));
            PutObjectArgs putObjectArgs = new PutObjectArgs()
                .WithBucket(configuration["Minio:BucketName"])
                .WithStreamData(stream)
                .WithObject(fileId)
                .WithContentType(file.MimeType)
                .WithServerSideEncryption(ssec)
                .WithObjectSize(stream.Length)
                .WithProgress(progress);
            PutObjectResponse? resp = await minioClient.PutObjectAsync(putObjectArgs);
            stream.Close();
            SetObjectTagsArgs args = new SetObjectTagsArgs()
                .WithBucket(configuration["Minio:BucketName"])
                .WithObject(fileId)
                .WithTagging(new Tagging(new Dictionary<string, string>
                {
                    { "UploadName", file.FileName }
                }, true));
            await minioClient.SetObjectTagsAsync(args);

            Console.WriteLine("File is uploaded successfully");
            return Ok(fileId);
        }
        catch (Exception e)
        {
            return Problem(e.Message);
        }
    }

    [HttpGet("GetLink")]
    public async Task<IActionResult> GetLink(string id)
    {
        return Ok(await minioClient.PresignedGetObjectAsync(new PresignedGetObjectArgs()
            .WithBucket(configuration["Minio:BucketName"])
            .WithObject(id)
        ).ConfigureAwait(false));
    }

    [HttpGet("Delete")]
    public async Task<IActionResult> Delete(string fileId)
    {
        try
        {
            RemoveObjectArgs args = new RemoveObjectArgs()
                .WithBucket(configuration["Minio:BucketName"])
                .WithObject(fileId);
            await minioClient.RemoveObjectAsync(args);
            return Ok();
        }
        catch (Exception e)
        {
            return Problem(e.Message);
        }
    }
}