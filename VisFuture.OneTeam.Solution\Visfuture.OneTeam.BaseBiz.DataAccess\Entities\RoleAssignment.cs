﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.DataAccess.Entities;

public partial class RoleAssignment : TenantEntity
{
    public Guid AssignableRoleId { get; set; }

    public Guid EmployeeId { get; set; }

    public bool IsInheritable { get; set; }

    public virtual AssignableRole AssignableRole { get; set; } = null!;

    public virtual Employee Employee { get; set; } = null!;
}
