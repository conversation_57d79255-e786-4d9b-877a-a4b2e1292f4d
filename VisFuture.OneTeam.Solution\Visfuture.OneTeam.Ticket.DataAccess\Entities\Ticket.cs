﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.Ticket.DataAccess.Entities;

public class Ticket : CodeEntity
{
    public Guid ProjectId { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public string MainType { get; set; } = null!;

    public string? SubType { get; set; }

    public string Status { get; set; } = null!;

    public string Priority { get; set; } = null!;

    public string Source { get; set; } = null!;

    public string? SourceId { get; set; }

    public DateTime RaisedOn { get; set; }

    public string? RaisedBy { get; set; }

    public DateTime? EndDate { get; set; }

    public string? Assignee { get; set; }

    public Guid? CustomerId { get; set; }

    public Guid? ContractId { get; set; }

    public string? ReviewFlag { get; set; }

    public string? Category { get; set; }

    public string? Module { get; set; }

    public bool IsPublic { get; set; }

    public string? BillMethod { get; set; }

    public bool Billable { get; set; }

    public short WorkMinutes { get; set; }

    public virtual ICollection<TicketBilling> TicketBillings { get; set; } = [];

    public virtual ICollection<TicketDevOpsLink> TicketDevOpsLinks { get; set; } = [];

    public virtual ICollection<TicketDiscussion> TicketDiscussions { get; set; } = [];

    public virtual ICollection<TicketLink> TicketLinkLinkeds { get; set; } = [];

    public virtual ICollection<TicketLink> TicketLinkTickets { get; set; } = [];

    public virtual ICollection<TicketReview> TicketReviews { get; set; } = [];
}