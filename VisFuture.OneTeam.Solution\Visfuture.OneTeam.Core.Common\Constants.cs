﻿namespace Visfuture.OneTeam.Core.Common;

public static class Constants
{
    public static class DateFormats
    {
        public const string ShortDateFormat = "dd-MMM-yyyy";
        public const string LongDateFormat = "MMMM dd, yyyy";
        public const string LongTimeFormat = "HH:mm:ss";
        public const string ShortTimeFormat = "HH:mm";
        public const string StandardtDateFormat = "yyyy-MM-dd";

        //Copied from old solution
        public const string DateFormat = "MM/dd/yyyy";
        public const string DateTimeFormat = "MM/dd/yyyy HH:mm:ss";
        public const string DateTimeMillisecondsFormat = "yyyy-MM-dd HH:mm:ss.fff";
    }

    public static class Validation
    {
        public const string UrlRegex = @"^https?:\/\/(([a-zA-Z0-9]+-?)+[a-zA-Z0-9]+\.)+(([a-zA-Z0-9]+-?)+[a-zA-Z0-9]+)";
        public const string NumberRegex = @"^[1-9]\d*$";
    }
}
