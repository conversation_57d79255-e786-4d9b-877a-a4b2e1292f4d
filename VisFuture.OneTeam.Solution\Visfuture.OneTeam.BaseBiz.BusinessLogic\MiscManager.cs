﻿using Mapster;
using MapsterMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Interfaces;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests;
using Visfuture.OneTeam.BaseBiz.DataAccess;
using Visfuture.OneTeam.BaseBiz.DataAccess.DbContext;
using Visfuture.OneTeam.BaseBiz.DataAccess.Entities;
using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Core.Common.Helpers;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic;

public class MiscManager(
    IConfiguration configuration,
    IHttpClientFactory httpClientFactory,
    IHttpContextAccessor httpContextAccessor,
    AppDataContext appDataContext,
    IMapper mapper)
    : BaseBizBaseManager(httpContextAccessor, appDataContext, mapper), IMiscManager
{
    private readonly CrudHelper<CodeItemAttribute, CodeItemAttributeDto, CodeItemAttributeListItemDto>
        _codeItemAttributeCrudHelper =
            new(configuration, httpClientFactory, appDataContext, appDataContext.CodeItemAttributes);

    private readonly CrudHelper<CodeItem, CodeItemDto, CodeItemListItemDto> _codeItemCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.CodeItems);

    private readonly CrudHelper<CodeType, CodeTypeDto, CodeTypeListItemDto> _codeTypeCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.CodeTypes);

    private readonly CrudHelper<I18nKey, I18nKeyDto, I18nKeyListItemDto> _i18nKeyCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.I18nKeys);

    private readonly CrudHelper<I18nTranslation, I18nTranslationDto, I18nTranslationListItemDto>
        _i18nTranslationCrudHelper =
            new(configuration, httpClientFactory, appDataContext, appDataContext.I18nTranslations);

    private readonly CrudHelper<NotificationTemplate, NotificationTemplateDto, NotificationTemplateListItemDto>
        _notificationTemplateCrudHelper =
            new(configuration, httpClientFactory, appDataContext, appDataContext.NotificationTemplates);

    private readonly CrudHelper<SequenceNo, SequenceNoDto, SequenceNoListItemDto> _sequenceNoCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.SequenceNos);

    #region I18nKey

    public async Task<EntityResponse<I18nKeyDto>> GetI18nKeyByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _i18nKeyCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddI18nKeyAsync(I18nKeyDto i18nKeyDto,
        CancellationToken cancellationToken = default)
    {
        return await _i18nKeyCrudHelper.AddEntity(GetCurrentUser(), i18nKeyDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateI18nKeyAsync(I18nKeyDto i18nKeyDto,
        CancellationToken cancellationToken = default)
    {
        return await _i18nKeyCrudHelper.UpdateEntity(GetCurrentUser(), i18nKeyDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteI18nKeyAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _i18nKeyCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<I18nKeyListItemDto>> QueryI18nKeyAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _i18nKeyCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse> AddI18nKeysAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _i18nKeyCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateI18nKeysAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _i18nKeyCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteI18nKeysAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _i18nKeyCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportI18nKeyExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _i18nKeyCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportI18nKeyExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _i18nKeyCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region I18nTranslation

    public async Task<EntityResponse<I18nTranslationDto>> GetI18nTranslationByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _i18nTranslationCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddI18nTranslationAsync(I18nTranslationDto i18nTranslationDto,
        CancellationToken cancellationToken = default)
    {
        return await _i18nTranslationCrudHelper.AddEntity(GetCurrentUser(), i18nTranslationDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateI18nTranslationAsync(I18nTranslationDto i18nTranslationDto,
        CancellationToken cancellationToken = default)
    {
        return await _i18nTranslationCrudHelper.UpdateEntity(GetCurrentUser(), i18nTranslationDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteI18nTranslationAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _i18nTranslationCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<I18nTranslationListItemDto>> QueryI18nTranslationAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _i18nTranslationCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse> AddI18nTranslationsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _i18nTranslationCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateI18nTranslationsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _i18nTranslationCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteI18nTranslationsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _i18nTranslationCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportI18nTranslationExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _i18nTranslationCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportI18nTranslationExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _i18nTranslationCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region NotificationTemplate

    public async Task<EntityResponse<NotificationTemplateDto>> GetNotificationTemplateByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _notificationTemplateCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddNotificationTemplateAsync(
        NotificationTemplateDto notificationTemplateDto, CancellationToken cancellationToken = default)
    {
        return await _notificationTemplateCrudHelper.AddEntity(GetCurrentUser(), notificationTemplateDto,
            cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateNotificationTemplateAsync(
        NotificationTemplateDto notificationTemplateDto, CancellationToken cancellationToken = default)
    {
        return await _notificationTemplateCrudHelper.UpdateEntity(GetCurrentUser(), notificationTemplateDto,
            cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteNotificationTemplateAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _notificationTemplateCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<NotificationTemplateListItemDto>> QueryNotificationTemplateAsync(
        BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _notificationTemplateCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse> AddNotificationTemplatesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _notificationTemplateCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateNotificationTemplatesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _notificationTemplateCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteNotificationTemplatesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _notificationTemplateCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportNotificationTemplateExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _notificationTemplateCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportNotificationTemplateExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _notificationTemplateCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    public async Task<EntityResponse<NotificationTemplateDto?>> GetNotificationTemplateByNotificationTypeAndNotificationMethod(
        string notificationType,
        string notificationMethod,
        CancellationToken cancellationToken = default
        )
    {
        //var tenantId = GetCurrentUser().TenantId;
        var tenantId = new Guid("4E369EE8-EDF1-41F5-B478-6707ADBB6D9D"); // task-4166 hardcode tenant ID after changes add in commit 98d25a3

        var notificationTemplate = await appDataContext.NotificationTemplates
            .AsNoTracking()
            .Where(p => p.TenantId == tenantId
                && p.NotificationType == notificationType
                && p.NotificationMethod == notificationMethod
            )
            .Select(p => p.Adapt<NotificationTemplateDto>())
            .FirstOrDefaultAsync(cancellationToken);

        return EntityResponse<NotificationTemplateDto?>.Success(notificationTemplate);
    }

    #endregion

    #region SequenceNo

    public async Task<EntityResponse<SequenceNoDto>> GetSequenceNoByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _sequenceNoCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<SequenceNoDto>> GetSequenceNoByNameAsync(string name,
        CancellationToken cancellationToken = default)
    {
        return await _sequenceNoCrudHelper.GetEntityBy(t => t.Name == name, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddSequenceNoAsync(SequenceNoDto sequenceNoDto,
        CancellationToken cancellationToken = default)
    {
        return await _sequenceNoCrudHelper.AddEntity(GetCurrentUser(), sequenceNoDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateSequenceNoAsync(SequenceNoDto sequenceNoDto,
        CancellationToken cancellationToken = default)
    {
        return await _sequenceNoCrudHelper.UpdateEntity(GetCurrentUser(), sequenceNoDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteSequenceNoAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _sequenceNoCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<SequenceNoListItemDto>> QuerySequenceNoAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _sequenceNoCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse> AddSequenceNosAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _sequenceNoCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateSequenceNosAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _sequenceNoCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteSequenceNosAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _sequenceNoCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportSequenceNoExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _sequenceNoCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportSequenceNoExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _sequenceNoCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region CodeItem

    public async Task<EntityResponse<CodeItemDto>> GetCodeItemByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddCodeItemAsync(CodeItemDto codeItemDto,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemCrudHelper.AddEntity(GetCurrentUser(), codeItemDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateCodeItemAsync(CodeItemDto codeItemDto,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemCrudHelper.UpdateEntity(GetCurrentUser(), codeItemDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteCodeItemAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _codeItemCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<CodeItemListItemDto>> QueryCodeItemAsync(CodeItemRequest request,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemCrudHelper.GetEntityPaged(request, cancellationToken, t => true, query =>
        {
            if (request.UnderSuperiorOf == null) return query;
            Guid? superiorId = DB.CodeTypes.FirstOrDefault(t =>
                t.Id == request.UnderSuperiorOf)?.SuperiorId;
            if (superiorId == null) return query;
            query = query.Where(t => t.CodeTypeId == superiorId);
            return query;
        });
    }

    public async Task<EntityResponse> AddCodeItemsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateCodeItemsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteCodeItemsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportCodeItemExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportCodeItemExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _codeItemCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region CodeItemAttribute

    public async Task<EntityResponse<CodeItemAttributeDto>> GetCodeItemAttributeByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemAttributeCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddCodeItemAttributeAsync(CodeItemAttributeDto codeItemAttributeDto,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemAttributeCrudHelper.AddEntity(GetCurrentUser(), codeItemAttributeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateCodeItemAttributeAsync(CodeItemAttributeDto codeItemAttributeDto,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemAttributeCrudHelper.UpdateEntity(GetCurrentUser(), codeItemAttributeDto,
            cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteCodeItemAttributeAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemAttributeCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<CodeItemAttributeListItemDto>> QueryCodeItemAttributeAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemAttributeCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse> AddCodeItemAttributesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemAttributeCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateCodeItemAttributesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemAttributeCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteCodeItemAttributesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemAttributeCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportCodeItemAttributeExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemAttributeCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportCodeItemAttributeExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _codeItemAttributeCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region CodeType

    public async Task<EntityResponse<CodeTypeDto>> GetCodeTypeByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _codeTypeCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<CodeTypeDto>> GetCodeTypeByTypeCodeAsync(string typeCode,
        CancellationToken cancellationToken)
    {
        return await _codeTypeCrudHelper.GetEntityBy(t => t.TypeCode == typeCode, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddCodeTypeAsync(CodeTypeDto codeTypeDto,
        CancellationToken cancellationToken = default)
    {
        return await _codeTypeCrudHelper.AddEntity(GetCurrentUser(), codeTypeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateCodeTypeAsync(CodeTypeDto codeTypeDto,
        CancellationToken cancellationToken = default)
    {
        return await _codeTypeCrudHelper.UpdateEntity(GetCurrentUser(), codeTypeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteCodeTypeAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _codeTypeCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<CodeTypeListItemDto>> QueryCodeTypeAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeTypeCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse> AddCodeTypesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeTypeCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateCodeTypesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeTypeCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteCodeTypesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeTypeCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportCodeTypeExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _codeTypeCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportCodeTypeExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _codeTypeCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion
}