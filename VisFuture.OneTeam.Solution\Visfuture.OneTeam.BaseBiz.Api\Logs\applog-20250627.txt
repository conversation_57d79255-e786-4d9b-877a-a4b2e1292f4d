2025-06-27 14:28:23.614 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-27 14:28:23.844 -04:00 [INF] Now listening on: http://localhost:5275
2025-06-27 14:28:23.885 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-27 14:28:23.886 -04:00 [INF] Hosting environment: Development
2025-06-27 14:28:23.888 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-06-27 14:28:26.393 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-06-27 14:28:26.468 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:26.474 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 38.3788 ms
2025-06-27 14:28:26.492 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 108.3963ms
2025-06-27 14:28:26.501 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 95
2025-06-27 14:28:26.508 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:26.587 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:26.610 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:28:27.954 -04:00 [INF] Executed DbCommand (107ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:28:27.996 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:28:28.316 -04:00 [INF] Executed DbCommand (97ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-27 14:28:28.342 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:28:28.359 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 1741.8748ms
2025-06-27 14:28:28.361 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:28.363 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 1856.0566 ms
2025-06-27 14:28:28.372 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 1870.9129ms
2025-06-27 14:28:28.381 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 90
2025-06-27 14:28:28.386 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:28.388 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:28.390 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:28:28.426 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:28:28.454 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:28:29.079 -04:00 [INF] Executed DbCommand (508ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:28:29.095 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:28:29.102 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 710.1793ms
2025-06-27 14:28:29.105 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:29.134 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 747.6882 ms
2025-06-27 14:28:29.146 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 764.9859ms
2025-06-27 14:28:35.719 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-06-27 14:28:35.725 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:35.728 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 2.4574 ms
2025-06-27 14:28:35.733 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 13.8154ms
2025-06-27 14:28:35.740 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 95
2025-06-27 14:28:35.744 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:35.750 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:35.753 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:28:35.882 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:28:35.914 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:28:36.015 -04:00 [INF] Executed DbCommand (92ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-27 14:28:36.021 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:28:36.025 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 264.2826ms
2025-06-27 14:28:36.028 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:36.030 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 286.2529 ms
2025-06-27 14:28:36.034 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 293.6556ms
2025-06-27 14:28:36.045 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 90
2025-06-27 14:28:36.050 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:36.051 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:36.053 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:28:36.083 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:28:36.116 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:28:36.155 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:28:36.159 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:28:36.162 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 105.2724ms
2025-06-27 14:28:36.167 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:36.169 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 119.0949 ms
2025-06-27 14:28:36.172 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 127.8241ms
2025-06-27 14:28:38.060 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 148
2025-06-27 14:28:38.064 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - null null
2025-06-27 14:28:38.064 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationEmployeeList - null null
2025-06-27 14:28:38.076 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:38.069 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - null null
2025-06-27 14:28:38.072 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:38.081 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:38.081 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 18
2025-06-27 14:28:38.088 -04:00 [INF] HTTP OPTIONS /AssignableRoleList responded 204 in 11.6756 ms
2025-06-27 14:28:38.092 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:38.094 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:38.095 -04:00 [INF] HTTP OPTIONS /OrganizationEmployeeList responded 204 in 13.7950 ms
2025-06-27 14:28:38.102 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - 204 null null 38.0652ms
2025-06-27 14:28:38.103 -04:00 [INF] HTTP OPTIONS /RoleAssignmentList responded 204 in 10.7782 ms
2025-06-27 14:28:38.104 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:28:38.100 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:38.115 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationEmployeeList - 204 null null 51.113ms
2025-06-27 14:28:38.121 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AssignableRoleList - application/json 162
2025-06-27 14:28:38.123 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - 204 null null 56.0125ms
2025-06-27 14:28:38.127 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:38.132 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - application/json 158
2025-06-27 14:28:38.135 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:38.139 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - application/json 164
2025-06-27 14:28:38.141 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:28:38.143 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:38.144 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:38.148 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:28:38.151 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:38.154 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:38.160 -04:00 [INF] Route matched with {action = "QueryRoleAssignmentList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleAssignmentListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:28:38.160 -04:00 [INF] Route matched with {action = "GetOrganizationEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationEmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:28:38.161 -04:00 [INF] Route matched with {action = "QueryAssignableRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAssignableRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:28:38.163 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:28:38.221 -04:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:28:38.272 -04:00 [INF] Executed DbCommand (46ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:28:38.323 -04:00 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:28:38.373 -04:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:28:38.408 -04:00 [INF] Executed DbCommand (56ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:28:38.424 -04:00 [INF] Executed DbCommand (46ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:28:38.456 -04:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:28:38.479 -04:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-27 14:28:38.495 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:28:38.505 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 378.8185ms
2025-06-27 14:28:38.508 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:38.509 -04:00 [INF] Executed DbCommand (48ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:28:38.511 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 438.8458 ms
2025-06-27 14:28:38.519 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 458.2327ms
2025-06-27 14:28:38.536 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:28:38.540 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-27 14:28:38.585 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:28:38.594 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 443.27ms
2025-06-27 14:28:38.607 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:38.618 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 518.4873 ms
2025-06-27 14:28:38.639 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 557.9034ms
2025-06-27 14:28:38.715 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[@__organizationId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Id], [o].[OrganizationId], [o].[EmployeeId], [o].[TenantId], [e].[Name] AS [EmployeeName], [o].[CreateAt], [o].[CreateBy], [o].[UpdateAt], [o].[UpdateBy]
FROM [OrganizationEmployees] AS [o]
INNER JOIN [Employee] AS [e] ON [o].[EmployeeId] = [e].[Id]
WHERE [o].[OrganizationId] = @__organizationId_0
2025-06-27 14:28:38.720 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationEmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:28:38.727 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 560.735ms
2025-06-27 14:28:38.730 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:38.733 -04:00 [INF] HTTP POST /OrganizationEmployeeList responded 200 in 589.1914 ms
2025-06-27 14:28:38.736 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationEmployeeList - 200 null application/json; charset=utf-8 604.0124ms
2025-06-27 14:28:38.965 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[OrganizationId], [a].[RoleId], [a].[IsInheritable], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AssignableRoles] AS [a]
2025-06-27 14:28:38.965 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[AssignableRoleId], [r].[EmployeeId], [r].[IsInheritable], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [RoleAssignments] AS [r]
2025-06-27 14:28:38.975 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AssignableRoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:28:38.976 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleAssignmentListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:28:38.984 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api) in 821.4546ms
2025-06-27 14:28:38.985 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 816.7074ms
2025-06-27 14:28:38.988 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:38.991 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:28:38.992 -04:00 [INF] HTTP POST /RoleAssignmentList responded 200 in 844.2105 ms
2025-06-27 14:28:38.994 -04:00 [INF] HTTP POST /AssignableRoleList responded 200 in 858.9276 ms
2025-06-27 14:28:39.135 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - 200 null application/json; charset=utf-8 996.0658ms
2025-06-27 14:28:39.135 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AssignableRoleList - 200 null application/json; charset=utf-8 1014.9043ms
2025-06-27 14:29:20.231 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-06-27 14:29:20.242 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:29:20.244 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 2.2671 ms
2025-06-27 14:29:20.248 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 16.5496ms
2025-06-27 14:29:20.260 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 51
2025-06-27 14:29:20.267 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:29:20.269 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:29:20.270 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:29:20.326 -04:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:29:20.357 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:29:20.392 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:29:20.401 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:29:20.404 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 129.8488ms
2025-06-27 14:29:20.406 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:29:20.408 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 141.2953 ms
2025-06-27 14:29:20.412 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 152.0575ms
2025-06-27 14:29:20.422 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/RoleList - null null
2025-06-27 14:29:20.436 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:29:20.438 -04:00 [INF] HTTP OPTIONS /RoleList responded 204 in 2.0708 ms
2025-06-27 14:29:20.443 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/RoleList - 204 null null 21.046ms
2025-06-27 14:29:20.452 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleList - application/json 129
2025-06-27 14:29:20.456 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:29:20.457 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:29:20.468 -04:00 [INF] Route matched with {action = "QueryRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:29:20.516 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:29:20.542 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:29:20.721 -04:00 [INF] Executed DbCommand (41ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Name], [r].[Description], [r].[IsActive], [r].[Usage], [r].[Code], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [Role] AS [r]
WHERE [r].[Id] IN (
    SELECT [r0].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r0]
)
2025-06-27 14:29:20.730 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:29:20.742 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 265.8426ms
2025-06-27 14:29:20.745 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:29:20.749 -04:00 [INF] HTTP POST /RoleList responded 200 in 293.2901 ms
2025-06-27 14:29:20.852 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleList - 200 null application/json; charset=utf-8 399.9306ms
2025-06-27 14:50:44.099 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - null null
2025-06-27 14:50:44.106 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:50:44.107 -04:00 [INF] HTTP OPTIONS /RoleAssignmentList responded 204 in 1.6754 ms
2025-06-27 14:50:44.110 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - 204 null null 13.3419ms
2025-06-27 14:50:44.119 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - application/json 107
2025-06-27 14:50:44.124 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:50:44.126 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:50:44.129 -04:00 [INF] Route matched with {action = "QueryRoleAssignmentList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleAssignmentListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:50:44.448 -04:00 [INF] Executed DbCommand (101ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:50:44.494 -04:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:50:44.715 -04:00 [INF] Executed DbCommand (79ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[AssignableRoleId], [r].[EmployeeId], [r].[IsInheritable], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [RoleAssignments] AS [r]
2025-06-27 14:50:44.724 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleAssignmentListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:50:44.728 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api) in 596.0378ms
2025-06-27 14:50:44.730 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:50:44.732 -04:00 [INF] HTTP POST /RoleAssignmentList responded 200 in 607.4717 ms
2025-06-27 14:50:44.776 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - 200 null application/json; charset=utf-8 657.5939ms
2025-06-27 14:52:42.842 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-27 14:52:42.845 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:42.846 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 1.0263 ms
2025-06-27 14:52:42.848 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 6.4805ms
2025-06-27 14:52:42.858 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 99
2025-06-27 14:52:42.865 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:42.867 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:42.883 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:42.919 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:42.950 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:43.027 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-27 14:52:43.035 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:43.046 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 150.5325ms
2025-06-27 14:52:43.050 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:43.052 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 186.9687 ms
2025-06-27 14:52:43.056 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 197.8ms
2025-06-27 14:52:43.067 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - null null
2025-06-27 14:52:43.072 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:43.075 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 2.4597 ms
2025-06-27 14:52:43.077 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 204 null null 9.9018ms
2025-06-27 14:52:43.082 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - application/json null
2025-06-27 14:52:43.090 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:43.093 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:43.101 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:43.128 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:43.155 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:43.275 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 14:52:43.331 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:43.342 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 236.476ms
2025-06-27 14:52:43.346 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:43.347 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 257.0480 ms
2025-06-27 14:52:43.350 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 200 null application/json; charset=utf-8 267.7836ms
2025-06-27 14:52:43.358 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-27 14:52:43.366 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:43.367 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.3001 ms
2025-06-27 14:52:43.370 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 11.7898ms
2025-06-27 14:52:43.377 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 97
2025-06-27 14:52:43.381 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:43.382 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:43.393 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:43.420 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:43.448 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:43.515 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:52:43.531 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:43.538 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 141.1287ms
2025-06-27 14:52:43.543 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:43.545 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 164.6140 ms
2025-06-27 14:52:43.549 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 171.3966ms
2025-06-27 14:52:45.656 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 149
2025-06-27 14:52:45.663 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:45.665 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:45.667 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - application/json null
2025-06-27 14:52:45.671 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:45.666 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-27 14:52:45.677 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:45.681 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:45.683 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:45.684 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:45.685 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:45.687 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:45.710 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:45.747 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:45.787 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:45.825 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:45.852 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:52:45.860 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:45.863 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 184.8538ms
2025-06-27 14:52:45.867 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:45.868 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 205.6794 ms
2025-06-27 14:52:45.871 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:45.873 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 217.7864ms
2025-06-27 14:52:45.883 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-27 14:52:45.888 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:45.889 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:45.892 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:45.911 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:45.938 -04:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:45.958 -04:00 [INF] Executed DbCommand (38ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 14:52:45.965 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:45.970 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 279.527ms
2025-06-27 14:52:45.979 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:45.983 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 306.9810 ms
2025-06-27 14:52:45.988 -04:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:45.995 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 200 null application/json; charset=utf-8 328.923ms
2025-06-27 14:52:45.998 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-27 14:52:46.024 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-27 14:52:46.029 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:46.032 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:46.034 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 341.2937ms
2025-06-27 14:52:46.035 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:46.037 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:46.039 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:46.041 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 359.3329 ms
2025-06-27 14:52:46.047 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:52:46.047 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 381.2158ms
2025-06-27 14:52:46.051 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:46.058 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 163.6584ms
2025-06-27 14:52:46.061 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:46.063 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 175.2346 ms
2025-06-27 14:52:46.066 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 182.8117ms
2025-06-27 14:52:46.117 -04:00 [INF] Executed DbCommand (72ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:46.145 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:46.176 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:52:46.191 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:46.196 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 151.6446ms
2025-06-27 14:52:46.199 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:46.203 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 170.4161 ms
2025-06-27 14:52:46.207 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 182.9099ms
2025-06-27 14:52:50.497 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-27 14:52:50.503 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:50.504 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 1.6992 ms
2025-06-27 14:52:50.509 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 11.2386ms
2025-06-27 14:52:50.517 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 99
2025-06-27 14:52:50.524 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:50.528 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:50.532 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:50.581 -04:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:50.610 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:50.644 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-27 14:52:50.648 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:50.651 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 114.4101ms
2025-06-27 14:52:50.655 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:50.658 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 134.7409 ms
2025-06-27 14:52:50.662 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 144.6122ms
2025-06-27 14:52:50.670 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - null null
2025-06-27 14:52:50.679 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:50.681 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 2.3359 ms
2025-06-27 14:52:50.684 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 204 null null 13.7509ms
2025-06-27 14:52:50.688 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - application/json null
2025-06-27 14:52:50.692 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:50.693 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:50.695 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:50.732 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:50.762 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:50.795 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 14:52:50.803 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:50.806 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 107.2586ms
2025-06-27 14:52:50.811 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:50.813 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 121.5070 ms
2025-06-27 14:52:50.817 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 200 null application/json; charset=utf-8 128.9055ms
2025-06-27 14:52:50.823 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-27 14:52:50.830 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:50.831 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.2307 ms
2025-06-27 14:52:50.834 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 10.7161ms
2025-06-27 14:52:50.844 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 97
2025-06-27 14:52:50.852 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:50.854 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:50.857 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:50.889 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:50.925 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:50.961 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:52:50.977 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:50.982 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 118.7787ms
2025-06-27 14:52:50.984 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:50.986 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 133.9784 ms
2025-06-27 14:52:50.990 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 145.6683ms
2025-06-27 14:52:53.194 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 149
2025-06-27 14:52:53.200 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - application/json null
2025-06-27 14:52:53.201 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-27 14:52:53.201 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:53.210 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:53.216 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:53.217 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:53.218 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:53.219 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:53.221 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:53.223 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:53.224 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:53.247 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:53.268 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:53.268 -04:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:53.295 -04:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:53.321 -04:00 [INF] Executed DbCommand (49ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:53.321 -04:00 [INF] Executed DbCommand (46ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:53.338 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:52:53.349 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:53.351 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 124.6833ms
2025-06-27 14:52:53.353 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:53.354 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-27 14:52:53.355 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 153.7280 ms
2025-06-27 14:52:53.359 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:53.363 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 168.7314ms
2025-06-27 14:52:53.365 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 134.165ms
2025-06-27 14:52:53.368 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 14:52:53.370 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-27 14:52:53.371 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:53.379 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 163.6886 ms
2025-06-27 14:52:53.377 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:53.374 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:53.383 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 182.0381ms
2025-06-27 14:52:53.384 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:53.386 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 157.7465ms
2025-06-27 14:52:53.392 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:53.394 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:53.398 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 188.6741 ms
2025-06-27 14:52:53.401 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 200 null application/json; charset=utf-8 200.5183ms
2025-06-27 14:52:53.407 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-27 14:52:53.412 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:53.413 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:53.415 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:53.439 -04:00 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:53.462 -04:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:53.484 -04:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:53.511 -04:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:53.537 -04:00 [INF] Executed DbCommand (44ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:52:53.547 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:53.547 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:52:53.550 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 153.5317ms
2025-06-27 14:52:53.557 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:53.558 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:53.563 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 143.5537ms
2025-06-27 14:52:53.565 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 187.4393 ms
2025-06-27 14:52:53.566 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:53.569 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 199.4682ms
2025-06-27 14:52:53.570 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 158.6743 ms
2025-06-27 14:52:53.579 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 171.4046ms
2025-06-27 14:52:55.308 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-27 14:52:55.308 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-27 14:52:55.312 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 76
2025-06-27 14:52:55.315 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:55.319 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:55.324 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:52:55.325 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:55.326 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:55.327 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:55.329 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:55.330 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:55.331 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:52:55.362 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:55.397 -04:00 [INF] Executed DbCommand (61ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:55.397 -04:00 [INF] Executed DbCommand (56ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:52:55.422 -04:00 [INF] Executed DbCommand (53ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:55.455 -04:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:55.455 -04:00 [INF] Executed DbCommand (54ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:52:55.471 -04:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-27 14:52:55.490 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:52:55.490 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:55.500 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:55.501 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 163.9287ms
2025-06-27 14:52:55.505 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:52:55.506 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 166.1136ms
2025-06-27 14:52:55.507 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:55.512 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:52:55.513 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:55.514 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 195.5321 ms
2025-06-27 14:52:55.516 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 181.4419ms
2025-06-27 14:52:55.517 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 193.0115 ms
2025-06-27 14:52:55.520 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 211.6699ms
2025-06-27 14:52:55.522 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:52:55.525 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 212.5072ms
2025-06-27 14:52:55.531 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 216.3340 ms
2025-06-27 14:52:55.540 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 231.1589ms
2025-06-27 14:53:54.593 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-27 14:53:54.599 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-27 14:53:54.608 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:53:54.608 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-27 14:53:54.617 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:53:54.618 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 10.1129 ms
2025-06-27 14:53:54.622 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:53:54.627 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 35.1985ms
2025-06-27 14:53:54.625 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 8.0450 ms
2025-06-27 14:53:54.629 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 6.9045 ms
2025-06-27 14:53:54.649 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-27 14:53:54.653 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 54.2962ms
2025-06-27 14:53:54.655 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 46.3653ms
2025-06-27 14:53:54.659 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:53:54.664 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-27 14:53:54.669 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 76
2025-06-27 14:53:54.670 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:53:54.675 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:53:54.678 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:53:54.679 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:53:54.681 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:53:54.682 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:53:54.685 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:53:54.687 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:53:54.738 -04:00 [INF] Executed DbCommand (53ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:53:54.762 -04:00 [INF] Executed DbCommand (67ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:53:54.762 -04:00 [INF] Executed DbCommand (72ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:53:54.786 -04:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:53:54.814 -04:00 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:53:54.814 -04:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:53:54.839 -04:00 [INF] Executed DbCommand (43ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:53:54.846 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:53:54.849 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 165.0907ms
2025-06-27 14:53:54.850 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:53:54.854 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:53:54.859 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:53:54.859 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 200.1012 ms
2025-06-27 14:53:54.863 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 169.2214ms
2025-06-27 14:53:54.866 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-27 14:53:54.867 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 217.8851ms
2025-06-27 14:53:54.871 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:53:54.876 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:53:54.884 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 205.5699 ms
2025-06-27 14:53:54.886 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 196.7464ms
2025-06-27 14:53:54.889 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 220.0814ms
2025-06-27 14:53:54.892 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:53:54.898 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 223.6841 ms
2025-06-27 14:53:54.901 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 237.365ms
2025-06-27 14:54:02.846 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-27 14:54:02.846 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-27 14:54:02.853 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-27 14:54:02.853 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:54:02.859 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:54:02.865 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:54:02.865 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 12.1734 ms
2025-06-27 14:54:02.867 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 7.2839 ms
2025-06-27 14:54:02.872 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 6.9602 ms
2025-06-27 14:54:02.875 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 28.847ms
2025-06-27 14:54:02.885 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 38.774ms
2025-06-27 14:54:02.889 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 36.4026ms
2025-06-27 14:54:02.895 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-27 14:54:02.911 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-27 14:54:02.916 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 76
2025-06-27 14:54:02.920 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:54:02.932 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:54:02.938 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:54:02.940 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:54:02.943 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:54:02.944 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:54:02.946 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:54:02.948 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:54:02.950 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:54:03.006 -04:00 [INF] Executed DbCommand (49ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:54:03.041 -04:00 [INF] Executed DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:54:03.041 -04:00 [INF] Executed DbCommand (72ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:54:03.074 -04:00 [INF] Executed DbCommand (64ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:54:03.101 -04:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:54:03.101 -04:00 [INF] Executed DbCommand (53ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:54:03.165 -04:00 [INF] Executed DbCommand (81ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:54:03.172 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:54:03.174 -04:00 [INF] Executed DbCommand (64ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:54:03.174 -04:00 [INF] Executed DbCommand (60ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-27 14:54:03.177 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 221.0755ms
2025-06-27 14:54:03.185 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:54:03.188 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:54:03.189 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:54:03.191 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 222.9855ms
2025-06-27 14:54:03.193 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 234.142ms
2025-06-27 14:54:03.195 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 274.9762 ms
2025-06-27 14:54:03.198 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:54:03.202 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:54:03.206 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 310.5112ms
2025-06-27 14:54:03.208 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 269.4110 ms
2025-06-27 14:54:03.210 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 278.1710 ms
2025-06-27 14:54:03.217 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 301.2282ms
2025-06-27 14:54:03.221 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 309.7037ms
2025-06-27 14:54:58.780 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-27 14:54:58.781 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-27 14:54:58.795 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:54:58.790 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-27 14:54:58.802 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:54:58.803 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 7.9749 ms
2025-06-27 14:54:58.809 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:54:58.811 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 9.5057 ms
2025-06-27 14:54:58.815 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 35.0738ms
2025-06-27 14:54:58.829 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 48.9209ms
2025-06-27 14:54:58.818 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 9.3629 ms
2025-06-27 14:54:58.845 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-27 14:54:58.853 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-27 14:54:58.857 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 67.463ms
2025-06-27 14:54:58.862 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:54:58.865 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:54:58.872 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 76
2025-06-27 14:54:58.874 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:54:58.876 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:54:58.881 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:54:58.882 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:54:58.884 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:54:58.885 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:54:58.894 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:54:58.998 -04:00 [INF] Executed DbCommand (106ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:54:59.011 -04:00 [INF] Executed DbCommand (113ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:54:59.011 -04:00 [INF] Executed DbCommand (122ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:54:59.034 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:54:59.041 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:54:59.053 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:54:59.068 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-27 14:54:59.073 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:54:59.076 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 184.989ms
2025-06-27 14:54:59.080 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:54:59.081 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:54:59.086 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:54:59.086 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 220.4378 ms
2025-06-27 14:54:59.088 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 191.0862ms
2025-06-27 14:54:59.091 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 237.9262ms
2025-06-27 14:54:59.098 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:54:59.104 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:54:59.114 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:54:59.115 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 234.1576 ms
2025-06-27 14:54:59.117 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 229.2721ms
2025-06-27 14:54:59.119 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 247.4588ms
2025-06-27 14:54:59.121 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:54:59.128 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 266.0524 ms
2025-06-27 14:54:59.130 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 285.4988ms
2025-06-27 14:55:06.146 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-27 14:55:06.151 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - null null
2025-06-27 14:55:06.151 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-27 14:55:06.152 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:06.155 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:06.160 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:06.160 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 7.9457 ms
2025-06-27 14:55:06.161 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 5.8484 ms
2025-06-27 14:55:06.162 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 2.8543 ms
2025-06-27 14:55:06.165 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 19.0054ms
2025-06-27 14:55:06.171 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 204 null null 19.5784ms
2025-06-27 14:55:06.174 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 23.052ms
2025-06-27 14:55:06.179 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 149
2025-06-27 14:55:06.183 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - application/json null
2025-06-27 14:55:06.187 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-27 14:55:06.191 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:06.194 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:06.198 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:06.199 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:06.200 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:06.201 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:06.202 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:55:06.203 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:55:06.205 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:55:06.268 -04:00 [INF] Executed DbCommand (60ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:55:06.299 -04:00 [INF] Executed DbCommand (89ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:55:06.299 -04:00 [INF] Executed DbCommand (87ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:55:06.325 -04:00 [INF] Executed DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:55:06.351 -04:00 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:55:06.351 -04:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:55:06.364 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:55:06.380 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 14:55:06.382 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:55:06.384 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:55:06.386 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 178.5672ms
2025-06-27 14:55:06.388 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 178.4831ms
2025-06-27 14:55:06.392 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:06.396 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:06.398 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 207.3916 ms
2025-06-27 14:55:06.400 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 206.0977 ms
2025-06-27 14:55:06.400 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-27 14:55:06.403 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 223.5785ms
2025-06-27 14:55:06.406 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ConfigType - 200 null application/json; charset=utf-8 222.5159ms
2025-06-27 14:55:06.410 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:55:06.416 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-27 14:55:06.418 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 205.842ms
2025-06-27 14:55:06.422 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:06.424 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:06.426 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-27 14:55:06.426 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:06.427 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 229.3385 ms
2025-06-27 14:55:06.431 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:06.432 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:55:06.435 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 247.6175ms
2025-06-27 14:55:06.436 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:06.444 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:55:06.464 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:55:06.489 -04:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:55:06.526 -04:00 [INF] Executed DbCommand (59ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:55:06.551 -04:00 [INF] Executed DbCommand (57ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:55:06.573 -04:00 [INF] Executed DbCommand (41ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:55:06.581 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:55:06.582 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:55:06.585 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 145.9231ms
2025-06-27 14:55:06.590 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:55:06.592 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:06.595 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 148.3499ms
2025-06-27 14:55:06.596 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 173.7641 ms
2025-06-27 14:55:06.598 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:06.601 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 184.4991ms
2025-06-27 14:55:06.602 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 171.4974 ms
2025-06-27 14:55:06.612 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 186.5145ms
2025-06-27 14:55:08.120 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-27 14:55:08.120 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-27 14:55:08.126 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 76
2025-06-27 14:55:08.126 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:08.130 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:08.135 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:08.136 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:08.138 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:08.139 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:08.141 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:55:08.142 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:55:08.143 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:55:08.167 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:55:08.183 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:55:08.183 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:55:08.204 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:55:08.208 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:55:08.228 -04:00 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:55:08.245 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:55:08.249 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:55:08.249 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-27 14:55:08.251 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 105.0819ms
2025-06-27 14:55:08.254 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:55:08.255 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:55:08.256 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:08.258 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 109.331ms
2025-06-27 14:55:08.262 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:55:08.262 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 136.1924 ms
2025-06-27 14:55:08.264 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:08.267 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 115.9201ms
2025-06-27 14:55:08.270 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 149.1636ms
2025-06-27 14:55:08.271 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 141.1404 ms
2025-06-27 14:55:08.274 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:08.281 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 160.9829ms
2025-06-27 14:55:08.283 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 148.5357 ms
2025-06-27 14:55:08.292 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 166.0821ms
2025-06-27 14:55:17.961 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-27 14:55:17.961 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - null null
2025-06-27 14:55:17.962 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-27 14:55:17.966 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:17.970 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:17.977 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:17.978 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 11.8255 ms
2025-06-27 14:55:17.979 -04:00 [INF] HTTP OPTIONS /CodeTypeList responded 204 in 9.1303 ms
2025-06-27 14:55:17.981 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 4.3568 ms
2025-06-27 14:55:17.983 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 22.0782ms
2025-06-27 14:55:17.988 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeList - 204 null null 26.5347ms
2025-06-27 14:55:17.997 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 34.8755ms
2025-06-27 14:55:18.003 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 18
2025-06-27 14:55:18.008 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeTypeList - application/json 51
2025-06-27 14:55:18.012 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 76
2025-06-27 14:55:18.016 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:18.020 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:18.023 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:55:18.025 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:18.026 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:18.027 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:18.029 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:55:18.030 -04:00 [INF] Route matched with {action = "GetCodeTypeList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:55:18.032 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:55:18.148 -04:00 [INF] Executed DbCommand (112ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:55:18.212 -04:00 [INF] Executed DbCommand (173ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:55:18.212 -04:00 [INF] Executed DbCommand (170ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:55:18.349 -04:00 [INF] Executed DbCommand (198ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:55:18.382 -04:00 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:55:18.382 -04:00 [INF] Executed DbCommand (166ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:55:18.401 -04:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
2025-06-27 14:55:18.408 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:55:18.411 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 376.4299ms
2025-06-27 14:55:18.413 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:18.416 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 400.6346 ms
2025-06-27 14:55:18.421 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 418.3283ms
2025-06-27 14:55:18.424 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:55:18.555 -04:00 [INF] Executed DbCommand (156ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[TypeCode], [c].[Name], [c].[Description], [c].[MainType], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeType] AS [c]
WHERE [c].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:55:18.555 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:55:18.561 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeTypeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:55:18.564 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 522.686ms
2025-06-27 14:55:18.565 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api) in 527.3082ms
2025-06-27 14:55:18.566 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:18.568 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:55:18.570 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 546.7393 ms
2025-06-27 14:55:18.571 -04:00 [INF] HTTP POST /CodeTypeList responded 200 in 551.8419 ms
2025-06-27 14:55:18.575 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 563.4048ms
2025-06-27 14:55:18.580 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeTypeList - 200 null application/json; charset=utf-8 571.6757ms
2025-06-27 14:56:02.572 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/GlobalAdminList - null null
2025-06-27 14:56:02.613 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:02.623 -04:00 [INF] HTTP OPTIONS /GlobalAdminList responded 204 in 9.7226 ms
2025-06-27 14:56:02.636 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/GlobalAdminList - 204 null null 63.7312ms
2025-06-27 14:56:02.695 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/GlobalAdminList - application/json 100
2025-06-27 14:56:02.715 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:02.719 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetGlobalAdminList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:02.737 -04:00 [INF] Route matched with {action = "GetGlobalAdminList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetGlobalAdminList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.GlobalAdminDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:02.842 -04:00 [INF] Executed DbCommand (81ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:02.952 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:03.077 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [g].[LoginName], [g].[Name], [g].[Password], [g].[Description], [g].[Email], [g].[Mobile], [g].[MFA], [g].[IsActive], [g].[ImageId], [g].[Id], [g].[CreateBy], [g].[CreateAt], [g].[UpdateBy], [g].[UpdateAt]
FROM [GlobalAdmin] AS [g]
2025-06-27 14:56:03.084 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.GlobalAdminListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:03.092 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetGlobalAdminList (Visfuture.OneTeam.BaseBiz.Api) in 331.2209ms
2025-06-27 14:56:03.094 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetGlobalAdminList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:03.095 -04:00 [INF] HTTP POST /GlobalAdminList responded 200 in 380.3096 ms
2025-06-27 14:56:03.098 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/GlobalAdminList - 200 null application/json; charset=utf-8 403.1085ms
2025-06-27 14:56:04.330 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/UserAccountList - null null
2025-06-27 14:56:04.333 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:04.334 -04:00 [INF] HTTP OPTIONS /UserAccountList responded 204 in 1.2501 ms
2025-06-27 14:56:04.338 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/UserAccountList - 204 null null 7.9642ms
2025-06-27 14:56:04.347 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/UserAccountList - application/json 95
2025-06-27 14:56:04.353 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:04.354 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetUserAccountList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:04.363 -04:00 [INF] Route matched with {action = "GetUserAccountList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserAccountList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.UserAccountDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:04.395 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:04.426 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:04.486 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Name], [u].[PasswordHash], [u].[Email], [u].[Mobile], [u].[MFA], [u].[IsActive], [u].[Description], [u].[ImageId], [u].[Language], [u].[Id], [u].[CreateBy], [u].[CreateAt], [u].[UpdateBy], [u].[UpdateAt]
FROM [UserAccounts] AS [u]
2025-06-27 14:56:04.496 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.UserAccountListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:04.507 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetUserAccountList (Visfuture.OneTeam.BaseBiz.Api) in 138.3342ms
2025-06-27 14:56:04.512 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetUserAccountList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:04.516 -04:00 [INF] HTTP POST /UserAccountList responded 200 in 162.5898 ms
2025-06-27 14:56:04.518 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/UserAccountList - 200 null application/json; charset=utf-8 171.775ms
2025-06-27 14:56:05.193 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/AccessResourceList - null null
2025-06-27 14:56:05.197 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:05.198 -04:00 [INF] HTTP OPTIONS /AccessResourceList responded 204 in 1.2438 ms
2025-06-27 14:56:05.200 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/AccessResourceList - 204 null null 7.1744ms
2025-06-27 14:56:05.204 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AccessResourceList - application/json 103
2025-06-27 14:56:05.208 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:05.209 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAccessResourceListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:05.215 -04:00 [INF] Route matched with {action = "QueryAccessResourceList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAccessResourceListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AccessResourceDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:05.239 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:05.271 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:05.476 -04:00 [INF] Executed DbCommand (67ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Name], [a].[ResourceCode], [a].[Description], [a].[IsActive], [a].[IsPublic], [a].[SubType], [a].[SuperiorId], [a].[SystemId], [a].[ModuleId], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AccessResource] AS [a]
2025-06-27 14:56:05.483 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AccessResourceListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:05.493 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAccessResourceListAsync (Visfuture.OneTeam.BaseBiz.Api) in 274.8306ms
2025-06-27 14:56:05.498 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAccessResourceListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:05.500 -04:00 [INF] HTTP POST /AccessResourceList responded 200 in 292.7544 ms
2025-06-27 14:56:05.609 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AccessResourceList - 200 null application/json; charset=utf-8 404.8505ms
2025-06-27 14:56:05.617 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=System - null null
2025-06-27 14:56:05.627 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:05.630 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 2.7658 ms
2025-06-27 14:56:05.633 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=System - 204 null null 15.8694ms
2025-06-27 14:56:05.639 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=System - application/json null
2025-06-27 14:56:05.644 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:05.645 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:05.647 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:05.669 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:05.697 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:05.730 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 14:56:05.736 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:05.739 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 90.2013ms
2025-06-27 14:56:05.744 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:05.747 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 102.2836 ms
2025-06-27 14:56:05.749 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=System - 200 null application/json; charset=utf-8 110.0721ms
2025-06-27 14:56:05.756 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-27 14:56:05.762 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:05.763 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.1830 ms
2025-06-27 14:56:05.766 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 9.5685ms
2025-06-27 14:56:05.770 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 91
2025-06-27 14:56:05.777 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:05.778 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:05.780 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:05.820 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:05.850 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:05.901 -04:00 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:56:05.919 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:05.924 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 141.2034ms
2025-06-27 14:56:05.928 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:05.930 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 153.5411 ms
2025-06-27 14:56:05.933 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 163.1293ms
2025-06-27 14:56:05.937 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=AccessResourceType - null null
2025-06-27 14:56:05.944 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:05.946 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.8973 ms
2025-06-27 14:56:05.951 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=AccessResourceType - 204 null null 13.6161ms
2025-06-27 14:56:05.958 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=AccessResourceType - application/json null
2025-06-27 14:56:05.963 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:05.964 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:05.966 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:06.040 -04:00 [INF] Executed DbCommand (70ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:06.071 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:06.106 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 14:56:06.117 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:06.121 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 151.3462ms
2025-06-27 14:56:06.124 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:06.127 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 164.4695 ms
2025-06-27 14:56:06.132 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=AccessResourceType - 200 null application/json; charset=utf-8 173.3321ms
2025-06-27 14:56:06.139 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 97
2025-06-27 14:56:06.145 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:06.146 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:06.149 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:06.174 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:06.204 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:06.242 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:56:06.259 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:06.263 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 111.0388ms
2025-06-27 14:56:06.267 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:06.269 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 124.4451 ms
2025-06-27 14:56:06.273 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 134.1397ms
2025-06-27 14:56:06.288 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/TenantList - null null
2025-06-27 14:56:06.291 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AccessResourceList - application/json 90
2025-06-27 14:56:06.292 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:06.296 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:06.296 -04:00 [INF] HTTP OPTIONS /TenantList responded 204 in 4.2352 ms
2025-06-27 14:56:06.298 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAccessResourceListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:06.300 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/TenantList - 204 null null 12.1386ms
2025-06-27 14:56:06.303 -04:00 [INF] Route matched with {action = "QueryAccessResourceList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAccessResourceListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AccessResourceDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:06.307 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/TenantList - application/json 95
2025-06-27 14:56:06.313 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:06.314 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetTenantList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:06.323 -04:00 [INF] Route matched with {action = "GetTenantList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTenantList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.TenantDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:06.346 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:06.369 -04:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:06.398 -04:00 [INF] Executed DbCommand (48ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:06.421 -04:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:06.478 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Name], [t].[Description], [t].[Address1], [t].[Address2], [t].[City], [t].[Province], [t].[PostalCode], [t].[EffectiveDate], [t].[ExpireDate], [t].[IsActive], [t].[Domain], [t].[Language], [t].[TimeZone], [t].[ContactName], [t].[ContactPhone], [t].[ContactFax], [t].[ContactEmail], [t].[Id], [t].[CreateBy], [t].[CreateAt], [t].[UpdateBy], [t].[UpdateAt]
FROM [Tenant] AS [t]
2025-06-27 14:56:06.495 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.TenantListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:06.507 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetTenantList (Visfuture.OneTeam.BaseBiz.Api) in 179.4034ms
2025-06-27 14:56:06.510 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetTenantList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:06.511 -04:00 [INF] HTTP POST /TenantList responded 200 in 198.8621 ms
2025-06-27 14:56:06.514 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/TenantList - 200 null application/json; charset=utf-8 207.1735ms
2025-06-27 14:56:06.596 -04:00 [INF] Executed DbCommand (53ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Name], [a].[ResourceCode], [a].[Description], [a].[IsActive], [a].[IsPublic], [a].[SubType], [a].[SuperiorId], [a].[SystemId], [a].[ModuleId], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AccessResource] AS [a]
WHERE [a].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:56:06.602 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AccessResourceListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:06.605 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAccessResourceListAsync (Visfuture.OneTeam.BaseBiz.Api) in 295.9773ms
2025-06-27 14:56:06.609 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAccessResourceListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:06.611 -04:00 [INF] HTTP POST /AccessResourceList responded 200 in 315.8137 ms
2025-06-27 14:56:06.657 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AccessResourceList - 200 null application/json; charset=utf-8 366.2228ms
2025-06-27 14:56:14.545 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-27 14:56:14.549 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:14.550 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.3232 ms
2025-06-27 14:56:14.553 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 8.1424ms
2025-06-27 14:56:14.562 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 95
2025-06-27 14:56:14.568 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:14.570 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:14.580 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:14.608 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:14.637 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:14.703 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-06-27 14:56:14.716 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:14.729 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 142.8996ms
2025-06-27 14:56:14.734 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:14.735 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 167.5641 ms
2025-06-27 14:56:14.738 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 176.5359ms
2025-06-27 14:56:14.752 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=EmployeeMainType - null null
2025-06-27 14:56:14.758 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:14.760 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.9822 ms
2025-06-27 14:56:14.763 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=EmployeeMainType - 204 null null 10.9667ms
2025-06-27 14:56:14.769 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=EmployeeMainType - application/json null
2025-06-27 14:56:14.773 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:14.774 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:14.775 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:14.802 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:14.830 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:14.859 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 14:56:14.868 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:14.874 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 95.5254ms
2025-06-27 14:56:14.877 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:14.879 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 106.1069 ms
2025-06-27 14:56:14.882 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=EmployeeMainType - 200 null application/json; charset=utf-8 112.6629ms
2025-06-27 14:56:14.886 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-27 14:56:14.890 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:14.891 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.7967 ms
2025-06-27 14:56:14.895 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 8.5504ms
2025-06-27 14:56:14.900 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 101
2025-06-27 14:56:14.905 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:14.907 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:14.910 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:14.937 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:14.968 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:15.012 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:56:15.030 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:15.036 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 123.6158ms
2025-06-27 14:56:15.038 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:15.041 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 136.5812 ms
2025-06-27 14:56:15.046 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 146.6215ms
2025-06-27 14:56:15.056 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=JobPosition - null null
2025-06-27 14:56:15.061 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:15.063 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.8851 ms
2025-06-27 14:56:15.068 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=JobPosition - 204 null null 12.0006ms
2025-06-27 14:56:15.079 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=JobPosition - application/json null
2025-06-27 14:56:15.083 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:15.085 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:15.092 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:15.116 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:15.142 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:15.170 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 14:56:15.179 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:15.182 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 87.6252ms
2025-06-27 14:56:15.185 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:15.187 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 103.9070 ms
2025-06-27 14:56:15.191 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=JobPosition - 200 null application/json; charset=utf-8 112.5709ms
2025-06-27 14:56:15.198 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 103
2025-06-27 14:56:15.202 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:15.203 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:15.205 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:15.237 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:15.277 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:15.321 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:56:15.336 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:15.342 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 133.4335ms
2025-06-27 14:56:15.346 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:15.349 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 147.0062 ms
2025-06-27 14:56:15.352 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 153.5035ms
2025-06-27 14:56:15.357 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=EmployeeStatus - null null
2025-06-27 14:56:15.361 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:15.362 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.1332 ms
2025-06-27 14:56:15.366 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=EmployeeStatus - 204 null null 8.96ms
2025-06-27 14:56:15.371 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=EmployeeStatus - application/json null
2025-06-27 14:56:15.375 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:15.377 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:15.379 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:15.457 -04:00 [INF] Executed DbCommand (74ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:15.483 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:15.512 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 14:56:15.517 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:15.520 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 138.5515ms
2025-06-27 14:56:15.524 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:15.526 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 151.0125 ms
2025-06-27 14:56:15.530 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=EmployeeStatus - 200 null application/json; charset=utf-8 158.7812ms
2025-06-27 14:56:15.535 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 96
2025-06-27 14:56:15.539 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:15.542 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:15.544 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:15.568 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:15.599 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:15.631 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:56:15.648 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:15.652 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 104.8057ms
2025-06-27 14:56:15.655 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:15.659 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 120.1288 ms
2025-06-27 14:56:15.665 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 129.9154ms
2025-06-27 14:56:18.078 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=EmployeeStatus - application/json null
2025-06-27 14:56:18.078 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=EmployeeMainType - application/json null
2025-06-27 14:56:18.080 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=JobPosition - application/json null
2025-06-27 14:56:18.083 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:18.090 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:18.086 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:18.090 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/UserAccountList - null null
2025-06-27 14:56:18.091 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.092 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.093 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.096 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:18.098 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:18.099 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:18.101 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:18.102 -04:00 [INF] HTTP OPTIONS /UserAccountList responded 204 in 5.2922 ms
2025-06-27 14:56:18.123 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/UserAccountList - 204 null null 32.3124ms
2025-06-27 14:56:18.129 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:18.129 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/UserAccountList - application/json 18
2025-06-27 14:56:18.136 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:18.138 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetUserAccountList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.139 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:18.140 -04:00 [INF] Route matched with {action = "GetUserAccountList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserAccountList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.UserAccountDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:18.143 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:18.159 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:18.183 -04:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:18.200 -04:00 [INF] Executed DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:18.201 -04:00 [INF] Executed DbCommand (38ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 14:56:18.217 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:18.219 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 111.0938ms
2025-06-27 14:56:18.221 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.222 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 14:56:18.223 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 140.4715 ms
2025-06-27 14:56:18.228 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:18.231 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=EmployeeStatus - 200 null application/json; charset=utf-8 152.8411ms
2025-06-27 14:56:18.232 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 120.8655ms
2025-06-27 14:56:18.239 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-27 14:56:18.240 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 14:56:18.242 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.246 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:18.253 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:18.255 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 165.6316 ms
2025-06-27 14:56:18.257 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.260 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 139.5526ms
2025-06-27 14:56:18.263 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=JobPosition - 200 null application/json; charset=utf-8 183.1756ms
2025-06-27 14:56:18.266 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:18.269 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.278 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-27 14:56:18.283 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 197.5870 ms
2025-06-27 14:56:18.288 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:18.292 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=EmployeeMainType - 200 null application/json; charset=utf-8 214.5627ms
2025-06-27 14:56:18.294 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.299 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-27 14:56:18.300 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:18.304 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:18.310 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:18.311 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.315 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:18.330 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:18.333 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:18.337 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:18.349 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:18.359 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:18.366 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:18.386 -04:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:56:18.389 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:18.395 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:18.403 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 121.3972ms
2025-06-27 14:56:18.404 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.407 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 160.6354 ms
2025-06-27 14:56:18.408 -04:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Name], [u].[PasswordHash], [u].[Email], [u].[Mobile], [u].[MFA], [u].[IsActive], [u].[Description], [u].[ImageId], [u].[Language], [u].[Id], [u].[CreateBy], [u].[CreateAt], [u].[UpdateBy], [u].[UpdateAt]
FROM [UserAccounts] AS [u]
2025-06-27 14:56:18.410 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 171.7455ms
2025-06-27 14:56:18.413 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.UserAccountListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:18.415 -04:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:56:18.420 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetUserAccountList (Visfuture.OneTeam.BaseBiz.Api) in 274.3454ms
2025-06-27 14:56:18.426 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 14:56:18.426 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:18.427 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetUserAccountList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.432 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:18.433 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 123.5422ms
2025-06-27 14:56:18.434 -04:00 [INF] HTTP POST /UserAccountList responded 200 in 297.2996 ms
2025-06-27 14:56:18.435 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 116.4946ms
2025-06-27 14:56:18.437 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.442 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/UserAccountList - 200 null application/json; charset=utf-8 312.9535ms
2025-06-27 14:56:18.444 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:18.447 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 158.7477 ms
2025-06-27 14:56:18.456 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 151.8244 ms
2025-06-27 14:56:18.460 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 181.8223ms
2025-06-27 14:56:18.462 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 163.3735ms
2025-06-27 14:56:31.992 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - null null
2025-06-27 14:56:31.995 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:31.996 -04:00 [INF] HTTP OPTIONS /AssignableRoleList responded 204 in 1.2635 ms
2025-06-27 14:56:31.999 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - 204 null null 7.1879ms
2025-06-27 14:56:32.004 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AssignableRoleList - application/json 105
2025-06-27 14:56:32.007 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:32.008 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:32.010 -04:00 [INF] Route matched with {action = "QueryAssignableRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAssignableRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:32.034 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:32.070 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:32.249 -04:00 [INF] Executed DbCommand (65ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[OrganizationId], [a].[RoleId], [a].[IsInheritable], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AssignableRoles] AS [a]
2025-06-27 14:56:32.255 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AssignableRoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:32.260 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 247.9885ms
2025-06-27 14:56:32.264 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:32.267 -04:00 [INF] HTTP POST /AssignableRoleList responded 200 in 260.3922 ms
2025-06-27 14:56:32.314 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AssignableRoleList - 200 null application/json; charset=utf-8 310.4951ms
2025-06-27 14:56:32.321 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-06-27 14:56:32.325 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:32.326 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 1.3145 ms
2025-06-27 14:56:32.328 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 7.5222ms
2025-06-27 14:56:32.332 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 129
2025-06-27 14:56:32.337 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:32.338 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:32.340 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:32.396 -04:00 [INF] Executed DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:32.422 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:32.458 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:56:32.466 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:32.469 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 125.0266ms
2025-06-27 14:56:32.472 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:32.475 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 138.4780 ms
2025-06-27 14:56:32.480 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 147.9654ms
2025-06-27 14:56:32.488 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/RoleList - null null
2025-06-27 14:56:32.496 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:32.499 -04:00 [INF] HTTP OPTIONS /RoleList responded 204 in 2.6967 ms
2025-06-27 14:56:32.503 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/RoleList - 204 null null 15.32ms
2025-06-27 14:56:32.511 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleList - application/json 246
2025-06-27 14:56:32.516 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:32.519 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:32.521 -04:00 [INF] Route matched with {action = "QueryRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:32.561 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:32.591 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:32.791 -04:00 [INF] Executed DbCommand (77ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Name], [r].[Description], [r].[IsActive], [r].[Usage], [r].[Code], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [Role] AS [r]
WHERE [r].[Id] IN (
    SELECT [r0].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r0]
)
2025-06-27 14:56:32.801 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:32.806 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 279.0691ms
2025-06-27 14:56:32.810 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:32.812 -04:00 [INF] HTTP POST /RoleList responded 200 in 295.8709 ms
2025-06-27 14:56:32.853 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleList - 200 null application/json; charset=utf-8 341.503ms
2025-06-27 14:56:37.317 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - null null
2025-06-27 14:56:37.320 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:37.321 -04:00 [INF] HTTP OPTIONS /RoleAssignmentList responded 204 in 1.0803 ms
2025-06-27 14:56:37.324 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - 204 null null 7.8678ms
2025-06-27 14:56:37.329 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - application/json 107
2025-06-27 14:56:37.333 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:37.334 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:37.336 -04:00 [INF] Route matched with {action = "QueryRoleAssignmentList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleAssignmentListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:37.369 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:37.402 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:37.678 -04:00 [INF] Executed DbCommand (81ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[AssignableRoleId], [r].[EmployeeId], [r].[IsInheritable], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [RoleAssignments] AS [r]
2025-06-27 14:56:37.690 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleAssignmentListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:37.694 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api) in 353.5378ms
2025-06-27 14:56:37.696 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:37.698 -04:00 [INF] HTTP POST /RoleAssignmentList responded 200 in 364.7699 ms
2025-06-27 14:56:37.824 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - 200 null application/json; charset=utf-8 494.2917ms
2025-06-27 14:56:39.122 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - null null
2025-06-27 14:56:39.122 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-27 14:56:39.126 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:39.130 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:39.131 -04:00 [INF] HTTP OPTIONS /AssignableRoleList responded 204 in 4.8598 ms
2025-06-27 14:56:39.132 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.4799 ms
2025-06-27 14:56:39.135 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - 204 null null 12.2041ms
2025-06-27 14:56:39.137 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 14.4007ms
2025-06-27 14:56:39.142 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AssignableRoleList - application/json 39
2025-06-27 14:56:39.146 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-06-27 14:56:39.149 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:39.152 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:39.153 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:39.155 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:39.157 -04:00 [INF] Route matched with {action = "QueryAssignableRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAssignableRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:39.159 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:39.200 -04:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:39.229 -04:00 [INF] Executed DbCommand (64ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:39.285 -04:00 [INF] Executed DbCommand (77ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:39.313 -04:00 [INF] Executed DbCommand (71ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:39.350 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-06-27 14:56:39.354 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:39.356 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 191.7283ms
2025-06-27 14:56:39.359 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:39.360 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 208.6401 ms
2025-06-27 14:56:39.363 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 216.8569ms
2025-06-27 14:56:39.370 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-06-27 14:56:39.374 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:39.376 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:39.378 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:39.442 -04:00 [INF] Executed DbCommand (62ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:39.466 -04:00 [INF] Executed DbCommand (56ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[OrganizationId], [a].[RoleId], [a].[IsInheritable], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AssignableRoles] AS [a]
2025-06-27 14:56:39.471 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AssignableRoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:39.474 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:39.475 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 313.9083ms
2025-06-27 14:56:39.483 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:39.485 -04:00 [INF] HTTP POST /AssignableRoleList responded 200 in 336.4085 ms
2025-06-27 14:56:39.511 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:56:39.519 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:39.524 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 143.9487ms
2025-06-27 14:56:39.527 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:39.529 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 154.5185 ms
2025-06-27 14:56:39.531 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 161.3301ms
2025-06-27 14:56:39.577 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AssignableRoleList - 200 null application/json; charset=utf-8 434.3557ms
2025-06-27 14:56:49.277 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - null null
2025-06-27 14:56:49.280 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:49.281 -04:00 [INF] HTTP OPTIONS /OrganizationHierarchyList responded 204 in 1.1055 ms
2025-06-27 14:56:49.284 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/OrganizationHierarchyList - 204 null null 7.3022ms
2025-06-27 14:56:49.289 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 95
2025-06-27 14:56:49.293 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:49.294 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:49.295 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:49.374 -04:00 [INF] Executed DbCommand (75ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:49.470 -04:00 [INF] Executed DbCommand (91ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:49.599 -04:00 [INF] Executed DbCommand (121ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
2025-06-27 14:56:49.607 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:49.611 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 312.2394ms
2025-06-27 14:56:49.615 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:49.618 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 324.9878 ms
2025-06-27 14:56:49.622 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 332.7151ms
2025-06-27 14:56:49.637 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - application/json 90
2025-06-27 14:56:49.641 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:56:49.642 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:49.644 -04:00 [INF] Route matched with {action = "GetOrganizationHierarchyList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetOrganizationHierarchyList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.OrganizationHierarchyDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:56:49.701 -04:00 [INF] Executed DbCommand (54ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:56:49.776 -04:00 [INF] Executed DbCommand (71ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:56:49.809 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [o].[Name], [o].[Description], [o].[IsActive], [o].[MainType], [o].[SubType], [o].[SuperiorId], [o].[DefaultLocation], [o].[DefaultWorkSchedule], [o].[Code], [o].[TenantId], [o].[Id], [o].[CreateBy], [o].[CreateAt], [o].[UpdateBy], [o].[UpdateAt]
FROM [OrganizationHierarchies] AS [o]
WHERE [o].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:56:49.813 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.OrganizationHierarchyListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:56:49.815 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api) in 168.4214ms
2025-06-27 14:56:49.817 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetOrganizationHierarchyList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:56:49.818 -04:00 [INF] HTTP POST /OrganizationHierarchyList responded 200 in 177.2378 ms
2025-06-27 14:56:49.823 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/OrganizationHierarchyList - 200 null application/json; charset=utf-8 185.937ms
2025-06-27 14:57:03.915 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - null null
2025-06-27 14:57:03.919 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:57:03.920 -04:00 [INF] HTTP OPTIONS /RoleAssignmentList responded 204 in 1.1335 ms
2025-06-27 14:57:03.922 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/RoleAssignmentList - 204 null null 6.9643ms
2025-06-27 14:57:03.928 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - application/json 107
2025-06-27 14:57:03.931 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:57:03.932 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:57:03.933 -04:00 [INF] Route matched with {action = "QueryRoleAssignmentList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleAssignmentListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleAssignmentDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:57:03.984 -04:00 [INF] Executed DbCommand (48ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:57:04.016 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:57:04.133 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[AssignableRoleId], [r].[EmployeeId], [r].[IsInheritable], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [RoleAssignments] AS [r]
2025-06-27 14:57:04.138 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleAssignmentListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:57:04.142 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api) in 206.8351ms
2025-06-27 14:57:04.144 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleAssignmentListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:57:04.146 -04:00 [INF] HTTP POST /RoleAssignmentList responded 200 in 215.3793 ms
2025-06-27 14:57:04.204 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleAssignmentList - 200 null application/json; charset=utf-8 276.5233ms
2025-06-27 14:57:05.090 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - null null
2025-06-27 14:57:05.090 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-27 14:57:05.095 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:57:05.098 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:57:05.099 -04:00 [INF] HTTP OPTIONS /AssignableRoleList responded 204 in 4.4055 ms
2025-06-27 14:57:05.101 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.3159 ms
2025-06-27 14:57:05.104 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/AssignableRoleList - 204 null null 13.06ms
2025-06-27 14:57:05.108 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 17.0568ms
2025-06-27 14:57:05.114 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/AssignableRoleList - application/json 39
2025-06-27 14:57:05.118 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-06-27 14:57:05.123 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:57:05.128 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:57:05.130 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:57:05.132 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:57:05.133 -04:00 [INF] Route matched with {action = "QueryAssignableRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryAssignableRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.AssignableRoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:57:05.135 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:57:05.159 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:57:05.218 -04:00 [INF] Executed DbCommand (77ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:57:05.243 -04:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:57:05.267 -04:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:57:05.313 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-06-27 14:57:05.329 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:57:05.331 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 190.7121ms
2025-06-27 14:57:05.333 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:57:05.335 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 206.6410 ms
2025-06-27 14:57:05.338 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 219.687ms
2025-06-27 14:57:05.343 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-06-27 14:57:05.346 -04:00 [INF] CORS policy execution successful.
2025-06-27 14:57:05.347 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:57:05.349 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 14:57:05.369 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[OrganizationId], [a].[RoleId], [a].[IsInheritable], [a].[TenantId], [a].[Id], [a].[CreateBy], [a].[CreateAt], [a].[UpdateBy], [a].[UpdateAt]
FROM [AssignableRoles] AS [a]
2025-06-27 14:57:05.373 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.AssignableRoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:57:05.376 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 238.6489ms
2025-06-27 14:57:05.378 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryAssignableRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:57:05.379 -04:00 [INF] HTTP POST /AssignableRoleList responded 200 in 256.4306 ms
2025-06-27 14:57:05.391 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 14:57:05.416 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 14:57:05.510 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/AssignableRoleList - 200 null application/json; charset=utf-8 396.5509ms
2025-06-27 14:57:05.510 -04:00 [INF] Executed DbCommand (85ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 14:57:05.528 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 14:57:05.531 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 176.3879ms
2025-06-27 14:57:05.533 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 14:57:05.534 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 188.1035 ms
2025-06-27 14:57:05.537 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 193.5487ms
2025-06-27 14:58:51.336 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-27 14:58:51.628 -04:00 [INF] Now listening on: http://localhost:5275
2025-06-27 14:58:51.703 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-27 14:58:51.707 -04:00 [INF] Hosting environment: Development
2025-06-27 14:58:51.709 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-06-27 15:20:06.783 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/login - null null
2025-06-27 15:20:06.850 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:20:06.855 -04:00 [INF] HTTP OPTIONS /login responded 204 in 39.3660 ms
2025-06-27 15:20:06.875 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/login - 204 null null 97.6623ms
2025-06-27 15:20:06.888 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/login - application/json 68
2025-06-27 15:20:06.895 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:20:06.991 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController.Login (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:20:07.016 -04:00 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.UserLoginRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 15:20:08.477 -04:00 [INF] Executed DbCommand (88ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 15:20:08.551 -04:00 [INF] Executed DbCommand (61ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 15:20:08.825 -04:00 [INF] Executed DbCommand (52ms) [Parameters=[@__request_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreateAt], [u].[CreateBy], [u].[Description], [u].[Email], [u].[ImageId], [u].[IsActive], [u].[Language], [u].[MFA], [u].[Mobile], [u].[Name], [u].[PasswordHash], [u].[UpdateAt], [u].[UpdateBy]
FROM [UserAccounts] AS [u]
WHERE [u].[Name] = @__request_Username_0
2025-06-27 15:20:08.844 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.Core.Common.Base.Models.UserJwtDto, Visfuture.OneTeam.Core.Common, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 15:20:08.859 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController.Login (Visfuture.OneTeam.BaseBiz.Api) in 1837.813ms
2025-06-27 15:20:08.861 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController.Login (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:20:08.864 -04:00 [INF] HTTP POST /login responded 200 in 1969.5669 ms
2025-06-27 15:20:08.980 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/login - 200 null application/json; charset=utf-8 2091.9591ms
2025-06-27 15:20:30.447 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/login - null null
2025-06-27 15:20:30.454 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:20:30.456 -04:00 [INF] HTTP OPTIONS /login responded 204 in 1.8242 ms
2025-06-27 15:20:30.458 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/login - 204 null null 11.6956ms
2025-06-27 15:20:30.467 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/login - application/json 43
2025-06-27 15:20:30.470 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:20:30.472 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController.Login (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:20:30.474 -04:00 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.UserLoginRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 15:20:30.682 -04:00 [INF] Executed DbCommand (59ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 15:20:30.713 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 15:20:30.748 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[@__request_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreateAt], [u].[CreateBy], [u].[Description], [u].[Email], [u].[ImageId], [u].[IsActive], [u].[Language], [u].[MFA], [u].[Mobile], [u].[Name], [u].[PasswordHash], [u].[UpdateAt], [u].[UpdateBy]
FROM [UserAccounts] AS [u]
WHERE [u].[Name] = @__request_Username_0
2025-06-27 15:20:30.789 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.Core.Common.Base.Models.UserJwtDto, Visfuture.OneTeam.Core.Common, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 15:20:30.793 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController.Login (Visfuture.OneTeam.BaseBiz.Api) in 316.8802ms
2025-06-27 15:20:30.794 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController.Login (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:20:30.796 -04:00 [INF] HTTP POST /login responded 200 in 325.5803 ms
2025-06-27 15:20:30.903 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/login - 200 null application/json; charset=utf-8 435.357ms
2025-06-27 15:20:30.908 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/UserTenantList - null null
2025-06-27 15:20:30.911 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:20:30.912 -04:00 [INF] HTTP OPTIONS /UserTenantList responded 204 in 1.0438 ms
2025-06-27 15:20:30.914 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/UserTenantList - 204 null null 6.0134ms
2025-06-27 15:20:30.921 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/UserTenantList - application/json null
2025-06-27 15:20:30.926 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:20:30.970 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController.GetUser (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:20:30.976 -04:00 [INF] Route matched with {action = "GetUser", controller = "Account"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUser() on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 15:20:31.162 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 15:20:31.193 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 15:20:31.277 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[@__userId_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[CreateAt], [t].[CreateBy], [t].[Description], [t].[Email], [t].[ImageId], [t].[IsActive], [t].[Language], [t].[MFA], [t].[Mobile], [t].[Name], [t].[PasswordHash], [t].[UpdateAt], [t].[UpdateBy], [e].[Id], [e].[BusinessEmail], [e].[Code], [e].[CreateAt], [e].[CreateBy], [e].[Description], [e].[EmployeeStatus], [e].[HireDate], [e].[IsActive], [e].[IsTenantAdmin], [e].[JobTitle], [e].[MainType], [e].[Name], [e].[PositionId], [e].[SubType], [e].[TenantId], [e].[TerminateDate], [e].[UpdateAt], [e].[UpdateBy], [e].[UserAccountId], [e].[WorkScheduleId]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreateAt], [u].[CreateBy], [u].[Description], [u].[Email], [u].[ImageId], [u].[IsActive], [u].[Language], [u].[MFA], [u].[Mobile], [u].[Name], [u].[PasswordHash], [u].[UpdateAt], [u].[UpdateBy]
    FROM [UserAccounts] AS [u]
    WHERE CONVERT(varchar(36), [u].[Id]) = @__userId_0
) AS [t]
LEFT JOIN [Employee] AS [e] ON [t].[Id] = [e].[UserAccountId]
ORDER BY [t].[Id]
2025-06-27 15:20:31.457 -04:00 [INF] Executed DbCommand (108ms) [Parameters=[@__tenants_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Name], [t].[Description], [t].[Address1], [t].[Address2], [t].[City], [t].[Province], [t].[PostalCode], [t].[EffectiveDate], [t].[ExpireDate], [t].[IsActive], [t].[Domain], [t].[Language], [t].[TimeZone], [t].[ContactName], [t].[ContactPhone], [t].[ContactFax], [t].[ContactEmail], [t].[Id], [t].[CreateBy], [t].[CreateAt], [t].[UpdateBy], [t].[UpdateAt]
FROM [Tenant] AS [t]
WHERE [t].[Id] IN (
    SELECT [t0].[value]
    FROM OPENJSON(@__tenants_0) WITH ([value] uniqueidentifier '$') AS [t0]
)
2025-06-27 15:20:31.467 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[System.Collections.Generic.List`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.TenantDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-27 15:20:31.482 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController.GetUser (Visfuture.OneTeam.BaseBiz.Api) in 503.8675ms
2025-06-27 15:20:31.485 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController.GetUser (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:20:31.487 -04:00 [INF] HTTP GET /UserTenantList responded 200 in 560.5892 ms
2025-06-27 15:20:31.618 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/UserTenantList - 200 null application/json; charset=utf-8 696.3639ms
2025-06-27 15:20:31.624 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SetTenant - null null
2025-06-27 15:20:31.627 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:20:31.628 -04:00 [INF] HTTP OPTIONS /SetTenant responded 204 in 1.4437 ms
2025-06-27 15:20:31.631 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SetTenant - 204 null null 6.3435ms
2025-06-27 15:20:31.635 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SetTenant - application/json 2
2025-06-27 15:20:31.638 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:20:31.641 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController.SetTenant (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:20:31.645 -04:00 [INF] Route matched with {action = "SetTenant", controller = "Account"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] SetTenant(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.GuidRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 15:20:31.908 -04:00 [INF] Executed DbCommand (137ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 15:20:31.933 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 15:20:31.988 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[@__userId_0='?' (Size = 4000), @__tenantId_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[RoleId]
FROM [Employee] AS [e]
INNER JOIN [RoleAssignments] AS [r] ON [e].[Id] = [r].[EmployeeId]
INNER JOIN [AssignableRoles] AS [a] ON [r].[AssignableRoleId] = [a].[Id]
WHERE CONVERT(varchar(36), [e].[UserAccountId]) = @__userId_0 AND [e].[TenantId] = @__tenantId_Id_1
2025-06-27 15:20:32.025 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[CreateAt], [a].[CreateBy], [a].[Description], [a].[IsActive], [a].[IsPublic], [a].[ModuleId], [a].[Name], [a].[ResourceCode], [a].[SubType], [a].[SuperiorId], [a].[SystemId], [a].[TenantId], [a].[UpdateAt], [a].[UpdateBy]
FROM [Role] AS [r]
INNER JOIN [RoleAccesses] AS [r0] ON [r].[Id] = [r0].[RoleId]
INNER JOIN [AccessResource] AS [a] ON [r0].[ResourceId] = [a].[Id]
WHERE 0 = 1
2025-06-27 15:20:32.033 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse'.
2025-06-27 15:20:32.036 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController.SetTenant (Visfuture.OneTeam.BaseBiz.Api) in 387.8782ms
2025-06-27 15:20:32.037 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.AccountController.SetTenant (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:20:32.038 -04:00 [INF] HTTP POST /SetTenant responded 200 in 399.7471 ms
2025-06-27 15:20:32.150 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SetTenant - 200 null application/json; charset=utf-8 515.2585ms
2025-06-27 15:40:49.311 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - null null
2025-06-27 15:40:49.316 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:40:49.317 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.3681 ms
2025-06-27 15:40:49.319 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - 204 null null 8.5719ms
2025-06-27 15:40:49.326 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - application/json null
2025-06-27 15:40:49.335 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:40:49.343 -04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:40:49 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-27 15:40:49.365 -04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:40:49 PM'.
2025-06-27 15:40:49.368 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:40:49.378 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 15:40:49.632 -04:00 [INF] Executed DbCommand (85ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 15:40:49.661 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 15:40:49.732 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 15:40:49.812 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 15:40:49.827 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 444.5344ms
2025-06-27 15:40:49.833 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:40:49.835 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 500.6500 ms
2025-06-27 15:40:49.839 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - 200 null application/json; charset=utf-8 514.096ms
2025-06-27 15:40:49.846 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-27 15:40:49.851 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:40:49.853 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.6871 ms
2025-06-27 15:40:49.857 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 10.5014ms
2025-06-27 15:40:49.865 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 91
2025-06-27 15:40:49.871 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:40:49.874 -04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:40:49 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-27 15:40:49.879 -04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:40:49 PM'.
2025-06-27 15:40:49.883 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:40:49.890 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 15:40:50.136 -04:00 [INF] Executed DbCommand (241ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 15:40:50.173 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 15:40:50.287 -04:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 15:40:50.332 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 15:40:50.353 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 458.4292ms
2025-06-27 15:40:50.357 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:40:50.359 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 488.0912 ms
2025-06-27 15:40:50.363 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 497.4143ms
2025-06-27 15:40:50.372 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-27 15:40:50.377 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:40:50.380 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.2856 ms
2025-06-27 15:40:50.384 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 12.1534ms
2025-06-27 15:40:50.392 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-27 15:40:50.400 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:40:50.403 -04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:40:50 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-27 15:40:50.409 -04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:40:50 PM'.
2025-06-27 15:40:50.413 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:40:50.423 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 15:40:50.574 -04:00 [INF] Executed DbCommand (145ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 15:40:50.624 -04:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 15:40:50.781 -04:00 [INF] Executed DbCommand (104ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 15:40:50.791 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 15:40:50.804 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 377.1409ms
2025-06-27 15:40:50.810 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:40:50.814 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 414.6805 ms
2025-06-27 15:40:50.822 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 430.8367ms
2025-06-27 15:40:50.830 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - null null
2025-06-27 15:40:50.834 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:40:50.835 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.7052 ms
2025-06-27 15:40:50.839 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - 204 null null 9.7205ms
2025-06-27 15:40:50.847 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - application/json null
2025-06-27 15:40:50.851 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:40:50.855 -04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:40:50 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-27 15:40:50.859 -04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:40:50 PM'.
2025-06-27 15:40:50.861 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:40:50.864 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 15:40:50.891 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 15:40:50.924 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 15:40:50.961 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-27 15:40:50.972 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 15:40:50.976 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 108.5724ms
2025-06-27 15:40:50.979 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:40:50.982 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 130.6523 ms
2025-06-27 15:40:50.985 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - 200 null application/json; charset=utf-8 138.4589ms
2025-06-27 15:40:50.992 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 91
2025-06-27 15:40:50.999 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:40:51.001 -04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:40:51 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-27 15:40:51.004 -04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:40:51 PM'.
2025-06-27 15:40:51.006 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:40:51.007 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 15:40:51.054 -04:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 15:40:51.083 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 15:40:51.131 -04:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-27 15:40:51.139 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 15:40:51.141 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 131.2945ms
2025-06-27 15:40:51.143 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:40:51.145 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 146.6174 ms
2025-06-27 15:40:51.148 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 156.1037ms
2025-06-27 15:45:33.398 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-27 15:45:33.406 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:45:33.407 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.1305 ms
2025-06-27 15:45:33.411 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 13.004ms
2025-06-27 15:45:33.421 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-27 15:45:33.426 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:45:33.428 -04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:45:33 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-27 15:45:33.431 -04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:45:33 PM'.
2025-06-27 15:45:33.433 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:45:33.435 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 15:45:33.461 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 15:45:33.496 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 15:45:33.598 -04:00 [INF] Executed DbCommand (89ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-27 15:45:33.604 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 15:45:33.607 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 169.4156ms
2025-06-27 15:45:33.610 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:45:33.614 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 188.8293 ms
2025-06-27 15:45:33.617 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 196.6915ms
2025-06-27 15:45:34.994 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-06-27 15:45:35.000 -04:00 [INF] CORS policy execution successful.
2025-06-27 15:45:35.002 -04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:45:35 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-27 15:45:35.005 -04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/27/2025 7:21:30 PM', Current time (UTC): '6/27/2025 7:45:35 PM'.
2025-06-27 15:45:35.006 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:45:35.008 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-27 15:45:35.064 -04:00 [INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-27 15:45:35.105 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-27 15:45:35.165 -04:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-06-27 15:45:35.170 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-27 15:45:35.174 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 163.6158ms
2025-06-27 15:45:35.178 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-27 15:45:35.181 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 180.9240 ms
2025-06-27 15:45:35.184 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 189.9047ms
