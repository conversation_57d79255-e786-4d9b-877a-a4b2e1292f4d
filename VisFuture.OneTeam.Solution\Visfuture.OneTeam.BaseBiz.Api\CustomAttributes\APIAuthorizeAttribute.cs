﻿using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using StackExchange.Redis;

namespace Visfuture.OneTeam.BaseBiz.Api.CustomAttributes;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class
    APIAuthorizeAttribute : TypeFilterAttribute //using TypeFilter as the custom attribute relies on IDistributedCache service that need DI.

{
    public APIAuthorizeAttribute(string resourceCode)
        : base(typeof(APIAuthorizeFilter))
    {
        Arguments = [resourceCode];
    }
}

public class APIAuthorizeFilter(IConnectionMultiplexer redis, string resourceCode) : IActionFilter
{
    public void OnActionExecuting(ActionExecutingContext context)
    {
        // Check for AllowAnonymous attribute
        bool allowAnonymous = context.ActionDescriptor.EndpointMetadata
            .Any(em => em.GetType() == typeof(AllowAnonymousAttribute));

        if (allowAnonymous) return;

        if (string.IsNullOrEmpty(resourceCode)) return;
        // Extract the User from HttpContext
        ClaimsPrincipal user = context.HttpContext.User;

        // Ensure the user is authenticated
        if (user.Identity?.IsAuthenticated != true)
        {
            context.Result = new UnauthorizedResult(); //401 error
            return;
        }

        // Retrieve the userId from the claims 
        Claim? userIdClaim = user.Claims.FirstOrDefault(c => c.Type == "UserId");

        if (userIdClaim == null)
        {
            context.Result = new UnauthorizedResult(); //401 error
            return;
        }

        // Add the userId to the ActionArguments for use in the action
        // context.ActionArguments["userId"] = userIdClaim.Value;

        IDatabase redb = redis.GetDatabase();

        if (!redb.SetContains(new RedisKey(userIdClaim.Value), new RedisValue(resourceCode)))
            context.Result = new ForbidResult();
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        // Optional: Logic after action execution
    }
}