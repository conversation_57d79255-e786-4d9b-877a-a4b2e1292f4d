{"format": 1, "restore": {"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.DataAccess\\Visfuture.OneTeam.Project.DataAccess.csproj": {}}, "projects": {"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Core.Common\\Visfuture.OneTeam.Core.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Core.Common\\Visfuture.OneTeam.Core.Common.csproj", "projectName": "Visfuture.OneTeam.Core.Common", "projectPath": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Core.Common\\Visfuture.OneTeam.Core.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Core.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Azure.Identity": {"target": "Package", "version": "[1.14.0, )"}, "ClosedXML": {"target": "Package", "version": "[0.104.2, )"}, "Mapster": {"target": "Package", "version": "[7.4.0, )"}, "Mapster.DependencyInjection": {"target": "Package", "version": "[1.0.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.10, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Graph": {"target": "Package", "version": "[5.82.0, )"}, "Minio": {"target": "Package", "version": "[6.0.4, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "xunit": {"target": "Package", "version": "[2.9.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.Common\\Visfuture.OneTeam.Project.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.Common\\Visfuture.OneTeam.Project.Common.csproj", "projectName": "Visfuture.OneTeam.Project.Common", "projectPath": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.Common\\Visfuture.OneTeam.Project.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Core.Common\\Visfuture.OneTeam.Core.Common.csproj": {"projectPath": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Core.Common\\Visfuture.OneTeam.Core.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.DataAccess\\Visfuture.OneTeam.Project.DataAccess.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.DataAccess\\Visfuture.OneTeam.Project.DataAccess.csproj", "projectName": "Visfuture.OneTeam.Project.DataAccess", "projectPath": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.DataAccess\\Visfuture.OneTeam.Project.DataAccess.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.DataAccess\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.Common\\Visfuture.OneTeam.Project.Common.csproj": {"projectPath": "D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Project.Common\\Visfuture.OneTeam.Project.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Mapster": {"target": "Package", "version": "[7.4.0, )"}, "Mapster.DependencyInjection": {"target": "Package", "version": "[1.0.1, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.10, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}