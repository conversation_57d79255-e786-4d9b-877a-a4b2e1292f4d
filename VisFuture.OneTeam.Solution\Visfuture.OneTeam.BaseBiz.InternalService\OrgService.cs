using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Interfaces;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.InternalService;

public class OrgService(IOrgManager orgManager) : IOrgService
{
    private readonly IOrgManager _orgManager = orgManager;

    #region Employee

    public async Task<EntityResponse<EmployeeDto>> GetEmployeeByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.GetEmployeeByIdAsync(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddEmployeeAsync(EmployeeDto employeeDto,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.AddEmployeeAsync(employeeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateEmployeeAsync(EmployeeDto employeeDto,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.UpdateEmployeeAsync(employeeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteEmployeeAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _orgManager.DeleteEmployeeAsync(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<EmployeeListItemDto>> QueryEmployeeAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.QueryEmployeeAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> AddEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.AddEmployeesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.UpdateEmployeesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.DeleteEmployeesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportEmployeeExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.ExportEmployeeExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportEmployeeExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _orgManager.ImportEmployeeExcel(file, cancellationToken);
    }

    #endregion

    #region GlobalAdmin

    public async Task<EntityResponse<GlobalAdminDto>> GetGlobalAdminByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.GetGlobalAdminByIdAsync(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddGlobalAdminAsync(GlobalAdminDto globalAdminDto,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.AddGlobalAdminAsync(globalAdminDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateGlobalAdminAsync(GlobalAdminDto globalAdminDto,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.UpdateGlobalAdminAsync(globalAdminDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteGlobalAdminAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.DeleteGlobalAdminAsync(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<GlobalAdminListItemDto>> QueryGlobalAdminAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.QueryGlobalAdminAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> AddGlobalAdminsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.AddGlobalAdminsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateGlobalAdminsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.UpdateGlobalAdminsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteGlobalAdminsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.DeleteGlobalAdminsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportGlobalAdminExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.ExportGlobalAdminExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportGlobalAdminExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.ImportGlobalAdminExcel(file, cancellationToken);
    }

    #endregion

    #region OrganizationEmployee

    public async Task<EntityResponsePaged<EmployeeListItemDto>> QueryEmployeesByOrganizationAsync(BaseQuery<EmployeeDto> request, CancellationToken cancellationToken = default)
    {
        return await _orgManager.QueryEmployeesByOrganizationAsync(request, cancellationToken);
    }


    public async Task<EntityResponse<OrganizationEmployeeDto>> GetOrganizationEmployeeByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.GetOrganizationEmployeeByIdAsync(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddOrganizationEmployeeAsync(
        OrganizationEmployeeDto organizationEmployeeDto, CancellationToken cancellationToken = default)
    {
        return await _orgManager.AddOrganizationEmployeeAsync(organizationEmployeeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateOrganizationEmployeeAsync(
        OrganizationEmployeeDto organizationEmployeeDto, CancellationToken cancellationToken = default)
    {
        return await _orgManager.UpdateOrganizationEmployeeAsync(organizationEmployeeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteOrganizationEmployeeAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.DeleteOrganizationEmployeeAsync(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<OrganizationEmployeeListItemDto>> QueryOrganizationEmployeeAsync(
        BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _orgManager.QueryOrganizationEmployeeAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> AddOrganizationEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.AddOrganizationEmployeesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateOrganizationEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.UpdateOrganizationEmployeesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteOrganizationEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.DeleteOrganizationEmployeesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportOrganizationEmployeeExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.ExportOrganizationEmployeeExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportOrganizationEmployeeExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.ImportOrganizationEmployeeExcel(file, cancellationToken);
    }

    #endregion

    #region OrganizationHierarchy

    public async Task<EntityResponse<OrganizationHierarchyDto>> GetOrganizationHierarchyByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.GetOrganizationHierarchyByIdAsync(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddOrganizationHierarchyAsync(
        OrganizationHierarchyDto organizationHierarchyDto, CancellationToken cancellationToken = default)
    {
        return await _orgManager.AddOrganizationHierarchyAsync(organizationHierarchyDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateOrganizationHierarchyAsync(
        OrganizationHierarchyDto organizationHierarchyDto, CancellationToken cancellationToken = default)
    {
        return await _orgManager.UpdateOrganizationHierarchyAsync(organizationHierarchyDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteOrganizationHierarchyAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.DeleteOrganizationHierarchyAsync(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<OrganizationHierarchyListItemDto>> QueryOrganizationHierarchyAsync(
        BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _orgManager.QueryOrganizationHierarchyAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> AddOrganizationHierarchiesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.AddOrganizationHierarchiesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateOrganizationHierarchiesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.UpdateOrganizationHierarchiesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteOrganizationHierarchiesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.DeleteOrganizationHierarchiesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportOrganizationHierarchyExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.ExportOrganizationHierarchyExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportOrganizationHierarchyExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.ImportOrganizationHierarchyExcel(file, cancellationToken);
    }

    #endregion

    #region Tenant

    public async Task<EntityResponse<TenantDto>> GetTenantByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.GetTenantByIdAsync(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTenantAsync(TenantDto tenantDto,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.AddTenantAsync(tenantDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTenantAsync(TenantDto tenantDto,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.UpdateTenantAsync(tenantDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTenantAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _orgManager.DeleteTenantAsync(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<TenantListItemDto>> QueryTenantAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.QueryTenantAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> AddTenantsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _orgManager.AddTenantsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTenantsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.UpdateTenantsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTenantsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.DeleteTenantsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTenantExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.ExportTenantExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTenantExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _orgManager.ImportTenantExcel(file, cancellationToken);
    }

    #endregion

    #region User Accounts

    public async Task<EntityResponse<UserAccountDto>> GetUserAccountByIdAsync(Guid userAccountId,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.GetUserAccountByIdAsync(userAccountId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddUserAccountAsync(UserAccountDto model,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.AddUserAccountAsync(model, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateUserAccountAsync(UserAccountDto model,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.UpdateUserAccountAsync(model, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteUserAccountAsync(Guid userAccountId,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.DeleteUserAccountAsync(userAccountId, cancellationToken);
    }

    public async Task<EntityResponsePaged<UserAccountListItemDto>> QueryUserAccountAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.QueryUserAccountAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> AddUserAccountsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.AddUserAccountsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateUserAccountsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.UpdateUserAccountsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteUserAccountsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.DeleteUserAccountsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportUserAccountExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.ExportUserAccountExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportUserAccountExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _orgManager.ImportUserAccountExcel(file, cancellationToken);
    }

    #endregion
}