﻿using System.ComponentModel.DataAnnotations;

namespace Visfuture.OneTeam.Core.Common.Base.Models;

public class BaseQuery
{
    protected BaseDto[]? dtoList;
    private int pageIndex = 1;
    private int pageSize;

    public int PageSize
    {
        get
        {
            if (pageSize < 0) pageSize = 10;
            return pageSize;
        }
        set => pageSize = value;
    }

    public List<Guid>? IdList { get; set; }
    public List<string>? NoList { get; set; }

    public BaseDto[]? DtoList
    {
        get => dtoList;
        set => dtoList = value;
    }

    public string? FilterString { get; set; } = string.Empty;
    public Dictionary<string, HashSet<string?>>? FieldValues { get; set; }
    public string? UniqueFromField { get; set; } // Get all unique values from a column
    public bool? IsActive { get; set; }

    public int PageIndex
    {
        get
        {
            if (pageIndex < 1) return 1;
            return pageIndex;
        }
        set => pageIndex = value;
    }


    public IList<PageSort> Sorts { get; set; } = [];

    public string SortField
    {
        get
        {
            if (Sorts.Count > 0) return Sorts[0].SortField;
            return string.Empty;
        }
    }

    public SortOperator SortDirection
    {
        get
        {
            if (Sorts.Count > 0) return Sorts[0].SortDirection;
            return SortOperator.Asc;
        }
    }

    public string OrderBy
    {
        get
        {
            if (string.IsNullOrWhiteSpace(SortField)) return string.Empty;

            return string.Format("{0} {1}", SortField, SortDirection.ToString().ToLowerInvariant());
        }
    }
}

//public abstract class BasePageRequest<T> : BasePagedQuery where T : struct
//{
//    public T? FilterKey { get; set; }
//}

public class BaseQuery<T> : BaseQuery where T : BaseDto
{
    public new T[]? DtoList
    {
        get => dtoList as T[];
        set => dtoList = value;
    }
}

public class PageSort
{
    [Required] public string SortField { get; set; } = default!;

    [Required] public SortOperator SortDirection { get; set; } = SortOperator.Asc;
}