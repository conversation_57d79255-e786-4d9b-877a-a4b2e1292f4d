using Microsoft.AspNetCore.Mvc;
using Visfuture.OneTeam.Project.BusinessLogic.DTOs;
using Visfuture.OneTeam.Project.InternalService.Interfaces;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.Project.Api.Controllers;

[ApiController]
[Route("[controller]")]
public class ExternalController(IProjectService projectService) : BaseController<ProjectController>
{
    [HttpGet("GetWorkTimeByTicketId")]
    public async Task<IActionResult> GetWorkTimeByTicketIdExt(Guid ticketId, CancellationToken cancellationToken = default)
    {
        EntityResponse<int> result = await projectService.GetWorkTimeByTicketIdAsync(ticketId, cancellationToken);
        return Ok(result);
    }
}