﻿namespace Visfuture.OneTeam.Core.Common.Helpers;

public static class UserClaims
{
    public const string Issuer = "OneTeam";
    public const string Audience = "OneTeamApp";
    public const string CURRENT_USER_CONTEXT = nameof(CURRENT_USER_CONTEXT);
    public const string CURRENT_WEB_TYPE = nameof(CURRENT_WEB_TYPE);

    public const string UserId = nameof(UserId);
    public const string UserName = nameof(UserName);
    public const string FirstName = nameof(FirstName);
    public const string LastName = nameof(LastName);
    public const string Email = nameof(Email);
    public const string RoleType = nameof(RoleType);
    public const string RefreshToken = nameof(RefreshToken);
    public const string ClientId = nameof(ClientId);
    public const string Permission = nameof(Permission);
}
