import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface SequenceNo extends TenantEntity {
  name: string
  prefix?: string
  suffix?: string
  currentNo: number
  length: number
}

export const sequenceNoInfo: Info<SequenceNo> = {
  typeName: 'Sequence Number',
  nameKey: 'name',
  sortKey: 'name',
  backend: 'BaseBiz',
  endpoint: 'SequenceNo',
  fields: {
    name: { label: 'Sequence Code', type: 'smalltext', required: true },
    prefix: { label: 'Prefix', type: 'smalltext' },
    suffix: { label: 'Suffix', type: 'smalltext' },
    currentNo: { label: 'Current Number', type: 'number', disabled: true },
    length: { label: 'Length', type: 'number', required: true },
    ...TenantEntityInfo.fields,
  },
  default: {
    name: '',
    currentNo: 0,
    length: 0,
  },
  options: {
    ...TenantEntityInfo.options,
  },
  columnsShown: new Set(['name', 'prefix', 'suffix', 'currentNo', 'length']),
  formLayout: [
    ['name', 'prefix', 'suffix'],
    ['currentNo', 'length'],
  ],
}
