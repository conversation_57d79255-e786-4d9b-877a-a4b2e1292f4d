﻿using MapsterMapper;
using Visfuture.OneTeam.BaseBiz.DataAccess.DbContext;
using Visfuture.OneTeam.Core.Common.Base.Services;

namespace Visfuture.OneTeam.BaseBiz.InternalService;

public class BaseBizBaseService(AppDataContext appDataContext, IMapper mapper) : BaseService
{
    public AppDataContext DB { get; private set; } = appDataContext;
    public IMapper Mapper { get; private set; } = mapper;
}
