﻿using Visfuture.OneTeam.BaseBiz.BusinessLogic.Interfaces;
using Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.InternalService;

public class JwtTokenService(IJwtTokenManager jwtTokenManager) : IJwtTokenService
{
    public string GenerateRefreshToken(Guid userId)
    {
        return jwtTokenManager.GenerateRefreshToken(userId);
    }

    public string GenerateToken(Guid userId, string userName, Guid? tenantId = null)
    {
        return jwtTokenManager.GenerateToken(userId, userName, tenantId);
    }

    public void InvalidateRefreshToken(Guid userId)
    {
        jwtTokenManager.InvalidateRefreshToken(userId);
    }

    public EntityResponse<UserJwtDto> RenewJwtToken(Guid userId, string refreshToken)
    {
        return jwtTokenManager.RenewJwtToken(userId, refreshToken);
    }

    public bool ValidateRefreshToken(Guid userId, string refreshToken)
    {
        return jwtTokenManager.ValidateRefreshToken(userId, refreshToken);
    }
}