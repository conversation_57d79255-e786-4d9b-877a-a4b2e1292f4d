import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface ProductDeploymentTracking extends TenantEntity {
  productId: string
  productReleaseId: string
  clientId: string
  status?: string
  siteId?: string
  siteName?: string
  dateDeployed?: Date
  targetEnvironment?: string
  description?: string
}

export const Statuses = ['Planned', 'In Progress', 'Completed', 'Failed'] as const
export type Status = (typeof Statuses)[number]
export const TargetEnvironments = ['Development', 'Testing', 'Staging', 'Production'] as const
export type TargetEnvironment = (typeof TargetEnvironments)[number]

export const productDeploymentTrackingInfo: Info<ProductDeploymentTracking> = {
  typeName: 'Product Deployment Tracking',
  nameKey: 'productReleaseId',
  sortKey: 'clientId',
  sortAsc: true,
  backend: 'Ticket',
  endpoint: 'ProductDeploymentTracking',
  fields: {
    productId: { label: 'Product', type: 'external', required: true },
    productReleaseId: { label: 'Release ID', type: 'external' },
    clientId: { label: 'Client ID', type: 'external', required: true },
    siteName: { label: 'Site Name', type: 'smalltext' },
    siteId: { label: 'Site Address', type: 'bigtext' },
    status: { label: 'Status', type: 'select' },
    targetEnvironment: { label: 'Target Environment', type: 'select' },
    dateDeployed: { label: 'Date Deployed', type: 'date' },
    description: { label: 'Description', type: 'textarea' },
    ...TenantEntityInfo.fields,
  },
  options: {
    productId: { entity: 'Product' },
    productReleaseId: { entity: 'ProductReleaseTracking', joins: { productId: 'productId' } },
    clientId: { entity: 'Company' },
    status: { options: Statuses.slice() },
    targetEnvironment: { options: TargetEnvironments.slice() },
    ...TenantEntityInfo.options,
  },
  default: {
    productId: '',
    productReleaseId: '',
    clientId: '',
    status: '',
    siteId: '',
    siteName: '',
    dateDeployed: new Date(),
    targetEnvironment: '',
    description: '',
  },
  columnsShown: new Set([
    'productId',
    'productReleaseId',
    'clientId',
    'status',
    'siteId',
    'siteName',
    'dateDeployed',
    'targetEnvironment',
    'description',
  ]),
  formLayout: [
    ['productId', 'productReleaseId'],
    ['clientId', 'status'],
    ['siteName', 'siteId'],
    ['dateDeployed', 'targetEnvironment'],
    ['description'],
  ],
}
