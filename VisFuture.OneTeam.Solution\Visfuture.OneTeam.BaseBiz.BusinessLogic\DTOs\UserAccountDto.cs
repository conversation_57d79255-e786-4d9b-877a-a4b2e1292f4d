﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;

public class UserAccountDto : BaseDto
{
    public string Name { get; set; } = null!;

    public string PasswordHash { get; set; } = null!;

    public string Email { get; set; } = null!;

    public string? Mobile { get; set; }

    public bool MFA { get; set; }

    public bool IsActive { get; set; }

    public string? Description { get; set; }

    public string? ImageId { get; set; }

    public string? Language { get; set; }
    
    public virtual ICollection<EmployeeDto> Employees { get; set; } = [];
}