using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;

public interface IOrgService
{
    #region Employee

    Task<EntityResponse<EmployeeDto>> GetEmployeeByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddEmployeeAsync(EmployeeDto employeeDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateEmployeeAsync(EmployeeDto employeeDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteEmployeeAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponsePaged<EmployeeListItemDto>> QueryEmployeeAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddEmployeesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateEmployeesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteEmployeesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<B64File>> ExportEmployeeExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportEmployeeExcel(B64File file, CancellationToken cancellationToken = default);

    #endregion

    #region GlobalAdmin

    Task<EntityResponse<GlobalAdminDto>>
        GetGlobalAdminByIdAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddGlobalAdminAsync(GlobalAdminDto globalAdminDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateGlobalAdminAsync(GlobalAdminDto globalAdminDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteGlobalAdminAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponsePaged<GlobalAdminListItemDto>> QueryGlobalAdminAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddGlobalAdminsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateGlobalAdminsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteGlobalAdminsAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportGlobalAdminExcel(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> ImportGlobalAdminExcel(B64File file, CancellationToken cancellationToken = default);

    #endregion

    #region OrganizationEmployee

    Task<EntityResponsePaged<EmployeeListItemDto>> QueryEmployeesByOrganizationAsync(BaseQuery<EmployeeDto> request, CancellationToken cancellationToken = default);

    Task<EntityResponse<OrganizationEmployeeDto>> GetOrganizationEmployeeByIdAsync(Guid id,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddOrganizationEmployeeAsync(OrganizationEmployeeDto organizationEmployeeDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateOrganizationEmployeeAsync(OrganizationEmployeeDto organizationEmployeeDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteOrganizationEmployeeAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponsePaged<OrganizationEmployeeListItemDto>> QueryOrganizationEmployeeAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse>
        AddOrganizationEmployeesAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse> UpdateOrganizationEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> DeleteOrganizationEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportOrganizationEmployeeExcel(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> ImportOrganizationEmployeeExcel(B64File file, CancellationToken cancellationToken = default);

    #endregion

    #region OrganizationHierarchy

    Task<EntityResponse<OrganizationHierarchyDto>> GetOrganizationHierarchyByIdAsync(Guid id,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddOrganizationHierarchyAsync(OrganizationHierarchyDto organizationHierarchyDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateOrganizationHierarchyAsync(OrganizationHierarchyDto organizationHierarchyDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteOrganizationHierarchyAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponsePaged<OrganizationHierarchyListItemDto>> QueryOrganizationHierarchyAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddOrganizationHierarchiesAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> UpdateOrganizationHierarchiesAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> DeleteOrganizationHierarchiesAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportOrganizationHierarchyExcel(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> ImportOrganizationHierarchyExcel(B64File file, CancellationToken cancellationToken = default);

    #endregion

    #region Tenant

    Task<EntityResponse<TenantDto>> GetTenantByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddTenantAsync(TenantDto tenantDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateTenantAsync(TenantDto tenantDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteTenantAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponsePaged<TenantListItemDto>> QueryTenantAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddTenantsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateTenantsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteTenantsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<B64File>> ExportTenantExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportTenantExcel(B64File file, CancellationToken cancellationToken = default);

    #endregion

    #region User Accounts

    Task<EntityResponse<UserAccountDto>>
        GetUserAccountByIdAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddUserAccountAsync(UserAccountDto userAccountDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateUserAccountAsync(UserAccountDto userAccountDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteUserAccountAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponsePaged<UserAccountListItemDto>> QueryUserAccountAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddUserAccountsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateUserAccountsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteUserAccountsAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportUserAccountExcel(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> ImportUserAccountExcel(B64File file, CancellationToken cancellationToken = default);

    #endregion
}