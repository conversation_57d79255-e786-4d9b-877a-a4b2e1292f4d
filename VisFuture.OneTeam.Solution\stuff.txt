no contract main type
what does to<PERSON><PERSON> do?
createdBy use id instead of name
testing framework for backend
how to use storyblocks
redis rpc
add i18n form labels
add calender timeline view, ask jay<PERSON> about it

put add & toggle options at the bottom
change page bug
isActive custom text
comment all the code
cannot rollback asNoTracking
control add component medium size
add patch request for update
implement access resource and permissions heiarchy
replace more things with codeitems
implement objlist
ai agent
descriptions hyperlink ~
@ people in descriptions
rich text editor

constant trunc fix?
add things to sprints
replace createBy updateBy with guid, make them "ed"
extend basequery
json patch request
projectmanager GUID
add proper database indices and primary keys
change all hard-coded options to codeTypes
website expiry & refresh timer
separate API into /EntityName/Add
add customers to invoice queue
upload logo in tenant

DON'T FOLLOW WHAT'S IN HERE DO WHAT BILL ASKS YOU!!!