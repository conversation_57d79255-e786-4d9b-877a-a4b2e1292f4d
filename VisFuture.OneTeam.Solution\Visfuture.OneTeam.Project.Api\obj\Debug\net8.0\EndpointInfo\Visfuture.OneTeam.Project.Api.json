{"openapi": "3.0.1", "info": {"title": "Visfuture.OneTeam.Project.Api", "version": "1.0"}, "paths": {"/CompanyList": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/CompanyDetail": {"get": {"tags": ["Company"], "parameters": [{"name": "companyId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddCompany": {"put": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateCompany": {"put": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteCompany": {"delete": {"tags": ["Company"], "parameters": [{"name": "companyId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddCompanyList": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateCompanyList": {"put": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteCompanyList": {"delete": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportCompanyList": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportCompanyList": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ContactList": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ContactDetail": {"get": {"tags": ["Company"], "parameters": [{"name": "contactId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddContact": {"put": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContactDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContactDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateContact": {"put": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContactDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContactDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteContact": {"delete": {"tags": ["Company"], "parameters": [{"name": "contactId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddContactList": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateContactList": {"put": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteContactList": {"delete": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportContactList": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContactDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportContactList": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/GetContactsByEmail": {"get": {"tags": ["Company"], "parameters": [{"name": "email", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/External/GetWorkTimeByTicketId": {"get": {"tags": ["External"], "parameters": [{"name": "ticketId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/ProjectList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProjectDetail": {"get": {"tags": ["Project"], "parameters": [{"name": "projectId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddProject": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddProjectList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProject": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProjectList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteProject": {"delete": {"tags": ["Project"], "parameters": [{"name": "projectId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/DeleteProjectList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportProjectList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportProjectList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ContractList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ContractDetail": {"get": {"tags": ["Project"], "parameters": [{"name": "contractId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddContract": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContractDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddContractList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateContract": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContractDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateContractList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteContract": {"delete": {"tags": ["Project"], "parameters": [{"name": "contractId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/DeleteContractList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportContractList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportContractList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/RelatedPartyList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/RelatedPartyDetail": {"get": {"tags": ["Project"], "parameters": [{"name": "relatedPartyId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddRelatedParty": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddRelatedPartyList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateRelatedParty": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateRelatedPartyList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteRelatedParty": {"delete": {"tags": ["Project"], "parameters": [{"name": "relatedPartyId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/DeleteRelatedPartyList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportRelatedPartyList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RelatedPartyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportRelatedPartyList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProjectRoleAssignmentList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProjectRoleAssignmentDetail": {"get": {"tags": ["Project"], "parameters": [{"name": "roleAssignmentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddProjectRoleAssignment": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddProjectRoleAssignmentList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProjectRoleAssignment": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProjectRoleAssignmentList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteProjectRoleAssignment": {"delete": {"tags": ["Project"], "parameters": [{"name": "projectRoleAssignmentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/DeleteProjectRoleAssignmentList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportProjectRoleAssignmentList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectRoleAssignmentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportProjectRoleAssignmentList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TimeLogList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceQueueQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceQueueQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceQueueQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TimeLogDetail": {"get": {"tags": ["Project"], "parameters": [{"name": "timeLogId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTimeLog": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TimeLogDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TimeLogDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TimeLogDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddTimeLogList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TimeLogDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TimeLogDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TimeLogDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTimeLog": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TimeLogDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TimeLogDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TimeLogDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTimeLogList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TimeLogDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TimeLogDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TimeLogDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTimeLog": {"delete": {"tags": ["Project"], "parameters": [{"name": "timeLogId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/DeleteTimeLogList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TimeLogDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TimeLogDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TimeLogDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportTimeLogList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TimeLogDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TimeLogDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TimeLogDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportTimeLogList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProjectDocumentList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProjectDocumentDetail": {"get": {"tags": ["Project"], "parameters": [{"name": "projectDocumentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddProjectDocument": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddProjectDocumentList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProjectDocument": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProjectDocumentList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteProjectDocument": {"delete": {"tags": ["Project"], "parameters": [{"name": "projectDocumentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/DeleteProjectDocumentList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportProjectDocumentList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDocumentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportProjectDocumentList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TaskList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TaskDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TaskDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TaskDetail": {"get": {"tags": ["Project"], "parameters": [{"name": "taskId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTask": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TaskDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TaskDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddTaskList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TaskDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TaskDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTask": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TaskDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TaskDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTaskList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TaskDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TaskDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTask": {"delete": {"tags": ["Project"], "parameters": [{"name": "taskId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/DeleteTaskList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TaskDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TaskDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProjectPaymentPlanList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProjectPaymentPlanDetail": {"get": {"tags": ["Project"], "parameters": [{"name": "projectPaymentPlanId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddProjectPaymentPlan": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddProjectPaymentPlanList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProjectPaymentPlan": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProjectPaymentPlanList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteProjectPaymentPlan": {"delete": {"tags": ["Project"], "parameters": [{"name": "projectPaymentPlanId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/DeleteProjectPaymentPlanList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentPlanDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProjectPaymentFactList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ProjectPaymentFactDetail": {"get": {"tags": ["Project"], "parameters": [{"name": "projectPaymentFactId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddProjectPaymentFact": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AddProjectPaymentFactList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProjectPaymentFact": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateProjectPaymentFactList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteProjectPaymentFact": {"delete": {"tags": ["Project"], "parameters": [{"name": "projectPaymentFactId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/DeleteProjectPaymentFactList": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectPaymentFactDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}, "post": {"tags": ["WeatherForecast"], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"B64File": {"type": "object", "properties": {"mimeType": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "data": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BaseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Company": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "phone": {"type": "string", "nullable": true}, "fax": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "province": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "webSite": {"type": "string", "nullable": true}, "alias": {"type": "string", "nullable": true}, "currency": {"type": "string", "nullable": true}, "companyContacts": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyContact"}, "nullable": true}}, "additionalProperties": false}, "CompanyContact": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "companyId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "jobTitle": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "fax": {"type": "string", "nullable": true}, "mobile": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "province": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "company": {"$ref": "#/components/schemas/Company"}}, "additionalProperties": false}, "CompanyDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "phone": {"type": "string", "nullable": true}, "fax": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "province": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "webSite": {"type": "string", "nullable": true}, "alias": {"type": "string", "nullable": true}, "currency": {"type": "string", "nullable": true}, "companyContacts": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyContact"}, "nullable": true}}, "additionalProperties": false}, "CompanyDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ContactDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "companyId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "jobTitle": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "fax": {"type": "string", "nullable": true}, "mobile": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "province": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "ContactDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/ContactDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ContractDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "relatedPartyA": {"type": "string", "format": "uuid"}, "relatedPartyB": {"type": "string", "format": "uuid"}, "relatedPartyC": {"type": "string", "format": "uuid", "nullable": true}, "relatedPartyD": {"type": "string", "format": "uuid", "nullable": true}, "mainType": {"type": "string", "nullable": true}, "subType": {"type": "string", "nullable": true}, "currency": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "taxRate": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "totalAmount": {"type": "number", "format": "double"}, "effectiveDate": {"type": "string", "format": "date-time"}, "sumMethod": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ContractDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/ContractDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "InvoiceQueueQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/TimeLogDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "billable": {"type": "boolean", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PageSort": {"required": ["sortDirection", "sortField"], "type": "object", "properties": {"sortField": {"minLength": 1, "type": "string"}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}}, "additionalProperties": false}, "Project": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "mainType": {"type": "string", "nullable": true}, "subType": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "status": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "projectManager": {"type": "string", "nullable": true}, "projectContracts": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectContract"}, "nullable": true}, "projectRelatedParties": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectRelatedParty"}, "nullable": true}, "projectRoleAssignments": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectRoleAssignment"}, "nullable": true}, "projectTimeLogs": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTimeLog"}, "nullable": true}, "projectDocuments": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectDocument"}, "nullable": true}, "projectTasks": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}, "nullable": true}, "projectPaymentPlans": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectPaymentPlan"}, "nullable": true}, "projectPaymentFacts": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectPaymentFact"}, "nullable": true}}, "additionalProperties": false}, "ProjectContract": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "relatedPartyA": {"type": "string", "format": "uuid"}, "relatedPartyB": {"type": "string", "format": "uuid"}, "relatedPartyC": {"type": "string", "format": "uuid", "nullable": true}, "relatedPartyD": {"type": "string", "format": "uuid", "nullable": true}, "mainType": {"type": "string", "nullable": true}, "subType": {"type": "string", "nullable": true}, "currency": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "taxRate": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "totalAmount": {"type": "number", "format": "double"}, "effectiveDate": {"type": "string", "format": "date-time"}, "sumMethod": {"type": "string", "nullable": true}, "project": {"$ref": "#/components/schemas/Project"}}, "additionalProperties": false}, "ProjectDocument": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "category": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "fileType": {"type": "string", "nullable": true}, "documentId": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "project": {"$ref": "#/components/schemas/Project"}}, "additionalProperties": false}, "ProjectDocumentDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "category": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "fileType": {"type": "string", "nullable": true}, "documentId": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProjectDocumentDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectDocumentDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ProjectDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "mainType": {"type": "string", "nullable": true}, "subType": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "status": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "projectManager": {"type": "string", "nullable": true}, "projectContracts": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectContract"}, "nullable": true}, "projectRelatedParties": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectRelatedParty"}, "nullable": true}, "projectRoleAssignments": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectRoleAssignment"}, "nullable": true}, "projectTimeLogs": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTimeLog"}, "nullable": true}, "projectTasks": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}, "nullable": true}}, "additionalProperties": false}, "ProjectDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ProjectPaymentFact": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "payPlanId": {"type": "string", "format": "uuid", "nullable": true}, "contractId": {"type": "string", "format": "uuid", "nullable": true}, "payerId": {"type": "string", "format": "uuid"}, "payeeId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "payMethod": {"type": "string", "nullable": true}, "transInfo": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "payDate": {"type": "string", "format": "date-time"}, "percentage": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "taxRate": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "totalAmount": {"type": "number", "format": "double"}, "project": {"$ref": "#/components/schemas/Project"}, "paymentPlan": {"$ref": "#/components/schemas/ProjectPaymentPlan"}}, "additionalProperties": false}, "ProjectPaymentFactDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "payPlanId": {"type": "string", "format": "uuid", "nullable": true}, "contractId": {"type": "string", "format": "uuid", "nullable": true}, "payerId": {"type": "string", "format": "uuid"}, "payeeId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "payMethod": {"type": "string", "nullable": true}, "transInfo": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "payDate": {"type": "string", "format": "date-time"}, "percentage": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "taxRate": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "totalAmount": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ProjectPaymentFactDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectPaymentFactDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ProjectPaymentPlan": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "contractId": {"type": "string", "format": "uuid", "nullable": true}, "payerId": {"type": "string", "format": "uuid"}, "payeeId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "payMethod": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "dueDate": {"type": "string", "format": "date-time"}, "percentage": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "taxRate": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "totalAmount": {"type": "number", "format": "double"}, "project": {"$ref": "#/components/schemas/Project"}, "paymentFacts": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectPaymentFact"}, "nullable": true}}, "additionalProperties": false}, "ProjectPaymentPlanDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "contractId": {"type": "string", "format": "uuid", "nullable": true}, "payerId": {"type": "string", "format": "uuid"}, "payeeId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "payMethod": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "dueDate": {"type": "string", "format": "date-time"}, "percentage": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "taxRate": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "totalAmount": {"type": "number", "format": "double"}, "paymentFacts": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectPaymentFact"}, "nullable": true}}, "additionalProperties": false}, "ProjectPaymentPlanDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectPaymentPlanDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ProjectRelatedParty": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "companyId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "relatedType": {"type": "string", "nullable": true}, "contactName": {"type": "string", "nullable": true}, "contactJobTitle": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "nullable": true}, "contactPhone": {"type": "string", "nullable": true}, "contactFax": {"type": "string", "nullable": true}, "contactMobile": {"type": "string", "nullable": true}, "contactAddress1": {"type": "string", "nullable": true}, "contactAddress2": {"type": "string", "nullable": true}, "contactCity": {"type": "string", "nullable": true}, "contactProvince": {"type": "string", "nullable": true}, "contactPostalCode": {"type": "string", "nullable": true}, "currency": {"type": "string", "nullable": true}, "project": {"$ref": "#/components/schemas/Project"}}, "additionalProperties": false}, "ProjectRoleAssignment": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "roleCode": {"type": "string", "nullable": true}, "employeeId": {"type": "string", "nullable": true}, "hourlyCurrency": {"type": "string", "nullable": true}, "hourlyRate": {"type": "number", "format": "double", "nullable": true}, "description": {"type": "string", "nullable": true}, "project": {"$ref": "#/components/schemas/Project"}}, "additionalProperties": false}, "ProjectRoleAssignmentDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "roleCode": {"type": "string", "nullable": true}, "employeeId": {"type": "string", "nullable": true}, "hourlyCurrency": {"type": "string", "nullable": true}, "hourlyRate": {"type": "number", "format": "double", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProjectRoleAssignmentDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectRoleAssignmentDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ProjectTask": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "wbs": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "assigneeId": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "milestone": {"type": "boolean"}, "description": {"type": "string", "nullable": true}, "project": {"$ref": "#/components/schemas/Project"}}, "additionalProperties": false}, "ProjectTimeLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "ticketId": {"type": "string", "format": "uuid", "nullable": true}, "employeeId": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "taskType": {"type": "string", "nullable": true}, "comment": {"type": "string", "nullable": true}, "workMinutes": {"type": "integer", "format": "int32"}, "factor": {"type": "number", "format": "double"}, "adjustTime": {"type": "integer", "format": "int32", "nullable": true}, "invoiceId": {"type": "string", "format": "uuid", "nullable": true}, "isAdminEntry": {"type": "boolean"}, "preSelectFlag": {"type": "boolean"}, "project": {"$ref": "#/components/schemas/Project"}}, "additionalProperties": false}, "RelatedPartyDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "companyId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "relatedType": {"type": "string", "nullable": true}, "contactName": {"type": "string", "nullable": true}, "contactJobTitle": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "nullable": true}, "contactPhone": {"type": "string", "nullable": true}, "contactFax": {"type": "string", "nullable": true}, "contactMobile": {"type": "string", "nullable": true}, "contactAddress1": {"type": "string", "nullable": true}, "contactAddress2": {"type": "string", "nullable": true}, "contactCity": {"type": "string", "nullable": true}, "contactProvince": {"type": "string", "nullable": true}, "contactPostalCode": {"type": "string", "nullable": true}, "currency": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RelatedPartyDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/RelatedPartyDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "SortOperator": {"enum": [0, 1], "type": "integer", "format": "int32"}, "TaskDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "wbs": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "assigneeId": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "milestone": {"type": "boolean"}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TaskDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/TaskDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TimeLogDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "ticketId": {"type": "string", "format": "uuid", "nullable": true}, "employeeId": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "taskType": {"type": "string", "nullable": true}, "comment": {"type": "string", "nullable": true}, "workMinutes": {"type": "integer", "format": "int32"}, "factor": {"type": "number", "format": "double"}, "adjustTime": {"type": "integer", "format": "int32", "nullable": true}, "invoiceId": {"type": "string", "format": "uuid", "nullable": true}, "isAdminEntry": {"type": "boolean"}, "preSelectFlag": {"type": "boolean"}}, "additionalProperties": false}, "TimeLogDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/TimeLogDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Authorization header.\r\nExample:'Bearer 123456abcdef'", "name": "Authorization", "in": "header"}}}, "security": [{"Authorization": []}]}