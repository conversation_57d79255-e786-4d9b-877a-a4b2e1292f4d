2025-06-05 15:33:49.937 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - null null
2025-06-05 15:33:50.091 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:50.096 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 19.3383 ms
2025-06-05 15:33:50.104 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - 204 null null 169.7125ms
2025-06-05 15:33:50.110 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - application/json null
2025-06-05 15:33:50.117 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:50.123 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:50.128 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:50.391 -04:00 [INF] Executed DbCommand (84ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:50.421 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:50.464 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:33:50.471 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:50.476 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 344.3159ms
2025-06-05 15:33:50.479 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:50.482 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 365.1522 ms
2025-06-05 15:33:50.486 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - 200 null application/json; charset=utf-8 375.2941ms
2025-06-05 15:33:50.492 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-05 15:33:50.497 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:50.498 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.4776 ms
2025-06-05 15:33:50.500 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 8.3026ms
2025-06-05 15:33:50.505 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 91
2025-06-05 15:33:50.509 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:50.511 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:50.513 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:50.537 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:50.594 -04:00 [INF] Executed DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:50.625 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:33:50.653 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:50.657 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 141.5258ms
2025-06-05 15:33:50.659 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:50.661 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 151.7142 ms
2025-06-05 15:33:50.664 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 158.593ms
2025-06-05 15:33:50.669 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-05 15:33:50.674 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:50.675 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.7954 ms
2025-06-05 15:33:50.679 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 9.5041ms
2025-06-05 15:33:50.684 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-05 15:33:50.687 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:50.688 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:50.690 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:50.818 -04:00 [INF] Executed DbCommand (123ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:50.881 -04:00 [INF] Executed DbCommand (57ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:51.022 -04:00 [INF] Executed DbCommand (128ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-05 15:33:51.028 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:51.032 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 338.5098ms
2025-06-05 15:33:51.037 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:51.041 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 353.5144 ms
2025-06-05 15:33:51.045 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 361.0191ms
2025-06-05 15:33:51.052 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - null null
2025-06-05 15:33:51.058 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:51.060 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 2.2286 ms
2025-06-05 15:33:51.064 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - 204 null null 12.5891ms
2025-06-05 15:33:51.071 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - application/json null
2025-06-05 15:33:51.075 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:51.077 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:51.078 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:51.109 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:51.184 -04:00 [INF] Executed DbCommand (73ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:51.210 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:33:51.214 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:51.216 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 133.9753ms
2025-06-05 15:33:51.218 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:51.219 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 143.9421 ms
2025-06-05 15:33:51.222 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - 200 null application/json; charset=utf-8 150.8869ms
2025-06-05 15:33:51.227 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 91
2025-06-05 15:33:51.230 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:51.231 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:51.232 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:51.258 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:51.285 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:51.356 -04:00 [INF] Executed DbCommand (65ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:33:51.371 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:51.375 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 139.9106ms
2025-06-05 15:33:51.377 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:51.379 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 149.0515 ms
2025-06-05 15:33:51.382 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 155.5993ms
2025-06-05 15:33:54.997 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - application/json null
2025-06-05 15:33:55.004 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - application/json null
2025-06-05 15:33:55.011 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:55.019 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-06-05 15:33:55.022 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:55.023 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:55.026 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:55.027 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:55.029 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:55.030 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:55.032 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:55.039 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:55.057 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:55.097 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:55.143 -04:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:55.172 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:55.207 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:33:55.218 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:55.226 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 190.1088ms
2025-06-05 15:33:55.227 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:55.230 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:55.242 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 234.8470 ms
2025-06-05 15:33:55.249 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - 200 null application/json; charset=utf-8 251.4717ms
2025-06-05 15:33:55.255 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-05 15:33:55.260 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:55.262 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:55.265 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:55.266 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:55.301 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:33:55.301 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:55.306 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:55.312 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 270.2087ms
2025-06-05 15:33:55.314 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:55.316 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 294.0755 ms
2025-06-05 15:33:55.319 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - 200 null application/json; charset=utf-8 314.7682ms
2025-06-05 15:33:55.345 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:55.354 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-05 15:33:55.365 -04:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-06-05 15:33:55.365 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:55.368 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:55.369 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:55.371 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 327.7533ms
2025-06-05 15:33:55.373 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:55.375 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:55.379 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 352.9890 ms
2025-06-05 15:33:55.382 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 363.6868ms
2025-06-05 15:33:55.385 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:33:55.394 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:55.397 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 118.2294ms
2025-06-05 15:33:55.400 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:55.400 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:55.402 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 141.5950 ms
2025-06-05 15:33:55.408 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 153.1171ms
2025-06-05 15:33:55.437 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:55.470 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:33:55.488 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:55.491 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 113.405ms
2025-06-05 15:33:55.493 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:55.495 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 130.1534 ms
2025-06-05 15:33:55.498 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 143.941ms
2025-06-05 15:33:55.585 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/RoleList - null null
2025-06-05 15:33:55.590 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:55.592 -04:00 [INF] HTTP OPTIONS /RoleList responded 204 in 1.7764 ms
2025-06-05 15:33:55.595 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/RoleList - 204 null null 9.5438ms
2025-06-05 15:33:55.601 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleList - application/json 90
2025-06-05 15:33:55.605 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:55.607 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:55.610 -04:00 [INF] Route matched with {action = "QueryRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:55.637 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:55.670 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:55.868 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Name], [r].[Description], [r].[IsActive], [r].[Usage], [r].[Code], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [Role] AS [r]
WHERE [r].[Id] IN (
    SELECT [r0].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r0]
)
2025-06-05 15:33:55.876 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:55.881 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 267.7502ms
2025-06-05 15:33:55.884 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:55.886 -04:00 [INF] HTTP POST /RoleList responded 200 in 281.5103 ms
2025-06-05 15:33:55.945 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleList - 200 null application/json; charset=utf-8 344.0684ms
2025-06-05 15:33:55.952 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-05 15:33:55.958 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:55.960 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.3308 ms
2025-06-05 15:33:55.962 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 10.0586ms
2025-06-05 15:33:55.969 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 90
2025-06-05 15:33:55.973 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:55.975 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:55.976 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:56.072 -04:00 [INF] Executed DbCommand (89ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:56.100 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:56.206 -04:00 [INF] Executed DbCommand (96ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-05 15:33:56.212 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:56.214 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 233.8418ms
2025-06-05 15:33:56.216 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:56.218 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 245.1132 ms
2025-06-05 15:33:56.223 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 253.4793ms
2025-06-05 15:33:56.229 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - null null
2025-06-05 15:33:56.235 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:56.237 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 2.0853 ms
2025-06-05 15:33:56.243 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - 204 null null 13.5121ms
2025-06-05 15:33:56.250 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - application/json null
2025-06-05 15:33:56.257 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:56.259 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:56.261 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:56.290 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:56.319 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:56.349 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:33:56.354 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:56.357 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 92.3482ms
2025-06-05 15:33:56.359 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:56.361 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 103.4725 ms
2025-06-05 15:33:56.364 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - 200 null application/json; charset=utf-8 114.3363ms
2025-06-05 15:33:56.371 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-05 15:33:56.377 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:56.379 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.8334 ms
2025-06-05 15:33:56.382 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 11.6117ms
2025-06-05 15:33:56.389 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 87
2025-06-05 15:33:56.394 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:56.396 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:56.398 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:56.428 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:56.463 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:56.497 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:33:56.512 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:56.517 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 114.6443ms
2025-06-05 15:33:56.521 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:56.525 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 130.8913 ms
2025-06-05 15:33:56.530 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 140.8714ms
2025-06-05 15:33:56.644 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - null null
2025-06-05 15:33:56.652 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:56.653 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.5019 ms
2025-06-05 15:33:56.657 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - 204 null null 13.0861ms
2025-06-05 15:33:56.663 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - application/json null
2025-06-05 15:33:56.667 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:56.668 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:56.670 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:56.695 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:56.732 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:56.767 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:33:56.775 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:56.778 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 104.8827ms
2025-06-05 15:33:56.781 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:56.784 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 117.7428 ms
2025-06-05 15:33:56.789 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - 200 null application/json; charset=utf-8 126.2504ms
2025-06-05 15:33:56.796 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 91
2025-06-05 15:33:56.801 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:56.803 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:56.807 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:56.862 -04:00 [INF] Executed DbCommand (49ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:56.904 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:56.977 -04:00 [INF] Executed DbCommand (61ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:33:56.992 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:56.997 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 185.4958ms
2025-06-05 15:33:57.000 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:57.003 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 201.3036 ms
2025-06-05 15:33:57.007 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 210.9699ms
2025-06-05 15:33:57.015 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - null null
2025-06-05 15:33:57.020 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:57.023 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 2.8072 ms
2025-06-05 15:33:57.027 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - 204 null null 12.3796ms
2025-06-05 15:33:57.034 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - application/json null
2025-06-05 15:33:57.040 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:57.042 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:57.043 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:57.126 -04:00 [INF] Executed DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:57.159 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:57.190 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:33:57.198 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:57.203 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 156.6978ms
2025-06-05 15:33:57.211 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:57.216 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 176.8789 ms
2025-06-05 15:33:57.222 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - 200 null application/json; charset=utf-8 188.1913ms
2025-06-05 15:33:57.233 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 85
2025-06-05 15:33:57.238 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:33:57.240 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:57.242 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:33:57.267 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:33:57.299 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:33:57.339 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:33:57.355 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:33:57.358 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 112.5608ms
2025-06-05 15:33:57.360 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:33:57.361 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 122.9914 ms
2025-06-05 15:33:57.364 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 130.9074ms
2025-06-05 15:34:09.259 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - null null
2025-06-05 15:34:09.265 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:09.266 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.1071 ms
2025-06-05 15:34:09.269 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - 204 null null 10.1929ms
2025-06-05 15:34:09.276 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - application/json null
2025-06-05 15:34:09.280 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:09.281 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:09.283 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:09.315 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:09.354 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:09.385 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:34:09.394 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:09.397 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 111.2353ms
2025-06-05 15:34:09.401 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:09.403 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 123.7737 ms
2025-06-05 15:34:09.409 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - 200 null application/json; charset=utf-8 132.9603ms
2025-06-05 15:34:09.414 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-05 15:34:09.420 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:09.422 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.9836 ms
2025-06-05 15:34:09.425 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 11.2844ms
2025-06-05 15:34:09.431 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 91
2025-06-05 15:34:09.436 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:09.437 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:09.440 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:09.472 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:09.504 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:09.538 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:34:09.553 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:09.559 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 116.5587ms
2025-06-05 15:34:09.564 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:09.566 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 130.1920 ms
2025-06-05 15:34:09.571 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 139.7912ms
2025-06-05 15:34:09.582 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - null null
2025-06-05 15:34:09.589 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:09.591 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 2.3771 ms
2025-06-05 15:34:09.598 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - 204 null null 16.1808ms
2025-06-05 15:34:09.606 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - application/json null
2025-06-05 15:34:09.609 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:09.610 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:09.612 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:09.689 -04:00 [INF] Executed DbCommand (72ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:09.785 -04:00 [INF] Executed DbCommand (87ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:09.831 -04:00 [INF] Executed DbCommand (37ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:34:09.842 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:09.845 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 228.8024ms
2025-06-05 15:34:09.848 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:09.850 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 240.6541 ms
2025-06-05 15:34:09.854 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - 200 null application/json; charset=utf-8 247.4956ms
2025-06-05 15:34:09.862 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 85
2025-06-05 15:34:09.867 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:09.869 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:09.871 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:09.906 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:09.936 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:09.973 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:34:09.988 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:09.993 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 115.8633ms
2025-06-05 15:34:09.996 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:09.999 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 131.3564 ms
2025-06-05 15:34:10.001 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 139.1936ms
2025-06-05 15:34:14.627 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - null null
2025-06-05 15:34:14.631 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:14.634 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 2.2074 ms
2025-06-05 15:34:14.637 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - 204 null null 10.2024ms
2025-06-05 15:34:14.642 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - application/json null
2025-06-05 15:34:14.646 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:14.647 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:14.649 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:14.677 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:14.706 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:14.745 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:34:14.762 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:14.766 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 113.698ms
2025-06-05 15:34:14.771 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:14.775 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 128.8323 ms
2025-06-05 15:34:14.778 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - 200 null application/json; charset=utf-8 136.2084ms
2025-06-05 15:34:14.786 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-05 15:34:14.791 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:14.792 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.1122 ms
2025-06-05 15:34:14.795 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 9.146ms
2025-06-05 15:34:14.802 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 91
2025-06-05 15:34:14.809 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:14.810 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:14.811 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:14.851 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:14.887 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:14.926 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:34:14.945 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:14.950 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 133.3981ms
2025-06-05 15:34:14.954 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:14.956 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 147.3930 ms
2025-06-05 15:34:14.960 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 157.7373ms
2025-06-05 15:34:14.966 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - null null
2025-06-05 15:34:14.971 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:14.972 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.2560 ms
2025-06-05 15:34:14.976 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - 204 null null 10.0058ms
2025-06-05 15:34:14.980 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - application/json null
2025-06-05 15:34:14.985 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:14.987 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:14.991 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:15.081 -04:00 [INF] Executed DbCommand (86ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:15.117 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:15.153 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:34:15.162 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:15.166 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 172.777ms
2025-06-05 15:34:15.170 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:15.174 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 188.3349 ms
2025-06-05 15:34:15.177 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - 200 null application/json; charset=utf-8 197.1325ms
2025-06-05 15:34:15.182 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 85
2025-06-05 15:34:15.186 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:15.188 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:15.191 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:15.219 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:15.255 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:15.291 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:34:15.306 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:15.311 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 116.4566ms
2025-06-05 15:34:15.315 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:15.317 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 131.0949 ms
2025-06-05 15:34:15.321 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 139.3331ms
2025-06-05 15:34:17.095 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - application/json null
2025-06-05 15:34:17.095 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - null null
2025-06-05 15:34:17.095 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - application/json null
2025-06-05 15:34:17.099 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:17.104 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:17.108 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:17.109 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:17.110 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 6.1141 ms
2025-06-05 15:34:17.112 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:17.114 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:17.117 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - 204 null null 22.5043ms
2025-06-05 15:34:17.119 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:17.129 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - application/json null
2025-06-05 15:34:17.136 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:17.137 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:17.140 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:17.155 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:17.157 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:17.174 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:17.184 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:17.192 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:17.206 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:17.212 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:34:17.217 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:17.219 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 97.441ms
2025-06-05 15:34:17.221 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:17.223 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 123.7393 ms
2025-06-05 15:34:17.226 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - 200 null application/json; charset=utf-8 131.3117ms
2025-06-05 15:34:17.230 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:34:17.232 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-05 15:34:17.235 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:17.239 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:17.242 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 109.6187ms
2025-06-05 15:34:17.243 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:17.244 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:34:17.245 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:17.246 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:17.250 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:17.251 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 143.5347 ms
2025-06-05 15:34:17.257 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 113.4654ms
2025-06-05 15:34:17.261 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - 200 null application/json; charset=utf-8 165.2264ms
2025-06-05 15:34:17.263 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:17.267 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-05 15:34:17.269 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 133.0746 ms
2025-06-05 15:34:17.275 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:17.279 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - 200 null application/json; charset=utf-8 149.5083ms
2025-06-05 15:34:17.281 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:17.281 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:17.290 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-05 15:34:17.292 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:17.300 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:17.303 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:17.304 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:17.318 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:17.336 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:17.336 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:17.358 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:34:17.363 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:17.372 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:17.378 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:17.380 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 126.3044ms
2025-06-05 15:34:17.382 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:17.384 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 145.3948 ms
2025-06-05 15:34:17.387 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 155.0535ms
2025-06-05 15:34:17.389 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:34:17.397 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:17.398 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:34:17.399 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 91.7355ms
2025-06-05 15:34:17.403 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:17.403 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:17.405 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 102.7615ms
2025-06-05 15:34:17.406 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 106.0034 ms
2025-06-05 15:34:17.408 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:17.410 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 120.8029ms
2025-06-05 15:34:17.411 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 136.5696 ms
2025-06-05 15:34:17.417 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 149.8173ms
2025-06-05 15:34:24.018 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - null null
2025-06-05 15:34:24.025 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:24.025 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 0.9776 ms
2025-06-05 15:34:24.028 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - 204 null null 10.0024ms
2025-06-05 15:34:24.033 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - application/json null
2025-06-05 15:34:24.038 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:24.040 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:24.043 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:24.068 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:24.103 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:24.136 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:34:24.147 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:24.150 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 103.4766ms
2025-06-05 15:34:24.154 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:24.157 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 119.1827 ms
2025-06-05 15:34:24.162 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractType - 200 null application/json; charset=utf-8 128.4047ms
2025-06-05 15:34:24.168 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-05 15:34:24.173 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:24.175 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.8097 ms
2025-06-05 15:34:24.179 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 11.113ms
2025-06-05 15:34:24.185 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 91
2025-06-05 15:34:24.191 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:24.192 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:24.193 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:24.224 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:24.257 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:24.295 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:34:24.313 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:24.317 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 120.8294ms
2025-06-05 15:34:24.320 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:24.321 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 130.6395 ms
2025-06-05 15:34:24.324 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 139.3809ms
2025-06-05 15:34:24.328 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - null null
2025-06-05 15:34:24.332 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:24.333 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.2664 ms
2025-06-05 15:34:24.336 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - 204 null null 7.9922ms
2025-06-05 15:34:24.343 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - application/json null
2025-06-05 15:34:24.348 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:24.350 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:24.352 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:24.453 -04:00 [INF] Executed DbCommand (95ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:24.486 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:24.531 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:34:24.538 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:24.544 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 187.7757ms
2025-06-05 15:34:24.546 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:24.549 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 202.3909 ms
2025-06-05 15:34:24.553 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ContractSumMethod - 200 null application/json; charset=utf-8 209.4166ms
2025-06-05 15:34:24.561 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 85
2025-06-05 15:34:24.566 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:24.568 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:24.571 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:24.603 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:24.641 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:24.682 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:34:24.705 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:24.708 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 131.9784ms
2025-06-05 15:34:24.709 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:24.711 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 144.9744 ms
2025-06-05 15:34:24.715 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 154.1486ms
2025-06-05 15:34:29.832 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - null null
2025-06-05 15:34:29.838 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:29.839 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.3334 ms
2025-06-05 15:34:29.842 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - 204 null null 10.2195ms
2025-06-05 15:34:29.847 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - application/json null
2025-06-05 15:34:29.851 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:29.852 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:29.854 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:29.891 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:29.934 -04:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:29.970 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:34:29.975 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:29.979 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 122.6005ms
2025-06-05 15:34:29.982 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:29.984 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 132.7705 ms
2025-06-05 15:34:29.987 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - 200 null application/json; charset=utf-8 139.8849ms
2025-06-05 15:34:29.994 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-05 15:34:29.999 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:30.000 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.6131 ms
2025-06-05 15:34:30.004 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 10.0267ms
2025-06-05 15:34:30.011 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-05 15:34:30.015 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:30.016 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:30.019 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:30.054 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:30.085 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:30.114 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:34:30.121 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:30.123 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 100.0454ms
2025-06-05 15:34:30.127 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:30.129 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 114.3456 ms
2025-06-05 15:34:30.131 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 120.6894ms
2025-06-05 15:34:39.131 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - null null
2025-06-05 15:34:39.137 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:39.139 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 2.1374 ms
2025-06-05 15:34:39.143 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - 204 null null 11.9812ms
2025-06-05 15:34:39.151 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - application/json null
2025-06-05 15:34:39.166 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:39.170 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:39.174 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:39.211 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:39.239 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:39.274 -04:00 [INF] Executed DbCommand (29ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-05 15:34:39.278 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:39.280 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 103.1477ms
2025-06-05 15:34:39.282 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:39.286 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 119.4806 ms
2025-06-05 15:34:39.290 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - 200 null application/json; charset=utf-8 139.2325ms
2025-06-05 15:34:39.296 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-05 15:34:39.302 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:39.304 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.9498 ms
2025-06-05 15:34:39.309 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 12.3774ms
2025-06-05 15:34:39.315 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-05 15:34:39.322 -04:00 [INF] CORS policy execution successful.
2025-06-05 15:34:39.323 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:39.325 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-05 15:34:39.393 -04:00 [INF] Executed DbCommand (62ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-05 15:34:39.431 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-05 15:34:39.488 -04:00 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-05 15:34:39.513 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-05 15:34:39.518 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 189.3641ms
2025-06-05 15:34:39.521 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-05 15:34:39.525 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 202.9222 ms
2025-06-05 15:34:39.528 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 213.0703ms
