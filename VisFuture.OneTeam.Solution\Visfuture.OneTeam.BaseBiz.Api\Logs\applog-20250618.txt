2025-06-18 13:21:14.159 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-18 13:21:14.358 -04:00 [INF] Now listening on: http://localhost:5275
2025-06-18 13:21:14.402 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-18 13:21:14.404 -04:00 [INF] Hosting environment: Development
2025-06-18 13:21:14.406 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-06-18 13:22:29.242 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-18 13:22:29.345 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:22:29.355 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 69.6409 ms
2025-06-18 13:22:29.377 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 142.7397ms
2025-06-18 13:22:29.397 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:22:29.406 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:22:29.555 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:22:29.607 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:22:32.627 -04:00 [INF] Executed DbCommand (121ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:22:32.702 -04:00 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:22:33.682 -04:00 [INF] Executed DbCommand (135ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:22:33.738 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:22:33.766 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 4148.6977ms
2025-06-18 13:22:33.770 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:22:33.774 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 4370.7639 ms
2025-06-18 13:22:33.795 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 4398.0795ms
2025-06-18 13:22:52.066 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/AddSequenceNo - null null
2025-06-18 13:22:52.074 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:22:52.076 -04:00 [INF] HTTP OPTIONS /AddSequenceNo responded 204 in 2.5210 ms
2025-06-18 13:22:52.080 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/AddSequenceNo - 204 null null 14.1767ms
2025-06-18 13:22:52.086 -04:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5275/AddSequenceNo - application/json 38
2025-06-18 13:22:52.091 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:22:52.095 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.SaveSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:22:52.109 -04:00 [INF] Route matched with {action = "SaveSequenceNo", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] SaveSequenceNo(Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:22:52.168 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:22:52.198 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:22:52.707 -04:00 [INF] Executed DbCommand (54ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime2), @p2='?' (Size = 50), @p3='?' (DbType = Int64), @p4='?' (DbType = Int16), @p5='?' (Size = 255), @p6='?' (Size = 50), @p7='?' (Size = 50), @p8='?' (DbType = Guid), @p9='?' (DbType = DateTime2), @p10='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [SequenceNo] ([Id], [CreateAt], [CreateBy], [CurrentNo], [Length], [Name], [Prefix], [Suffix], [TenantId], [UpdateAt], [UpdateBy])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-06-18 13:22:52.731 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-18 13:22:52.740 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.SaveSequenceNo (Visfuture.OneTeam.BaseBiz.Api) in 624.9805ms
2025-06-18 13:22:52.744 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.SaveSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:22:52.746 -04:00 [INF] HTTP PUT /AddSequenceNo responded 200 in 655.8138 ms
2025-06-18 13:22:52.752 -04:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5275/AddSequenceNo - 200 null application/json; charset=utf-8 665.3955ms
2025-06-18 13:22:52.762 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-18 13:22:52.768 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:22:52.770 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 2.3674 ms
2025-06-18 13:22:52.774 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 11.8891ms
2025-06-18 13:22:52.782 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:22:52.789 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:22:52.796 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:22:52.800 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:22:52.933 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:22:52.962 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:22:53.059 -04:00 [INF] Executed DbCommand (84ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:22:53.069 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:22:53.074 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 268.186ms
2025-06-18 13:22:53.077 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:22:53.079 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 290.5425 ms
2025-06-18 13:22:53.083 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 301.2816ms
2025-06-18 13:22:54.576 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:22:54.583 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:22:54.586 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:22:54.589 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:22:54.622 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:22:54.653 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:22:54.741 -04:00 [INF] Executed DbCommand (81ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:22:54.754 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:22:54.760 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 166.6766ms
2025-06-18 13:22:54.765 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:22:54.766 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 183.4758 ms
2025-06-18 13:22:54.771 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 193.744ms
2025-06-18 13:22:57.057 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:22:57.063 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:22:57.065 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:22:57.070 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:22:57.100 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:22:57.146 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:22:57.264 -04:00 [INF] Executed DbCommand (99ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:22:57.270 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:22:57.273 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 200.5645ms
2025-06-18 13:22:57.276 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:22:57.279 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 215.6041 ms
2025-06-18 13:22:57.282 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 225.2392ms
2025-06-18 13:23:02.086 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/DeleteSequenceNo?SequenceNoId=97a7ce36-e949-4a5f-b74f-9da5640680fd - null null
2025-06-18 13:23:02.094 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:02.096 -04:00 [INF] HTTP OPTIONS /DeleteSequenceNo responded 204 in 2.6308 ms
2025-06-18 13:23:02.100 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/DeleteSequenceNo?SequenceNoId=97a7ce36-e949-4a5f-b74f-9da5640680fd - 204 null null 14.7803ms
2025-06-18 13:23:02.106 -04:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5275/DeleteSequenceNo?SequenceNoId=97a7ce36-e949-4a5f-b74f-9da5640680fd - application/json null
2025-06-18 13:23:02.110 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:02.111 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.DeleteSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:02.123 -04:00 [INF] Route matched with {action = "DeleteSequenceNo", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeleteSequenceNo(System.Guid, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:23:02.191 -04:00 [INF] Executed DbCommand (60ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:23:02.224 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:23:02.349 -04:00 [INF] Executed DbCommand (38ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
DELETE FROM [s]
FROM [SequenceNo] AS [s]
WHERE [s].[Id] = @__id_0
2025-06-18 13:23:02.363 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-18 13:23:02.368 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.DeleteSequenceNo (Visfuture.OneTeam.BaseBiz.Api) in 238.4784ms
2025-06-18 13:23:02.373 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.DeleteSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:02.377 -04:00 [INF] HTTP DELETE /DeleteSequenceNo responded 200 in 267.6614 ms
2025-06-18 13:23:02.382 -04:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5275/DeleteSequenceNo?SequenceNoId=97a7ce36-e949-4a5f-b74f-9da5640680fd - 200 null application/json; charset=utf-8 276.3805ms
2025-06-18 13:23:02.392 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-18 13:23:02.397 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:02.399 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.5919 ms
2025-06-18 13:23:02.402 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 10.0834ms
2025-06-18 13:23:02.408 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:23:02.413 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:02.414 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:02.416 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:23:02.439 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:23:02.469 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:23:02.571 -04:00 [INF] Executed DbCommand (97ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:23:02.576 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:23:02.579 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 160.4822ms
2025-06-18 13:23:02.581 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:02.583 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 170.5786 ms
2025-06-18 13:23:02.587 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 179.0349ms
2025-06-18 13:23:24.813 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/UpdateSequenceNo - null null
2025-06-18 13:23:24.824 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:24.825 -04:00 [INF] HTTP OPTIONS /UpdateSequenceNo responded 204 in 1.0840 ms
2025-06-18 13:23:24.829 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/UpdateSequenceNo - 204 null null 16.4152ms
2025-06-18 13:23:24.843 -04:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5275/UpdateSequenceNo - application/json 262
2025-06-18 13:23:24.848 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:24.850 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:24.864 -04:00 [INF] Route matched with {action = "UpdateSequenceNo", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateSequenceNo(Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:23:24.900 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:23:24.943 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:23:25.024 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@p10='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (Size = 50), @p2='?' (DbType = Int64), @p3='?' (DbType = Int16), @p4='?' (Size = 255), @p5='?' (Size = 50), @p6='?' (Size = 50), @p7='?' (DbType = Guid), @p8='?' (DbType = DateTime2), @p9='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [SequenceNo] SET [CreateAt] = @p0, [CreateBy] = @p1, [CurrentNo] = @p2, [Length] = @p3, [Name] = @p4, [Prefix] = @p5, [Suffix] = @p6, [TenantId] = @p7, [UpdateAt] = @p8, [UpdateBy] = @p9
OUTPUT 1
WHERE [Id] = @p10;
2025-06-18 13:23:25.037 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-18 13:23:25.042 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api) in 168.447ms
2025-06-18 13:23:25.045 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:25.048 -04:00 [INF] HTTP PUT /UpdateSequenceNo responded 200 in 200.2430 ms
2025-06-18 13:23:25.053 -04:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5275/UpdateSequenceNo - 200 null application/json; charset=utf-8 209.848ms
2025-06-18 13:23:25.073 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-18 13:23:25.076 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:25.078 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.2905 ms
2025-06-18 13:23:25.081 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 8.3647ms
2025-06-18 13:23:25.086 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:23:25.090 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:25.091 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:25.093 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:23:25.118 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:23:25.149 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:23:25.257 -04:00 [INF] Executed DbCommand (97ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:23:25.267 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:23:25.272 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 176.3745ms
2025-06-18 13:23:25.275 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:25.277 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 187.3396 ms
2025-06-18 13:23:25.280 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 194.5673ms
2025-06-18 13:23:37.940 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-18 13:23:37.945 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:37.947 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.0276 ms
2025-06-18 13:23:37.951 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 11.9103ms
2025-06-18 13:23:37.970 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-18 13:23:37.975 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:37.977 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:37.984 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:23:38.010 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:23:38.038 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:23:38.305 -04:00 [INF] Executed DbCommand (84ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-18 13:23:38.323 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:23:38.344 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 355.0109ms
2025-06-18 13:23:38.348 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:38.349 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 374.7371 ms
2025-06-18 13:23:38.353 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 383.496ms
2025-06-18 13:23:39.879 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-06-18 13:23:39.885 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:39.887 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:39.888 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:23:39.913 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:23:39.941 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:23:40.084 -04:00 [INF] Executed DbCommand (125ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-06-18 13:23:40.094 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:23:40.100 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 208.9779ms
2025-06-18 13:23:40.103 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:40.106 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 220.3833 ms
2025-06-18 13:23:40.110 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 231.0399ms
2025-06-18 13:23:44.730 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - application/json; charset=utf-8 0
2025-06-18 13:23:44.786 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:44.798 -04:00 [INF] Route matched with {action = "GetSequenceNoByNameExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoByNameExt(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:23:44.837 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:23:44.868 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:23:44.950 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__name_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[CreateAt], [s].[CreateBy], [s].[CurrentNo], [s].[Length], [s].[Name], [s].[Prefix], [s].[Suffix], [s].[TenantId], [s].[UpdateAt], [s].[UpdateBy]
FROM [SequenceNo] AS [s]
WHERE [s].[Name] = @__name_0
2025-06-18 13:23:44.963 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:23:44.969 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api) in 164.9648ms
2025-06-18 13:23:44.974 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:44.976 -04:00 [INF] HTTP GET /External/SequenceNoByName responded 200 in 235.0555 ms
2025-06-18 13:23:44.981 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - 200 null application/json; charset=utf-8 251.0379ms
2025-06-18 13:23:45.169 -04:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5275/External/UpdateSequenceNo - application/json; charset=utf-8 331
2025-06-18 13:23:45.182 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.UpdateSequenceNoExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:45.194 -04:00 [INF] Route matched with {action = "UpdateSequenceNoExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateSequenceNoExt(Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:23:45.220 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:23:45.250 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:23:45.289 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[@p10='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (Size = 50), @p2='?' (DbType = Int64), @p3='?' (DbType = Int16), @p4='?' (Size = 255), @p5='?' (Size = 50), @p6='?' (Size = 50), @p7='?' (DbType = Guid), @p8='?' (DbType = DateTime2), @p9='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [SequenceNo] SET [CreateAt] = @p0, [CreateBy] = @p1, [CurrentNo] = @p2, [Length] = @p3, [Name] = @p4, [Prefix] = @p5, [Suffix] = @p6, [TenantId] = @p7, [UpdateAt] = @p8, [UpdateBy] = @p9
OUTPUT 1
WHERE [Id] = @p10;
2025-06-18 13:23:45.302 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-18 13:23:45.314 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.UpdateSequenceNoExt (Visfuture.OneTeam.BaseBiz.Api) in 115.3221ms
2025-06-18 13:23:45.319 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.UpdateSequenceNoExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:45.327 -04:00 [INF] HTTP PUT /External/UpdateSequenceNo responded 200 in 149.2500 ms
2025-06-18 13:23:45.336 -04:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5275/External/UpdateSequenceNo - 200 null application/json; charset=utf-8 166.3442ms
2025-06-18 13:23:45.997 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-18 13:23:46.003 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:46.007 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 4.9638 ms
2025-06-18 13:23:46.014 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 17.0383ms
2025-06-18 13:23:46.023 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-18 13:23:46.029 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:46.031 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:46.033 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:23:46.060 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:23:46.088 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:23:46.187 -04:00 [INF] Executed DbCommand (85ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-18 13:23:46.195 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:23:46.200 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 162.4941ms
2025-06-18 13:23:46.203 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:46.206 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 176.3490 ms
2025-06-18 13:23:46.209 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 185.1949ms
2025-06-18 13:23:58.508 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-18 13:23:58.513 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:58.514 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.6536 ms
2025-06-18 13:23:58.518 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 9.1994ms
2025-06-18 13:23:58.524 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-18 13:23:58.529 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:23:58.531 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:58.532 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:23:58.557 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:23:58.582 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:23:58.672 -04:00 [INF] Executed DbCommand (82ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-18 13:23:58.682 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:23:58.689 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 153.9079ms
2025-06-18 13:23:58.694 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:23:58.700 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 170.6808 ms
2025-06-18 13:23:58.709 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 185.2177ms
2025-06-18 13:24:04.691 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-18 13:24:04.694 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:04.695 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 0.9717 ms
2025-06-18 13:24:04.697 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 6.1548ms
2025-06-18 13:24:04.702 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:24:04.706 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:04.707 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:04.708 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:24:04.733 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:24:04.762 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:24:04.859 -04:00 [INF] Executed DbCommand (88ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:24:04.866 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:24:04.875 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 163.2241ms
2025-06-18 13:24:04.879 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:04.880 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 174.6646 ms
2025-06-18 13:24:04.883 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 180.8602ms
2025-06-18 13:24:18.075 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/UpdateSequenceNo - null null
2025-06-18 13:24:18.078 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:18.079 -04:00 [INF] HTTP OPTIONS /UpdateSequenceNo responded 204 in 1.1320 ms
2025-06-18 13:24:18.083 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/UpdateSequenceNo - 204 null null 7.8428ms
2025-06-18 13:24:18.090 -04:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5275/UpdateSequenceNo - application/json 262
2025-06-18 13:24:18.095 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:18.097 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:18.099 -04:00 [INF] Route matched with {action = "UpdateSequenceNo", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateSequenceNo(Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:24:18.184 -04:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:24:18.211 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:24:18.241 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@p10='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (Size = 50), @p2='?' (DbType = Int64), @p3='?' (DbType = Int16), @p4='?' (Size = 255), @p5='?' (Size = 50), @p6='?' (Size = 50), @p7='?' (DbType = Guid), @p8='?' (DbType = DateTime2), @p9='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [SequenceNo] SET [CreateAt] = @p0, [CreateBy] = @p1, [CurrentNo] = @p2, [Length] = @p3, [Name] = @p4, [Prefix] = @p5, [Suffix] = @p6, [TenantId] = @p7, [UpdateAt] = @p8, [UpdateBy] = @p9
OUTPUT 1
WHERE [Id] = @p10;
2025-06-18 13:24:18.252 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-18 13:24:18.257 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api) in 154.8684ms
2025-06-18 13:24:18.261 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:18.265 -04:00 [INF] HTTP PUT /UpdateSequenceNo responded 200 in 170.4695 ms
2025-06-18 13:24:18.273 -04:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5275/UpdateSequenceNo - 200 null application/json; charset=utf-8 183.3246ms
2025-06-18 13:24:18.287 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-18 13:24:18.295 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:18.300 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 4.8422 ms
2025-06-18 13:24:18.304 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 17.2312ms
2025-06-18 13:24:18.311 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:24:18.315 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:18.317 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:18.319 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:24:18.344 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:24:18.373 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:24:18.470 -04:00 [INF] Executed DbCommand (89ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:24:18.479 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:24:18.484 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 160.8331ms
2025-06-18 13:24:18.489 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:18.491 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 176.2621 ms
2025-06-18 13:24:18.494 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 183.4327ms
2025-06-18 13:24:27.321 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/UpdateSequenceNo - null null
2025-06-18 13:24:27.326 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:27.328 -04:00 [INF] HTTP OPTIONS /UpdateSequenceNo responded 204 in 1.9955 ms
2025-06-18 13:24:27.331 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/UpdateSequenceNo - 204 null null 10.1795ms
2025-06-18 13:24:27.335 -04:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5275/UpdateSequenceNo - application/json 248
2025-06-18 13:24:27.339 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:27.340 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:27.342 -04:00 [INF] Route matched with {action = "UpdateSequenceNo", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateSequenceNo(Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:24:27.421 -04:00 [INF] Executed DbCommand (72ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:24:27.450 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:24:27.484 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[@p10='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (Size = 50), @p2='?' (DbType = Int64), @p3='?' (DbType = Int16), @p4='?' (Size = 255), @p5='?' (Size = 50), @p6='?' (Size = 50), @p7='?' (DbType = Guid), @p8='?' (DbType = DateTime2), @p9='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [SequenceNo] SET [CreateAt] = @p0, [CreateBy] = @p1, [CurrentNo] = @p2, [Length] = @p3, [Name] = @p4, [Prefix] = @p5, [Suffix] = @p6, [TenantId] = @p7, [UpdateAt] = @p8, [UpdateBy] = @p9
OUTPUT 1
WHERE [Id] = @p10;
2025-06-18 13:24:27.491 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-18 13:24:27.495 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api) in 148.2117ms
2025-06-18 13:24:27.500 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:27.504 -04:00 [INF] HTTP PUT /UpdateSequenceNo responded 200 in 165.8007 ms
2025-06-18 13:24:27.510 -04:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5275/UpdateSequenceNo - 200 null application/json; charset=utf-8 174.2776ms
2025-06-18 13:24:27.516 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-18 13:24:27.519 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:27.521 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.3132 ms
2025-06-18 13:24:27.524 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 8.0998ms
2025-06-18 13:24:27.530 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:24:27.533 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:27.539 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:27.541 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:24:27.566 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:24:27.600 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:24:27.697 -04:00 [INF] Executed DbCommand (89ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:24:27.706 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:24:27.709 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 164.7555ms
2025-06-18 13:24:27.712 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:27.716 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 182.9379 ms
2025-06-18 13:24:27.721 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 191.0129ms
2025-06-18 13:24:29.487 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:24:29.496 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:29.498 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:29.501 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:24:29.550 -04:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:24:29.582 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:24:29.683 -04:00 [INF] Executed DbCommand (91ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:24:29.693 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:24:29.696 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 190.7833ms
2025-06-18 13:24:29.700 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:29.704 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 207.8115 ms
2025-06-18 13:24:29.707 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 220.5791ms
2025-06-18 13:24:31.309 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:24:31.314 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:31.315 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:31.317 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:24:31.340 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:24:31.370 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:24:31.467 -04:00 [INF] Executed DbCommand (89ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:24:31.474 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:24:31.479 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 159.2709ms
2025-06-18 13:24:31.481 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:31.484 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 169.4571 ms
2025-06-18 13:24:31.487 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 177.5832ms
2025-06-18 13:24:37.589 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-18 13:24:37.594 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:37.596 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.4094 ms
2025-06-18 13:24:37.598 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 8.482ms
2025-06-18 13:24:37.603 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:24:37.606 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:37.607 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:37.609 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:24:37.636 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:24:37.662 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:24:37.753 -04:00 [INF] Executed DbCommand (79ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:24:37.760 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:24:37.765 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 151.4736ms
2025-06-18 13:24:37.770 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:37.775 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 168.4790 ms
2025-06-18 13:24:37.782 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 179.0825ms
2025-06-18 13:24:38.967 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:24:38.974 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:24:38.975 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:38.976 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:24:39.000 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:24:39.029 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:24:39.116 -04:00 [INF] Executed DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:24:39.124 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:24:39.128 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 149.2851ms
2025-06-18 13:24:39.132 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:24:39.134 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 160.7110 ms
2025-06-18 13:24:39.138 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 171.1807ms
2025-06-18 13:26:16.620 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-18 13:26:16.629 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:26:16.631 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.4982 ms
2025-06-18 13:26:16.636 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 15.385ms
2025-06-18 13:26:16.651 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-18 13:26:16.655 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:26:16.657 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:16.659 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:26:16.683 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:26:16.712 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:26:16.805 -04:00 [INF] Executed DbCommand (86ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-18 13:26:16.811 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:26:16.813 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 150.2316ms
2025-06-18 13:26:16.816 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:16.817 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 162.0664 ms
2025-06-18 13:26:16.821 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 169.7063ms
2025-06-18 13:26:18.420 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-06-18 13:26:18.427 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:26:18.429 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:18.431 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:26:18.454 -04:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:26:18.482 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:26:18.588 -04:00 [INF] Executed DbCommand (94ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-06-18 13:26:18.598 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:26:18.607 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 172.5656ms
2025-06-18 13:26:18.611 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:18.615 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 187.8870 ms
2025-06-18 13:26:18.624 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 203.6347ms
2025-06-18 13:26:22.133 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - application/json; charset=utf-8 0
2025-06-18 13:26:22.140 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:22.141 -04:00 [INF] Route matched with {action = "GetSequenceNoByNameExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoByNameExt(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:26:22.167 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:26:22.198 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:26:22.232 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__name_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[CreateAt], [s].[CreateBy], [s].[CurrentNo], [s].[Length], [s].[Name], [s].[Prefix], [s].[Suffix], [s].[TenantId], [s].[UpdateAt], [s].[UpdateBy]
FROM [SequenceNo] AS [s]
WHERE [s].[Name] = @__name_0
2025-06-18 13:26:22.245 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:26:22.249 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api) in 104.5485ms
2025-06-18 13:26:22.255 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.GetSequenceNoByNameExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:22.258 -04:00 [INF] HTTP GET /External/SequenceNoByName responded 200 in 119.6855 ms
2025-06-18 13:26:22.263 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/External/SequenceNoByName?name=Product - 200 null application/json; charset=utf-8 129.5168ms
2025-06-18 13:26:22.309 -04:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5275/External/UpdateSequenceNo - application/json; charset=utf-8 333
2025-06-18 13:26:22.320 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.UpdateSequenceNoExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:22.325 -04:00 [INF] Route matched with {action = "UpdateSequenceNoExt", controller = "External"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateSequenceNoExt(Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:26:22.355 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:26:22.387 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:26:22.418 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[@p10='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (Size = 50), @p2='?' (DbType = Int64), @p3='?' (DbType = Int16), @p4='?' (Size = 255), @p5='?' (Size = 50), @p6='?' (Size = 50), @p7='?' (DbType = Guid), @p8='?' (DbType = DateTime2), @p9='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [SequenceNo] SET [CreateAt] = @p0, [CreateBy] = @p1, [CurrentNo] = @p2, [Length] = @p3, [Name] = @p4, [Prefix] = @p5, [Suffix] = @p6, [TenantId] = @p7, [UpdateAt] = @p8, [UpdateBy] = @p9
OUTPUT 1
WHERE [Id] = @p10;
2025-06-18 13:26:22.424 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-18 13:26:22.427 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.UpdateSequenceNoExt (Visfuture.OneTeam.BaseBiz.Api) in 97.1686ms
2025-06-18 13:26:22.431 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.ExternalController.UpdateSequenceNoExt (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:22.435 -04:00 [INF] HTTP PUT /External/UpdateSequenceNo responded 200 in 116.6454 ms
2025-06-18 13:26:22.441 -04:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5275/External/UpdateSequenceNo - 200 null application/json; charset=utf-8 131.6884ms
2025-06-18 13:26:22.608 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-18 13:26:22.613 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:26:22.614 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 1.6790 ms
2025-06-18 13:26:22.618 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 10.4386ms
2025-06-18 13:26:22.624 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-18 13:26:22.628 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:26:22.630 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:22.632 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:26:22.659 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:26:22.689 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:26:22.786 -04:00 [INF] Executed DbCommand (91ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-18 13:26:22.794 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:26:22.798 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 163.752ms
2025-06-18 13:26:22.802 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:22.807 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 178.7437 ms
2025-06-18 13:26:22.813 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 189.2718ms
2025-06-18 13:26:26.057 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-18 13:26:26.065 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:26:26.067 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:26.070 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:26:26.099 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:26:26.130 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:26:26.219 -04:00 [INF] Executed DbCommand (79ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-18 13:26:26.225 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:26:26.228 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 152.8134ms
2025-06-18 13:26:26.231 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:26.233 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 168.4138 ms
2025-06-18 13:26:26.238 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 181.3635ms
2025-06-18 13:26:29.982 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-18 13:26:29.988 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:26:29.989 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.5483 ms
2025-06-18 13:26:29.993 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 11.4066ms
2025-06-18 13:26:30.000 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:26:30.004 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:26:30.006 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:30.008 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:26:30.047 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:26:30.078 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:26:30.169 -04:00 [INF] Executed DbCommand (85ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:26:30.175 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:26:30.179 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 166.4999ms
2025-06-18 13:26:30.181 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:26:30.183 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 178.8786 ms
2025-06-18 13:26:30.186 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 186.5623ms
2025-06-18 13:30:33.161 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-18 13:30:33.340 -04:00 [INF] Now listening on: http://localhost:5275
2025-06-18 13:30:33.383 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-18 13:30:33.385 -04:00 [INF] Hosting environment: Development
2025-06-18 13:30:33.386 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-06-18 13:30:47.197 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-18 13:30:47.294 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:30:47.301 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 58.8317 ms
2025-06-18 13:30:47.325 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 135.8005ms
2025-06-18 13:30:47.340 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:30:47.349 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:30:47.481 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:30:47.518 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:30:49.992 -04:00 [INF] Executed DbCommand (112ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:30:50.053 -04:00 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:30:50.859 -04:00 [INF] Executed DbCommand (118ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:30:50.909 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:30:50.936 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 3408.4938ms
2025-06-18 13:30:50.944 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:30:50.952 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 3604.8950 ms
2025-06-18 13:30:50.977 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 3637.191ms
2025-06-18 13:30:58.344 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/UpdateSequenceNo - null null
2025-06-18 13:30:58.353 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:30:58.356 -04:00 [INF] HTTP OPTIONS /UpdateSequenceNo responded 204 in 3.7530 ms
2025-06-18 13:30:58.359 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/UpdateSequenceNo - 204 null null 14.7446ms
2025-06-18 13:30:58.366 -04:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5275/UpdateSequenceNo - application/json 280
2025-06-18 13:30:58.372 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:30:58.376 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:30:58.385 -04:00 [INF] Route matched with {action = "UpdateSequenceNo", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateSequenceNo(Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:30:58.437 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:30:58.472 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:30:58.844 -04:00 [INF] Executed DbCommand (53ms) [Parameters=[@p10='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (Size = 50), @p2='?' (DbType = Int64), @p3='?' (DbType = Int16), @p4='?' (Size = 255), @p5='?' (Size = 50), @p6='?' (Size = 50), @p7='?' (DbType = Guid), @p8='?' (DbType = DateTime2), @p9='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [SequenceNo] SET [CreateAt] = @p0, [CreateBy] = @p1, [CurrentNo] = @p2, [Length] = @p3, [Name] = @p4, [Prefix] = @p5, [Suffix] = @p6, [TenantId] = @p7, [UpdateAt] = @p8, [UpdateBy] = @p9
OUTPUT 1
WHERE [Id] = @p10;
2025-06-18 13:30:58.870 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-18 13:30:58.880 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api) in 490.1784ms
2025-06-18 13:30:58.883 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:30:58.885 -04:00 [INF] HTTP PUT /UpdateSequenceNo responded 200 in 513.9957 ms
2025-06-18 13:30:58.889 -04:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5275/UpdateSequenceNo - 200 null application/json; charset=utf-8 522.8858ms
2025-06-18 13:30:58.894 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-18 13:30:58.899 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:30:58.900 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 1.4029 ms
2025-06-18 13:30:58.904 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 9.3148ms
2025-06-18 13:30:58.908 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:30:58.912 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:30:58.917 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:30:58.919 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:30:59.041 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:30:59.069 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:30:59.162 -04:00 [INF] Executed DbCommand (81ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:30:59.169 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:30:59.172 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 249.7531ms
2025-06-18 13:30:59.175 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:30:59.178 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 265.9110 ms
2025-06-18 13:30:59.181 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 273.0408ms
2025-06-18 13:31:38.581 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/UpdateSequenceNo - null null
2025-06-18 13:31:38.594 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:31:38.596 -04:00 [INF] HTTP OPTIONS /UpdateSequenceNo responded 204 in 1.5594 ms
2025-06-18 13:31:38.600 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/UpdateSequenceNo - 204 null null 19.0719ms
2025-06-18 13:31:38.606 -04:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5275/UpdateSequenceNo - application/json 279
2025-06-18 13:31:38.611 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:31:38.613 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:31:38.615 -04:00 [INF] Route matched with {action = "UpdateSequenceNo", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateSequenceNo(Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:31:38.647 -04:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:31:38.675 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:31:38.706 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[@p10='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (Size = 50), @p2='?' (DbType = Int64), @p3='?' (DbType = Int16), @p4='?' (Size = 255), @p5='?' (Size = 50), @p6='?' (Size = 50), @p7='?' (DbType = Guid), @p8='?' (DbType = DateTime2), @p9='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [SequenceNo] SET [CreateAt] = @p0, [CreateBy] = @p1, [CurrentNo] = @p2, [Length] = @p3, [Name] = @p4, [Prefix] = @p5, [Suffix] = @p6, [TenantId] = @p7, [UpdateAt] = @p8, [UpdateBy] = @p9
OUTPUT 1
WHERE [Id] = @p10;
2025-06-18 13:31:38.718 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-18 13:31:38.725 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api) in 105.5997ms
2025-06-18 13:31:38.732 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.UpdateSequenceNo (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:31:38.734 -04:00 [INF] HTTP PUT /UpdateSequenceNo responded 200 in 122.8484 ms
2025-06-18 13:31:38.739 -04:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5275/UpdateSequenceNo - 200 null application/json; charset=utf-8 132.5998ms
2025-06-18 13:31:38.766 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - null null
2025-06-18 13:31:38.784 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:31:38.786 -04:00 [INF] HTTP OPTIONS /SequenceNoList responded 204 in 2.1747 ms
2025-06-18 13:31:38.791 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/SequenceNoList - 204 null null 24.4144ms
2025-06-18 13:31:38.803 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/SequenceNoList - application/json 95
2025-06-18 13:31:38.807 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:31:38.809 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:31:38.813 -04:00 [INF] Route matched with {action = "GetSequenceNoList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSequenceNoList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.SequenceNoDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:31:38.848 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:31:38.877 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:31:38.974 -04:00 [INF] Executed DbCommand (92ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Name], [s].[Prefix], [s].[Suffix], [s].[CurrentNo], [s].[Length], [s].[TenantId], [s].[Id], [s].[CreateBy], [s].[CreateAt], [s].[UpdateBy], [s].[UpdateAt]
FROM [SequenceNo] AS [s]
2025-06-18 13:31:38.979 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.SequenceNoListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:31:38.981 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api) in 155.5646ms
2025-06-18 13:31:38.985 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetSequenceNoList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:31:38.987 -04:00 [INF] HTTP POST /SequenceNoList responded 200 in 179.7282 ms
2025-06-18 13:31:38.990 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/SequenceNoList - 200 null application/json; charset=utf-8 187.0946ms
2025-06-18 13:37:16.103 -04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-18 13:37:16.338 -04:00 [INF] Now listening on: http://localhost:5275
2025-06-18 13:37:16.390 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-18 13:37:16.391 -04:00 [INF] Hosting environment: Development
2025-06-18 13:37:16.393 -04:00 [INF] Content root path: D:\Repo\One team\VisFuture.OneTeam.Solution\Visfuture.OneTeam.BaseBiz.Api
2025-06-18 13:37:31.910 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - null null
2025-06-18 13:37:32.006 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:32.014 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 58.9599 ms
2025-06-18 13:37:32.035 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - 204 null null 130.7866ms
2025-06-18 13:37:32.044 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - application/json null
2025-06-18 13:37:32.053 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:32.151 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:32.188 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:34.711 -04:00 [INF] Executed DbCommand (194ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:34.846 -04:00 [INF] Executed DbCommand (108ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:35.461 -04:00 [INF] Executed DbCommand (142ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-18 13:37:35.605 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:35.656 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 3458.6862ms
2025-06-18 13:37:35.661 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:35.665 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 3614.0395 ms
2025-06-18 13:37:35.683 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - 200 null application/json; charset=utf-8 3639.317ms
2025-06-18 13:37:35.689 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-18 13:37:35.695 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:35.697 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 2.0616 ms
2025-06-18 13:37:35.700 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 10.7326ms
2025-06-18 13:37:35.706 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 91
2025-06-18 13:37:35.713 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:35.716 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:35.726 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:35.851 -04:00 [INF] Executed DbCommand (100ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:35.891 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:36.059 -04:00 [INF] Executed DbCommand (69ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-18 13:37:36.089 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:36.109 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 379.4793ms
2025-06-18 13:37:36.113 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:36.115 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 402.6791 ms
2025-06-18 13:37:36.121 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 414.0957ms
2025-06-18 13:37:36.130 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - null null
2025-06-18 13:37:36.137 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:36.140 -04:00 [INF] HTTP OPTIONS /EmployeeList responded 204 in 2.4112 ms
2025-06-18 13:37:36.143 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/EmployeeList - 204 null null 12.253ms
2025-06-18 13:37:36.149 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 129
2025-06-18 13:37:36.155 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:36.159 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:36.167 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:36.303 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:36.448 -04:00 [INF] Executed DbCommand (139ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:36.737 -04:00 [INF] Executed DbCommand (130ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-18 13:37:36.748 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:36.766 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 594.6031ms
2025-06-18 13:37:36.772 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:36.775 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 620.8423 ms
2025-06-18 13:37:36.782 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 633.0032ms
2025-06-18 13:37:36.792 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - null null
2025-06-18 13:37:36.798 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:36.799 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.5424 ms
2025-06-18 13:37:36.802 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - 204 null null 9.6183ms
2025-06-18 13:37:36.809 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - application/json null
2025-06-18 13:37:36.813 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:36.814 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:36.817 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:36.877 -04:00 [INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:36.939 -04:00 [INF] Executed DbCommand (54ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:36.995 -04:00 [INF] Executed DbCommand (45ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-18 13:37:37.003 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:37.006 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 185.4142ms
2025-06-18 13:37:37.008 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:37.010 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 197.3917 ms
2025-06-18 13:37:37.014 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - 200 null application/json; charset=utf-8 205.5503ms
2025-06-18 13:37:37.020 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 91
2025-06-18 13:37:37.022 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:37.023 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:37.025 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:37.050 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:37.081 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:37.116 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-18 13:37:37.131 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:37.136 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 108.5656ms
2025-06-18 13:37:37.140 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:37.142 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 119.7479 ms
2025-06-18 13:37:37.145 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 125.4885ms
2025-06-18 13:37:38.575 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - null null
2025-06-18 13:37:38.577 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - application/json null
2025-06-18 13:37:38.594 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 18
2025-06-18 13:37:38.594 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 51
2025-06-18 13:37:38.583 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:38.604 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:38.608 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:38.612 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:38.614 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 30.9963 ms
2025-06-18 13:37:38.616 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:38.618 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:38.642 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:38.619 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:38.622 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - 204 null null 47.148ms
2025-06-18 13:37:38.648 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:38.653 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:38.658 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - application/json null
2025-06-18 13:37:38.672 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:38.673 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:38.675 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:38.677 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:38.717 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:38.756 -04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:38.792 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:38.828 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:38.846 -04:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:38.879 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:38.879 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-18 13:37:38.899 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:38.906 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 254.5348ms
2025-06-18 13:37:38.911 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:38.925 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 321.6210 ms
2025-06-18 13:37:38.925 -04:00 [INF] Executed DbCommand (33ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-18 13:37:38.929 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectStatus - 200 null application/json; charset=utf-8 351.8187ms
2025-06-18 13:37:38.933 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
2025-06-18 13:37:38.935 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:38.940 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-18 13:37:38.944 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:38.946 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 267.7277ms
2025-06-18 13:37:38.949 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:38.954 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:38.954 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 293.4113ms
2025-06-18 13:37:38.955 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:38.957 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 285.0348 ms
2025-06-18 13:37:38.958 -04:00 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:38.959 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:38.961 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:38.964 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=ProjectType - 200 null application/json; charset=utf-8 305.9942ms
2025-06-18 13:37:38.969 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 360.7896 ms
2025-06-18 13:37:38.979 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-18 13:37:38.981 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 387.6295ms
2025-06-18 13:37:38.988 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:38.994 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-18 13:37:38.997 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:39.002 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:39.003 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:39.007 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 342.5458ms
2025-06-18 13:37:39.007 -04:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:39.014 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:39.020 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 407.5376 ms
2025-06-18 13:37:39.023 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 429.4519ms
2025-06-18 13:37:39.038 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:39.046 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:39.065 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:39.074 -04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-18 13:37:39.094 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:39.097 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 125.8493ms
2025-06-18 13:37:39.097 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/RoleList - null null
2025-06-18 13:37:39.099 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:39.100 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-18 13:37:39.103 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:39.105 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 155.3973 ms
2025-06-18 13:37:39.109 -04:00 [INF] HTTP OPTIONS /RoleList responded 204 in 6.0997 ms
2025-06-18 13:37:39.112 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 172.4267ms
2025-06-18 13:37:39.115 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/RoleList - 204 null null 17.8774ms
2025-06-18 13:37:39.127 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:39.132 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/RoleList - application/json 90
2025-06-18 13:37:39.139 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 129.6057ms
2025-06-18 13:37:39.140 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:39.142 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:39.144 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:39.145 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 157.0215 ms
2025-06-18 13:37:39.149 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 170.106ms
2025-06-18 13:37:39.152 -04:00 [INF] Route matched with {action = "QueryRoleList", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] QueryRoleListAsync(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.RoleDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:39.234 -04:00 [INF] Executed DbCommand (75ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:39.260 -04:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:39.750 -04:00 [INF] Executed DbCommand (64ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Name], [r].[Description], [r].[IsActive], [r].[Usage], [r].[Code], [r].[TenantId], [r].[Id], [r].[CreateBy], [r].[CreateAt], [r].[UpdateBy], [r].[UpdateAt]
FROM [Role] AS [r]
WHERE [r].[Id] IN (
    SELECT [r0].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r0]
)
2025-06-18 13:37:39.757 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.RoleListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:39.765 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleListAsync (Visfuture.OneTeam.BaseBiz.Api) in 608.2851ms
2025-06-18 13:37:39.768 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.RoleController.QueryRoleListAsync (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:39.771 -04:00 [INF] HTTP POST /RoleList responded 200 in 631.2012 ms
2025-06-18 13:37:39.910 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/RoleList - 200 null application/json; charset=utf-8 778.5869ms
2025-06-18 13:37:39.916 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/EmployeeList - application/json 90
2025-06-18 13:37:39.921 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:39.922 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:39.924 -04:00 [INF] Route matched with {action = "GetEmployeeList", controller = "Org"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetEmployeeList(Visfuture.OneTeam.Core.Common.Base.Models.BaseQuery`1[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.EmployeeDto], System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:39.962 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:39.992 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:40.096 -04:00 [INF] Executed DbCommand (98ms) [Parameters=[@__request_IdList_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [e].[Name], [e].[MainType], [e].[SubType], [e].[Description], [e].[IsActive], [e].[PositionId], [e].[JobTitle], [e].[BusinessEmail], [e].[WorkScheduleId], [e].[HireDate], [e].[TerminateDate], [e].[EmployeeStatus], [e].[UserAccountId], [e].[IsTenantAdmin], [e].[Code], [e].[TenantId], [e].[Id], [e].[CreateBy], [e].[CreateAt], [e].[UpdateBy], [e].[UpdateAt]
FROM [Employee] AS [e]
WHERE [e].[Id] IN (
    SELECT [r].[value]
    FROM OPENJSON(@__request_IdList_0) WITH ([value] uniqueidentifier '$') AS [r]
)
2025-06-18 13:37:40.101 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.EmployeeListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:40.105 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api) in 177.9879ms
2025-06-18 13:37:40.108 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.OrgController.GetEmployeeList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:40.110 -04:00 [INF] HTTP POST /EmployeeList responded 200 in 189.5603 ms
2025-06-18 13:37:40.116 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/EmployeeList - 200 null application/json; charset=utf-8 199.5713ms
2025-06-18 13:37:40.124 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - null null
2025-06-18 13:37:40.129 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:40.130 -04:00 [INF] HTTP OPTIONS /CodeTypeByTypeCode responded 204 in 1.6475 ms
2025-06-18 13:37:40.133 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - 204 null null 9.1933ms
2025-06-18 13:37:40.139 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - application/json null
2025-06-18 13:37:40.143 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:40.145 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:40.147 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:40.171 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:40.199 -04:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:40.226 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-18 13:37:40.231 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:40.233 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 83.0279ms
2025-06-18 13:37:40.236 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:40.238 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 94.2117 ms
2025-06-18 13:37:40.241 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - 200 null application/json; charset=utf-8 101.8976ms
2025-06-18 13:37:40.246 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 87
2025-06-18 13:37:40.251 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:40.253 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:40.255 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:40.280 -04:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:40.324 -04:00 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:40.360 -04:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-18 13:37:40.368 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:40.371 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 112.974ms
2025-06-18 13:37:40.373 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:40.374 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 123.1231 ms
2025-06-18 13:37:40.377 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 130.8099ms
2025-06-18 13:37:43.427 -04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - application/json null
2025-06-18 13:37:43.432 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:43.434 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:43.435 -04:00 [INF] Route matched with {action = "GetCodeTypeByTypeCode", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeTypeByTypeCode(System.String, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:43.461 -04:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:43.492 -04:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:43.523 -04:00 [INF] Executed DbCommand (27ms) [Parameters=[@__typeCode_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[CreateAt], [c].[CreateBy], [c].[Description], [c].[MainType], [c].[Name], [c].[SubType], [c].[SuperiorId], [c].[TenantId], [c].[TypeCode], [c].[UpdateAt], [c].[UpdateBy]
FROM [CodeType] AS [c]
WHERE [c].[TypeCode] = @__typeCode_0
2025-06-18 13:37:43.529 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponse`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs.CodeTypeDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:43.533 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api) in 95.0418ms
2025-06-18 13:37:43.536 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeTypeByTypeCode (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:43.539 -04:00 [INF] HTTP GET /CodeTypeByTypeCode responded 200 in 107.3118 ms
2025-06-18 13:37:43.544 -04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5275/CodeTypeByTypeCode?typeCode=Currency - 200 null application/json; charset=utf-8 116.5112ms
2025-06-18 13:37:43.552 -04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - null null
2025-06-18 13:37:43.558 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:43.559 -04:00 [INF] HTTP OPTIONS /CodeItemList responded 204 in 1.3938 ms
2025-06-18 13:37:43.562 -04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5275/CodeItemList - 204 null null 10.6283ms
2025-06-18 13:37:43.568 -04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5275/CodeItemList - application/json 71
2025-06-18 13:37:43.574 -04:00 [INF] CORS policy execution successful.
2025-06-18 13:37:43.576 -04:00 [INF] Executing endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:43.583 -04:00 [INF] Route matched with {action = "GetCodeItemList", controller = "Misc"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCodeItemList(Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests.CodeItemRequest, System.Threading.CancellationToken) on controller Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController (Visfuture.OneTeam.BaseBiz.Api).
2025-06-18 13:37:43.622 -04:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-18 13:37:43.657 -04:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-18 13:37:43.690 -04:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CodeTypeId], [c].[Name], [c].[Value], [c].[SeqNo], [c].[SuperiorId], [c].[ItemField1], [c].[ItemField2], [c].[ItemField3], [c].[TenantId], [c].[Id], [c].[CreateBy], [c].[CreateAt], [c].[UpdateBy], [c].[UpdateAt]
FROM [CodeItems] AS [c]
2025-06-18 13:37:43.702 -04:00 [INF] Executing OkObjectResult, writing value of type 'Visfuture.OneTeam.Core.Common.Base.Models.EntityResponsePaged`1[[Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos.CodeItemListItemDto, Visfuture.OneTeam.BaseBiz.BusinessLogic, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-18 13:37:43.705 -04:00 [INF] Executed action Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api) in 118.149ms
2025-06-18 13:37:43.707 -04:00 [INF] Executed endpoint 'Visfuture.OneTeam.BaseBiz.Api.Controllers.MiscController.GetCodeItemList (Visfuture.OneTeam.BaseBiz.Api)'
2025-06-18 13:37:43.709 -04:00 [INF] HTTP POST /CodeItemList responded 200 in 134.8467 ms
2025-06-18 13:37:43.712 -04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5275/CodeItemList - 200 null application/json; charset=utf-8 144.7154ms
