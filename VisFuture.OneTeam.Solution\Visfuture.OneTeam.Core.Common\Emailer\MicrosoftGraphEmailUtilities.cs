﻿using Azure.Identity;
using Microsoft.Graph;
using Microsoft.Graph.Users.Item.MailFolders.Item.Messages.Item.Reply;

namespace Visfuture.OneTeam.Core.Common.Emailer
{
    public static class MicrosoftGraphEmailUtilities
    {
        public static async Task SendReply(
            string tenantId,
            string clientId,
            string clientSecret,
            string userEmailAddress,
            string emailId,
            string emailReplyBody
            )
        {
            var credentials = new ClientSecretCredential(
                tenantId,
                clientId,
                clientSecret,
                new TokenCredentialOptions { AuthorityHost = AzureAuthorityHosts.AzurePublicCloud }
                );
            var graphServiceClient = new GraphServiceClient(credentials);
            var user = graphServiceClient.Users[userEmailAddress];
            var messagesRequest = user.MailFolders["Inbox"].Messages;

            var messageToReplyTo = messagesRequest[emailId];
            var originalMessage = await messageToReplyTo.GetAsync();

            var replyRequest = new ReplyPostRequestBody();
            replyRequest.Message = originalMessage;
            replyRequest.Comment = emailReplyBody;

            await messageToReplyTo.Reply.PostAsync(replyRequest);
        }
    }
}
