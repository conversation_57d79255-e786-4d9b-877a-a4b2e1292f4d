﻿using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;

public interface IAccountService
{
    Task<EntityResponse<UserJwtDto>> AuthenticateAsync(UserLoginRequest request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<List<TenantDto>>> GetUserTenants();

    //EntityResponse<IAuthorizeKeyDto> AuthorizeRequest(BaseRequest request);

    //EntityResponse<UserJwtDto> AuthorizeJWT(UserJwtDto key); // return refresh token if jwt expires

    EntityResponse Deauthenticate(Guid userId);

    EntityResponse<UserJwtDto> RefreshToken(Guid userId, string tokenStr);

    Task<EntityResponse> SetTenant(GuidRequest tenantId, CancellationToken cancellationToken = default);

    //EntityResponse<UserJwtUtils> SignUp(UserSignUpRequest model);

    //bool ValidateUserToken(Guid userId, string refreshToken);

    //bool ValidateAPIToken(ApiAuthorizeType apiType, string token);

    //Task<EntityResponse> GetPhoneNoByEmailAsync(string email);

    //Task<EntityResponse> SendVerificationCodeAsync(VerificationCodeRequest model);

    //Task<EntityResponse> SetUserPasswordAsync(UserPasswordRequest model);

    //Task<EntityResponse> ChangeUserPasswordAsync(ChangePasswordRequest model);

    //Task<EntityResponse> ResetPasswordAsync(ResetPasswordRequest model);

    //Task<EntityResponse> RequireResetPasswordAsync(RequireResetPasswordRequest model);
}