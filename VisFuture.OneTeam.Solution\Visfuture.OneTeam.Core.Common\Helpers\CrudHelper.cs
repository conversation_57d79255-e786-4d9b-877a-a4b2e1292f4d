﻿using System.Linq.Expressions;
using System.Reflection;
using ClosedXML.Excel;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json.Linq;
using Visfuture.OneTeam.Core.Common.Base.Manager;
using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Core.Common.Extensions;

namespace Visfuture.OneTeam.Core.Common.Helpers;

/// <summary>
/// Provides CRUD operations for entities of type <typeparamref name="T"/>.
/// </summary>
/// <typeparam name="T">The entity type.</typeparam>
/// <typeparam name="TDto">The DTO type for the entity.</typeparam>
/// <typeparam name="TListItemDto">The list item DTO type for the entity.</typeparam>
public class CrudHelper<T, TDto, TListItemDto>(
    IConfiguration configuration,
    IHttpClientFactory httpClientFactory,
    DbContext DB,
    DbSet<T> dbSet) where T : BaseEntity
    where TDto : BaseDto
    where TListItemDto : BaseDto
{
    /// <summary>
    /// Retrieves an entity by its ID.
    /// </summary>
    /// <param name="id">The ID of the entity.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An <see cref="EntityResponse{TDto}"/> containing the entity or an error message.</returns>
    public async Task<EntityResponse<TDto>> GetEntity(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            T entity = await dbSet.AsNoTracking().FirstOrDefaultAsync(t => t.Id == id, cancellationToken) ??
                       throw new Exception("Entity not found!");
            return EntityResponse<TDto>.Success(entity.Adapt<TDto>());
        }
        catch (Exception ex)
        {
            return EntityResponse<TDto>.Failed(ex.Message);
        }
    }

    /// <summary>
    /// Retrieves an entity that matches the specified condition.
    /// </summary>
    /// <param name="lambda">The condition to match.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An <see cref="EntityResponse{TDto}"/> containing the entity or an error message.</returns>
    public async Task<EntityResponse<TDto>> GetEntityBy(Expression<Func<T, bool>> lambda,
        CancellationToken cancellationToken)
    {
        try
        {
            T entity = await dbSet.AsNoTracking().FirstOrDefaultAsync(lambda, cancellationToken) ??
                       throw new Exception("Entity not found!");
            return EntityResponse<TDto>.Success(entity.Adapt<TDto>());
        }
        catch (Exception ex)
        {
            return EntityResponse<TDto>.Failed(ex.Message);
        }
    }

    /// <summary>
    /// Add metadata and sequence number to the entity before adding it to the database.
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    private async Task<bool> PreAdd(T entity, Operator user)
    {
        DateTime dateTime = BaseManager.CurrentDateTime;
        entity.Id = Guid.NewGuid();
        if (entity is TenantEntity tenantEntity) tenantEntity.TenantId = user.TenantId;
        entity.CreateAt = dateTime;
        entity.CreateBy = user.EmployeeId;
        entity.UpdateAt = dateTime;
        entity.UpdateBy = user.EmployeeId;

        // create code from sequence number, retrieve from BaseBiz if necessary
        if (entity is not CodeEntity codeEntity) return true;
        HttpContent response =
            await ExternalRequestUtils.Send(httpClientFactory,
                configuration["BackendsList:BaseBiz"]! + "/External/SequenceNoByName?name=" + typeof(T).Name,
                "GET", "");
        JToken json = JObject.Parse(await response.ReadAsStringAsync())["entity"]!;
        json["currentNo"] = int.Parse(json["currentNo"]!.ToString()) + 1;
        await ExternalRequestUtils.Send(httpClientFactory,
            configuration["BackendsList:BaseBiz"]! + "/External/UpdateSequenceNo", "PUT",
            json.ToString());

        // ex: ROL0001
        string numPart = json["currentNo"]!.ToString().PadLeft(int.Parse(json["length"]!.ToString()), '0');
        codeEntity.Code = json["prefix"]! + numPart + json["suffix"]!;

        return true;
    }


    /// <summary>
    /// Adds a new entity to the database.
    /// </summary>
    /// <param name="user">The operator performing the action.</param>
    /// <param name="dto">The DTO representing the entity to add.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An <see cref="EntityResponse{Guid}"/> containing the ID of the added entity or an error message.</returns>
    public async Task<EntityResponse<Guid>> AddEntity(Operator user, TDto dto, CancellationToken cancellationToken)
    {
        try
        {
            T entity = dto.Adapt<T>();
            if (!await PreAdd(entity, user)) throw new Exception("Could not write entity metadata or sequence number!");
            dbSet.Add(entity);
            await DB.SaveChangesAsync(cancellationToken);
            return EntityResponse<Guid>.Success(entity.Id);
        }
        catch (Exception ex)
        {
            return EntityResponse<Guid>.Failed(ex.Message);
        }
    }

    /// <summary>
    /// Adds multiple entities to the database.
    /// </summary>
    /// <param name="user">The operator performing the action.</param>
    /// <param name="request">The request containing the list of DTOs to add.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An <see cref="EntityResponse"/> indicating success or failure.</returns>
    public async Task<EntityResponse> AddEntities(Operator user, BaseQuery request, CancellationToken cancellationToken)
    {
        try
        {
            IEnumerable<Task<T>> mapped = request.DtoList!.Select(async dto =>
            {
                T entity = dto.Adapt<T>();
                if (!await PreAdd(entity, user))
                    throw new Exception("Could not write entity metadata or sequence number!");
                return entity;
            });
            await dbSet.AddRangeAsync(await Task.WhenAll(mapped));

            await DB.SaveChangesAsync(cancellationToken);
            return EntityResponse.Success();
        }
        catch (Exception ex)
        {
            return EntityResponse.Failed(ex.Message);
        }
    }

    /// <summary>
    /// Updates an existing entity in the database.
    /// </summary>
    /// <param name="user">The operator performing the action.</param>
    /// <param name="dto">The DTO representing the entity to update.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An <see cref="EntityResponse{Guid}"/> containing the ID of the updated entity or an error message.</returns>
    public async Task<EntityResponse<Guid>> UpdateEntity(Operator user, TDto dto, CancellationToken cancellationToken)
    {
        try
        {
            T entity = dto.Adapt<T>();
            DateTime dateTime = BaseManager.CurrentDateTime;

            entity.UpdateAt = dateTime;
            entity.UpdateBy = user.EmployeeId;
            dbSet.Update(entity);
            await DB.SaveChangesAsync(cancellationToken);
            return EntityResponse<Guid>.Success(entity.Id);
        }
        catch (Exception ex)
        {
            return EntityResponse<Guid>.Failed(ex.Message);
        }
    }

    /// <summary>
    /// Updates multiple entities in the database.
    /// </summary>
    /// <param name="user">The operator performing the action.</param>
    /// <param name="request">The request containing the list of DTOs to update.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An <see cref="EntityResponse"/> indicating success or failure.</returns>
    public async Task<EntityResponse> UpdateEntities(Operator user, BaseQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            IQueryable<T> query = dbSet.AsQueryable();
            if (request.DtoList != null)
            {
                // update all entities in dto list
                dbSet.UpdateRange(request.DtoList!.Select(dto =>
                {
                    T entity = dto.Adapt<T>();
                    DateTime dateTime = BaseManager.CurrentDateTime;

                    entity.UpdateAt = dateTime;
                    entity.UpdateBy = user.EmployeeId;

                    return entity;
                }));
            }
            else // if dto list not provided
            {
                if (request.IdList is { Count: > 0 }) // select entities in id list
                    query = query.Where(t => request.IdList.Contains(t.Id));
                List<T> entities = await query.ToListAsync(cancellationToken);
                if (request.NoList is { Count: > 0 }) // select entities in no (code) list
                {
                    if (!typeof(T).IsSubclassOf(typeof(CodeEntity)))
                        throw new Exception("Property \"Code\" not found in Dto");
                    entities = [.. entities.Where(t => request.NoList.Contains((t as CodeEntity)!.Code))];
                }

                if (request.FieldValues != null) // mass edit using field values
                {
                    PropertyInfo[] properties = [.. typeof(T).GetProperties()];
                    foreach (KeyValuePair<string, HashSet<string?>> tuple in request.FieldValues)
                    {
                        PropertyInfo? prop = properties.FirstOrDefault(p =>
                            p.Name.Equals(tuple.Key, StringComparison.OrdinalIgnoreCase));
                        if (prop == null) continue;

                        // set field to first value in set (should probably be one value)
                        entities =
                        [
                            .. entities.Select(entity =>
                            {
                                prop.SetValue(entity, tuple.Value.First());
                                DateTime dateTime = BaseManager.CurrentDateTime;

                                entity.UpdateAt = dateTime;
                                entity.UpdateBy = user.EmployeeId;
                                return entity;
                            })
                        ];
                    }
                }

                dbSet.UpdateRange(entities);
            }

            await DB.SaveChangesAsync(cancellationToken);
            return EntityResponse.Success();
        }
        catch (Exception ex)
        {
            return EntityResponse.Failed(ex.Message);
        }
    }
    /// <summary>
    /// Deletes an entity by its ID.
    /// </summary>
    /// <param name="id">The ID of the entity to delete.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An <see cref="EntityResponse{Guid}"/> containing the ID of the deleted entity or an error message.</returns>
    public async Task<EntityResponse<Guid>> DeleteEntity(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            await dbSet.Where(t => t.Id == id).ExecuteDeleteAsync(cancellationToken);
            return EntityResponse<Guid>.Success(id);
        }
        catch (Exception ex)
        {
            return EntityResponse<Guid>.Failed(ex.Message);
        }
    }

    /// <summary>
    /// Deletes multiple entities based on the provided query.
    /// </summary>
    /// <param name="request">The query specifying the entities to delete.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An <see cref="EntityResponse"/> indicating success or failure.</returns>
    public async Task<EntityResponse> DeleteEntities(BaseQuery request, CancellationToken cancellationToken)
    {
        try
        {
            IQueryable<T> query = dbSet.AsQueryable();
            if (request.IdList is { Count: > 0 }) // delete all in id list
                query = query.Where(t => request.IdList.Contains(t.Id));
            List<T> entities = await query.ToListAsync(cancellationToken);
            if (request.NoList is { Count: > 0 }) // delete all in no (code) list
            {
                if (!typeof(T).IsSubclassOf(typeof(CodeEntity)))
                    throw new Exception("Property \"Code\" not found in Dto");
                entities = [.. entities.Where(t => request.NoList.Contains((t as CodeEntity)!.Code))];
            }

            if (request.FieldValues != null) // delete all with entities with values for certain fields
            {
                PropertyInfo[] properties = typeof(T).GetProperties().ToArray();
                foreach (KeyValuePair<string, HashSet<string?>> filter in request.FieldValues)
                {
                    PropertyInfo? prop = properties.FirstOrDefault(p =>
                        p.Name.Equals(filter.Key, StringComparison.OrdinalIgnoreCase));
                    if (prop == null) continue;
                    entities =
                    [
                        .. entities.Where(entity =>
                        {
                            object? obj = prop.GetValue(entity);
                            if (obj == null) return filter.Value.Count() == 0;
                            return filter.Value.Contains(obj.ToString()!);
                        })
                    ];
                }
            }

            dbSet.RemoveRange(entities);
            await DB.SaveChangesAsync(cancellationToken);
            return EntityResponse.Success();
        }
        catch (Exception ex)
        {
            return EntityResponse.Failed(ex.Message);
        }
    }

    /// <summary>
    /// Retrieves a paginated list of entities based on the provided query.
    /// </summary>
    /// <param name="request">The query specifying the pagination and filtering criteria.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <param name="exactMatch">An optional expression for exact matching.</param>
    /// <param name="queryEdit">An optional function to modify the query.</param>
    /// <returns>An <see cref="EntityResponsePaged{TListItemDto}"/> containing the paginated list of entities or an error message.</returns>
    public async Task<EntityResponsePaged<TListItemDto>> GetEntityPaged(BaseQuery request,
        CancellationToken cancellationToken, Expression<Func<T, bool>>? exactMatch = null,
        Func<IQueryable<T>, IQueryable<T>>? queryEdit = null)
    {
        try
        {
            IQueryable<T> query = dbSet.AsNoTracking(); // no tracking for read-only operations
            if (request.UniqueFromField != null) // get unique values from a column
            {
                PropertyInfo prop = typeof(T).GetProperties()
                                        .FirstOrDefault(p => p.Name.Equals(request.UniqueFromField,
                                            StringComparison.OrdinalIgnoreCase)) ??
                                    throw new Exception("Property not found in ListDto");
                IEnumerable<string?> uniqueValues = (await query.ToListAsync(cancellationToken))
                    .Select(e => prop.GetValue(e)?.ToString()).Distinct();
                string?[] enumerable = ((IEnumerable<string?>)[.. uniqueValues]).ToArray();
                return new EntityResponsePaged<TListItemDto>
                {
                    UniqueValues = [.. enumerable],
                    Total = enumerable.Length
                };
            }

            if (request.IdList is { Count: > 0 }) // select entities in id list
                query = query.Where(t => request.IdList.Contains(t.Id));
            if (exactMatch != null) query = query.Where(exactMatch);
            if (queryEdit != null) query = queryEdit.Invoke(query);

            List<TListItemDto> entities = await query.ToListAsync<T, TListItemDto>(cancellationToken);

            if (request.NoList is { Count: > 0 }) // select entities in no (code) list
            {
                if (!typeof(TListItemDto).IsSubclassOf(typeof(CodeDto)))
                    throw new Exception("Property \"Code\" not found in Dto");
                entities = [.. entities.Where(t => request.NoList.Contains((t as CodeDto)!.Code))];
            }

            return Paginate(request, Filter(request, entities));
        }
        catch (Exception ex)
        {
            return new EntityResponsePaged<TListItemDto> { Message = ex.Message, IsSuccess = false };
        }
    }

    /// <summary>
    /// Filters a list of entities based on the provided query.
    /// </summary>
    /// <param name="request"></param>
    /// <param name="entities"></param>
    /// <returns></returns>
    private static List<TListItemDto> Filter(BaseQuery request, List<TListItemDto> entities)
    {
        if (request.IsActive != null)
        {
            PropertyInfo? codeProp =
                typeof(TListItemDto).GetProperties().FirstOrDefault(p => p.Name.Equals("IsActive"));
            if (codeProp != null) entities = [.. entities.Where(t => request.IsActive! == (bool)codeProp.GetValue(t)!)];
        }

        // filter for FilterString in all string properties
        if (!string.IsNullOrEmpty(request.FilterString))
        {
            PropertyInfo[] stringProperties = [.. typeof(TListItemDto).GetProperties().Where(p => p.PropertyType == typeof(string))];

            entities =
            [
                .. entities
                    .Where(item => stringProperties.Any(prop =>
                    {
                        object? obj = prop.GetValue(item);
                        if (obj == null) return false;
                        string value = obj.ToString()!;
                        if (string.IsNullOrEmpty(value)) return false;
                        return value.Contains(request.FilterString, StringComparison.OrdinalIgnoreCase);
                    }))
            ];
        }

        // filter for exact values in all properties
        if (request.FieldValues == null) return entities;
        PropertyInfo[] properties = [.. typeof(TListItemDto).GetProperties()];
        foreach (KeyValuePair<string, HashSet<string?>> filter in request.FieldValues)
        {
            PropertyInfo? prop =
                properties.FirstOrDefault(p => p.Name.Equals(filter.Key, StringComparison.OrdinalIgnoreCase));
            if (prop == null) continue;
            entities =
            [
                .. entities.Where(item =>
                {
                    object? obj = prop.GetValue(item);
                    return filter.Value.Contains(obj?.ToString());
                })
            ];
        }

        return entities;
    }

    /// <summary>
    /// Paginate a list of entities based on the provided query.
    /// </summary>
    /// <param name="request"></param>
    /// <param name="entities"></param>
    /// <returns></returns>
    private static EntityResponsePaged<TListItemDto> Paginate(BaseQuery request, List<TListItemDto> entities)
    {
        EntityResponsePaged<TListItemDto> result = new()
        {
            Total = entities.Count
        };
        if (entities.Count == 0) // return if no entities found
        {
            result.Entity = [];
            return result;
        }

        foreach (PageSort e in request.Sorts.Reverse()) // sort by multiple fields (first field order guaranteed)
        {
            const BindingFlags flags = BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase; // ONLY THIS WORKS
            object getProp(TListItemDto rliDto)
            {
                return typeof(TListItemDto).GetProperty(e.SortField, flags)!.GetValue(rliDto)!;
            }
            if (e.SortDirection == SortOperator.Asc)
                entities = [.. entities.OrderBy(getProp)];
            else
                entities = [.. entities.OrderByDescending(getProp)];
        }

        int skip = (request.PageIndex - 1) * request.PageSize; // skip entities not on the page
        entities = [.. entities.Skip(skip)];
        if (request.PageSize > 0) entities = [.. entities.Take(request.PageSize)];
        result.Entity = entities;
        return result;
    }

    /// <summary>
    /// Exports a list of entities to an Excel file.
    /// </summary>
    /// <param name="request">The query specifying the entities to export.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <param name="exactMatch">An optional expression for exact matching.</param>
    /// <returns>An <see cref="EntityResponse{B64File}"/> containing the exported Excel file or an error message.</returns>
    public async Task<EntityResponse<B64File>> ExportExcel(BaseQuery request, CancellationToken cancellationToken,
        Expression<Func<T, bool>>? exactMatch = null)
    {
        try
        {
            // convert to ClosedXML workbook
            XLWorkbook wb = new();
            IXLWorksheet ws = wb.Worksheets.Add(typeof(T).Name + " List");
            request.PageSize = 0; // ignore pagination, export everything
            ws.Cell(1, 1).InsertData(typeof(TListItemDto).GetProperties().Select(p => p.Name), true); // insert column names
            ws.Cell(2, 1).InsertData((await GetEntityPaged(request, cancellationToken, exactMatch)).Entity); // insert values

            // return base64-encoded file
            MemoryStream memoryStream = new();
            wb.SaveAs(memoryStream);
            B64File file = new()
            {
                MimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                FileName = typeof(T).Name + "_export.xlsx",
                Data = Convert.ToBase64String(memoryStream.ToArray())
            };
            return EntityResponse<B64File>.Success(file);
        }
        catch (Exception ex)
        {
            return EntityResponse<B64File>.Failed(ex.Message);
        }
    }

    /// <summary>
    /// Imports entities from an Excel file.
    /// </summary>
    /// <param name="user">The operator performing the action.</param>
    /// <param name="file">The Excel file containing the entities to import.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An <see cref="EntityResponse"/> indicating success or failure.</returns>
    public async Task<EntityResponse> ImportExcel(Operator user, B64File file, CancellationToken cancellationToken)
    {
        try
        {
            // convert to ClosedXML workbook
            using XLWorkbook wb = new(new MemoryStream(Convert.FromBase64String(file.Data)));
            IXLWorksheet ws = wb.Worksheet(1);
            IXLRows rows = ws.RowsUsed();
            IXLColumns columns = ws.ColumnsUsed();
            string[] columnNames = [.. columns.Select(c => c.Cell(1).Value.ToString())];

            // convert to list of entities and add to db
            PropertyInfo[] properties = typeof(TListItemDto).GetProperties();
            List<T> entities = [];
            foreach (IXLRow? row in rows.Skip(1))
            {
                TListItemDto entity = Activator.CreateInstance<TListItemDto>();
                for (int i = 0; i < columnNames.Length; i++)
                {
                    // match column name to db object property name
                    PropertyInfo? prop = properties.FirstOrDefault(p =>
                        p.Name.Equals(columnNames[i], StringComparison.OrdinalIgnoreCase));
                    if (prop == null) continue;

                    // get cell value and set to object property
                    XLCellValue cellVal = row.Cell(i + 1).Value;
                    if (cellVal.IsError) throw new Exception("Spreadsheet cell has error!");
                    if (cellVal.IsText)
                        prop.SetValue(entity, cellVal.GetText());
                    else if (cellVal.IsDateTime)
                        prop.SetValue(entity, cellVal.GetDateTime());
                    else if (cellVal.IsBoolean)
                        prop.SetValue(entity, cellVal.GetBoolean());
                    else if (cellVal.IsNumber) prop.SetValue(entity, cellVal.GetNumber());
                }

                T dbEntity = entity.Adapt<T>();
                if (!await PreAdd(dbEntity, user)) return EntityResponse<Guid>.Failed();
                entities.Add(dbEntity);
            }

            dbSet.AddRange(entities);
            await DB.SaveChangesAsync(cancellationToken);
            return EntityResponse.Success();
        }
        catch (Exception ex)
        {
            return EntityResponse.Failed(ex.Message + "\n" + ex.InnerException?.Message);
        }
    }
}
