﻿
namespace Visfuture.OneTeam.Core.Common.Base.Models;

public class EntityResponse
{
    public bool IsSuccess { get; set; }
    public string? Message { get; set; }
    public List<string>? Errors { get; set; } = default!;

    public static EntityResponse Success(string? message = default)
    {
        return new EntityResponse()
        {
            IsSuccess = true,
            Message = message
        };
    }
    public static EntityResponse Failed(string? message = default)
    {
        return new EntityResponse()
        {
            IsSuccess = false,
            Message = message,
        };
    }

    public static EntityResponse Failed(List<string> errors)
    {
        return new EntityResponse()
        {
            IsSuccess = false,
            Errors = errors
        };
    }
}


public class EntityResponse<T> : EntityResponse
{
    public T Entity { get; set; } = default!;

    public EntityResponse()
    {
        IsSuccess = true;
    }

    public EntityResponse(T entity)
    {
        Entity = entity;
        IsSuccess = true;
    }

    public EntityResponse(T entity, string message)
    {
        Entity = entity;
        Message = message;
        IsSuccess = true;
    }

    public static EntityResponse<T> Success(T entity)
    {
        return new EntityResponse<T>()
        {
            IsSuccess = true,
            Entity = entity
        };
    }

    public static new EntityResponse<T> Failed(string? message = default)
    {
        return new EntityResponse<T>()
        {
            IsSuccess = false,
            Message = message
        };
    }

    public static new EntityResponse<T> Failed(List<string> errors)
    {
        return new EntityResponse<T>()
        {
            IsSuccess = false,
            Errors = errors,
        };
    }
}