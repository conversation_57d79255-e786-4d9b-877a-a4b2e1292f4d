using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using Visfuture.OneTeam.BaseBiz.Api.CustomAttributes;

namespace Visfuture.OneTeam.BaseBiz.Api.Controllers;

[Route("[controller]")]
public class WeatherForecastController(ILogger<WeatherForecastController> logger, IDistributedCache distributedCache) : BaseController<WeatherForecastController>
{
    private readonly IDistributedCache _distributedCache = distributedCache;

    private static readonly string[] Summaries =
    [
        "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "Ba<PERSON>y", "Hot", "Sweltering", "Scorching"
    ];

    private readonly ILogger<WeatherForecastController> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    [APIAuthorize("Form.Project.ProjectList")]
    [HttpGet(Name = "GetWeatherForecast")]
    public IEnumerable<WeatherForecast> Get()
    {
        return Enumerable.Range(1, 5).Select(index => new WeatherForecast
        {
            Date = DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
            TemperatureC = Random.Shared.Next(-20, 55),
            Summary = Summaries[Random.Shared.Next(Summaries.Length)]
        })
        .ToArray();
    }

    // Docker run -p 6379:6379 --name redis -d redis
    // testing: https://localhost:7250/WeatherForecast/CacheUserId/?id=12345

    [HttpPost]
    [Route("CacheUserId")]
    public async Task<int?> SetCache([FromQuery] int id, CancellationToken cancellation = default)
    {
        string key = $"User-{id}";

        string? cachedUser = await _distributedCache.GetStringAsync(key, cancellation);

        int? UserId;
        if (string.IsNullOrEmpty(cachedUser))
        {
            // get user id from database
            UserId = 12345;

            if (UserId is null)
            {
                return UserId;
            }

            await _distributedCache.SetStringAsync(key, JsonConvert.SerializeObject(UserId), cancellation);

            return UserId;
        }

        UserId = JsonConvert.DeserializeObject<int?>(cachedUser);

        return UserId;
    }

}
