﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <Folder Include="Base.Entities\Ticket\" />
        <Folder Include="Base.Entities\People\" />
        <Folder Include="Base.Models\Interfaces\" />
        <Folder Include="Encryption\" />
        <Folder Include="Middlewares\" />
        <Folder Include="SMS\" />
        <Folder Include="PDF\" />
    </ItemGroup>

    <ItemGroup>
		<PackageReference Include="Azure.Identity" Version="1.14.0" />
        <PackageReference Include="ClosedXML" Version="0.104.2" />
        <PackageReference Include="Mapster" Version="7.4.0" />
        <PackageReference Include="Mapster.DependencyInjection" Version="1.0.1" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.10" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
		<PackageReference Include="Microsoft.Graph" Version="5.82.0" />
        <PackageReference Include="Minio" Version="6.0.4" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="xunit" Version="2.9.3" />
    </ItemGroup>

</Project>
