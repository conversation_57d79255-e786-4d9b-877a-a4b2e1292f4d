{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Backend": "Ticket", "Minio": {"Endpoint": "blob.vfqa.ca", "AccessKey": "9ruPOkxfIpDwrLoAPBWF", "SecretKey": "MNSnBHXpOQ9jaEYE1v4QiCrkeVraYz188O1tREvB"}, "AllowedHosts": "*", "ConnectionStrings": {"sqlConnection": "Server=vfsql01.mayerglobal.com;Database=OneTeam.Ticket;User Id=oneteampro.webuser;Password=********;MultipleActiveResultSets=True;TrustServerCertificate=True;", "basebiz-sqlConnection": "Server=vfsql01.mayerglobal.com;Database=OneTeam.BaseBiz;User Id=oneteampro.webuser;Password=********;MultipleActiveResultSets=True;TrustServerCertificate=True;"}, "JWT": {"key": "C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4", "Issuer": "SecureApi", "Audience": "SecureApiUser", "DurationInMinutes": 1}, "EmailScanner": {"SupportEmailAddress": "<EMAIL>"}, "GraphMail": {"TenantId": "Microsoft Entra ID directory tenant ID", "ClientId": "Microsoft Entra ID application client ID", "ClientSecret": "Microsoft Entra ID One Team client secret", "UserObjectId": "Microsoft Entra ID user object ID"}}