{"openapi": "3.0.1", "info": {"title": "Visfuture.OneTeam.BaseBiz.Api", "version": "1.0"}, "paths": {"/Login": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Refresh": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserJwtDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserJwtDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserJwtDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Logout": {"get": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/UserTenantList": {"get": {"tags": ["Account"], "responses": {"200": {"description": "OK"}}}}, "/SetTenant": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GuidRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GuidRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GuidRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/External/SequenceNoByName": {"get": {"tags": ["External"], "parameters": [{"name": "name", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/External/UpdateSequenceNo": {"put": {"tags": ["External"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SequenceNoDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/File/Upload": {"post": {"tags": ["File"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/File/GetLink": {"get": {"tags": ["File"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/File/Delete": {"get": {"tags": ["File"], "parameters": [{"name": "fileId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/I18nKeyList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/I18nKeyDetails": {"get": {"tags": ["Misc"], "parameters": [{"name": "i18nKeyId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddI18nKey": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nKeyDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateI18nKey": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nKeyDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteI18nKey": {"delete": {"tags": ["Misc"], "parameters": [{"name": "i18nKeyId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddI18nKeyList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateI18nKeyList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteI18nKeyList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportI18nKeyList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nKeyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportI18nKeyList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/I18nTranslationList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/I18nTranslationDetails": {"get": {"tags": ["Misc"], "parameters": [{"name": "i18nTranslationId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddI18nTranslation": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateI18nTranslation": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteI18nTranslation": {"delete": {"tags": ["Misc"], "parameters": [{"name": "i18nTranslationId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddI18nTranslationList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateI18nTranslationList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteI18nTranslationList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportI18nTranslationList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/I18nTranslationDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportI18nTranslationList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/NotificationTemplateList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/NotificationTemplateDetails": {"get": {"tags": ["Misc"], "parameters": [{"name": "notificationTemplateId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddNotificationTemplate": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateNotificationTemplate": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteNotificationTemplate": {"delete": {"tags": ["Misc"], "parameters": [{"name": "notificationTemplateId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddNotificationTemplateList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateNotificationTemplateList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteNotificationTemplateList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportNotificationTemplateList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportNotificationTemplateList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/GetNotificationTemplateByNotificationTypeAndNotificationMethod": {"get": {"tags": ["Misc"], "parameters": [{"name": "notificationType", "in": "query", "schema": {"type": "string"}}, {"name": "notificationMethod", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/SequenceNoList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/SequenceNoByName": {"get": {"tags": ["Misc"], "parameters": [{"name": "name", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/SequenceNoDetails": {"get": {"tags": ["Misc"], "parameters": [{"name": "sequenceNoId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddSequenceNo": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SequenceNoDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateSequenceNo": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SequenceNoDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteSequenceNo": {"delete": {"tags": ["Misc"], "parameters": [{"name": "sequenceNoId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddSequenceNoList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateSequenceNoList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteSequenceNoList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportSequenceNoList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SequenceNoDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportSequenceNoList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/CodeItemList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/CodeItemDetails": {"get": {"tags": ["Misc"], "parameters": [{"name": "codeItemId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddCodeItem": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateCodeItem": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteCodeItem": {"delete": {"tags": ["Misc"], "parameters": [{"name": "codeItemId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddCodeItemList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateCodeItemList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteCodeItemList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportCodeItemList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportCodeItemList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/CodeItemAttributeList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/CodeItemAttributeDetails": {"get": {"tags": ["Misc"], "parameters": [{"name": "codeItemAttributeId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddCodeItemAttribute": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateCodeItemAttribute": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteCodeItemAttribute": {"delete": {"tags": ["Misc"], "parameters": [{"name": "codeItemAttributeId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddCodeItemAttributeList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateCodeItemAttributeList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteCodeItemAttributeList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportCodeItemAttributeList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeItemAttributeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportCodeItemAttributeList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/CodeTypeList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/CodeTypeDetails": {"get": {"tags": ["Misc"], "parameters": [{"name": "codeTypeId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/CodeTypeByTypeCode": {"get": {"tags": ["Misc"], "parameters": [{"name": "typeCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/AddCodeType": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeTypeDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateCodeType": {"put": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeTypeDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteCodeType": {"delete": {"tags": ["Misc"], "parameters": [{"name": "codeTypeId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddCodeTypeList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateCodeTypeList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteCodeTypeList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportCodeTypeList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeTypeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportCodeTypeList": {"post": {"tags": ["Misc"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/EmployeeList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/EmployeeDetails": {"get": {"tags": ["Org"], "parameters": [{"name": "employeeId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddEmployee": {"put": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmployeeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmployeeDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateEmployee": {"put": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmployeeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmployeeDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteEmployee": {"delete": {"tags": ["Org"], "parameters": [{"name": "employeeId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddEmployeeList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateEmployeeList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteEmployeeList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportEmployeeList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportEmployeeList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/GlobalAdminList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/GlobalAdminDetails": {"get": {"tags": ["Org"], "parameters": [{"name": "globalAdminId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddGlobalAdmin": {"put": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateGlobalAdmin": {"put": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteGlobalAdmin": {"delete": {"tags": ["Org"], "parameters": [{"name": "globalAdminId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddGlobalAdminList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateGlobalAdminList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteGlobalAdminList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportGlobalAdminList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GlobalAdminDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportGlobalAdminList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/EmployeeListByOrganizationList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmployeeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/OrganizationEmployeeList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/OrganizationEmployeeDetails": {"get": {"tags": ["Org"], "parameters": [{"name": "organizationEmployeeId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddOrganizationEmployee": {"put": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateOrganizationEmployee": {"put": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteOrganizationEmployee": {"delete": {"tags": ["Org"], "parameters": [{"name": "organizationEmployeeId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddOrganizationEmployeeList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateOrganizationEmployeeList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteOrganizationEmployeeList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportOrganizationEmployeeList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationEmployeeDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportOrganizationEmployeeList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/OrganizationHierarchyList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/OrganizationHierarchyDetails": {"get": {"tags": ["Org"], "parameters": [{"name": "organizationHierarchyId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddOrganizationHierarchy": {"put": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateOrganizationHierarchy": {"put": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteOrganizationHierarchy": {"delete": {"tags": ["Org"], "parameters": [{"name": "organizationHierarchyId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddOrganizationHierarchyList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateOrganizationHierarchyList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteOrganizationHierarchyList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportOrganizationHierarchyList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrganizationHierarchyDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportOrganizationHierarchyList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TenantList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/TenantDetails": {"get": {"tags": ["Org"], "parameters": [{"name": "tenantId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTenant": {"put": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTenant": {"put": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTenant": {"delete": {"tags": ["Org"], "parameters": [{"name": "tenantId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddTenantList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateTenantList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteTenantList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportTenantList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TenantDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportTenantList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UserAccountList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UserAccountDetails": {"get": {"tags": ["Org"], "parameters": [{"name": "userAccountId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddUserAccount": {"put": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAccountDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserAccountDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserAccountDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateUserAccount": {"put": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAccountDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserAccountDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserAccountDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteUserAccount": {"delete": {"tags": ["Org"], "parameters": [{"name": "userAccountId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddUserAccountList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateUserAccountList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteUserAccountList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportUserAccountList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserAccountDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportUserAccountList": {"post": {"tags": ["Org"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/RoleList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/RoleDetails": {"get": {"tags": ["Role"], "parameters": [{"name": "roleId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddRole": {"put": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateRole": {"put": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteRole": {"delete": {"tags": ["Role"], "parameters": [{"name": "roleId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddRoleList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateRoleList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteRoleList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportRoleList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportRoleList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/RoleAccessList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAccessDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleAccessDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleAccessDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/RoleAccessDetails": {"get": {"tags": ["Role"], "parameters": [{"name": "roleAccessId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddRoleAccess": {"put": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAccessDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleAccessDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleAccessDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateRoleAccess": {"put": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAccessDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleAccessDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleAccessDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteRoleAccess": {"delete": {"tags": ["Role"], "parameters": [{"name": "roleAccessId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddRoleAccessList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAccessDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleAccessDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleAccessDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateRoleAccessList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAccessDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleAccessDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleAccessDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteRoleAccessList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAccessDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleAccessDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleAccessDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportRoleAccessList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportRoleAccessList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AssignableRoleList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AssignableRoleDetails": {"get": {"tags": ["Role"], "parameters": [{"name": "assignableRoleId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddAssignableRole": {"put": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateAssignableRole": {"put": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteAssignableRole": {"delete": {"tags": ["Role"], "parameters": [{"name": "assignableRoleId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddAssignableRoleList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateAssignableRoleList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteAssignableRoleList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignableRoleDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportAssignableRoleList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportAssignableRoleList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/RoleAssignmentList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/RoleAssignmentDetails": {"get": {"tags": ["Role"], "parameters": [{"name": "roleAssignmentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddRoleAssignment": {"put": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateRoleAssignment": {"put": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteRoleAssignment": {"delete": {"tags": ["Role"], "parameters": [{"name": "roleAssignmentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddRoleAssignmentList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateRoleAssignmentList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteRoleAssignmentList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleAssignmentDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportRoleAssignmentList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportRoleAssignmentList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AccessResourceList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccessResourceDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccessResourceDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AccessResourceDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/AccessResourceDetails": {"get": {"tags": ["Role"], "parameters": [{"name": "accessResourceId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddAccessResource": {"put": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccessResourceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccessResourceDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AccessResourceDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateAccessResource": {"put": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccessResourceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccessResourceDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AccessResourceDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteAccessResource": {"delete": {"tags": ["Role"], "parameters": [{"name": "accessResourceId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/AddAccessResourceList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccessResourceDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccessResourceDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AccessResourceDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/UpdateAccessResourceList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccessResourceDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccessResourceDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AccessResourceDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/DeleteAccessResourceList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccessResourceDtoBaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccessResourceDtoBaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AccessResourceDtoBaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ExportAccessResourceList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BaseQuery"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ImportAccessResourceList": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "text/json": {"schema": {"$ref": "#/components/schemas/B64File"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/B64File"}}}}, "responses": {"200": {"description": "OK"}}}}, "/MatchAccessResource": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringListRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringListRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StringListRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}, "/WeatherForecast/CacheUserId": {"post": {"tags": ["WeatherForecast"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}}, "components": {"schemas": {"AccessResource": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "resourceCode": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isPublic": {"type": "boolean"}, "subType": {"type": "string", "nullable": true}, "superiorId": {"type": "string", "format": "uuid", "nullable": true}, "systemId": {"type": "string", "nullable": true}, "moduleId": {"type": "string", "nullable": true}, "roleAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/RoleAccess"}, "nullable": true}}, "additionalProperties": false}, "AccessResourceDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "resourceCode": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isPublic": {"type": "boolean"}, "subType": {"type": "string", "nullable": true}, "superiorId": {"type": "string", "format": "uuid", "nullable": true}, "systemId": {"type": "string", "nullable": true}, "moduleId": {"type": "string", "nullable": true}, "roleAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/RoleAccess"}, "nullable": true}}, "additionalProperties": false}, "AccessResourceDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/AccessResourceDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "AssignableRole": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "organizationId": {"type": "string", "format": "uuid"}, "roleId": {"type": "string", "format": "uuid"}, "isInheritable": {"type": "boolean"}, "organization": {"$ref": "#/components/schemas/OrganizationHierarchy"}, "role": {"$ref": "#/components/schemas/Role"}, "roleAssignments": {"type": "array", "items": {"$ref": "#/components/schemas/RoleAssignment"}, "nullable": true}}, "additionalProperties": false}, "AssignableRoleDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "organizationId": {"type": "string", "format": "uuid"}, "roleId": {"type": "string", "format": "uuid"}, "isInheritable": {"type": "boolean"}, "roleAssignments": {"type": "array", "items": {"$ref": "#/components/schemas/RoleAssignmentDto"}, "nullable": true}}, "additionalProperties": false}, "AssignableRoleDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/AssignableRoleDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "B64File": {"type": "object", "properties": {"mimeType": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "data": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BaseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "BaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/BaseDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "CodeItemAttributeDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "codeItemId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CodeItemAttributeDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/CodeItemAttributeDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "CodeItemDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "codeTypeId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "seqNo": {"type": "string", "nullable": true}, "superiorId": {"type": "string", "format": "uuid", "nullable": true}, "itemField1": {"type": "string", "nullable": true}, "itemField2": {"type": "string", "nullable": true}, "itemField3": {"type": "string", "nullable": true}, "codeItemAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CodeItemAttributeDto"}, "nullable": true}}, "additionalProperties": false}, "CodeItemDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/CodeItemDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "CodeItemRequest": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/CodeItemDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}, "underSuperiorOf": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "CodeTypeDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "typeCode": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "mainType": {"type": "string", "nullable": true}, "subType": {"type": "string", "nullable": true}, "superiorId": {"type": "string", "format": "uuid", "nullable": true}, "codeItems": {"type": "array", "items": {"$ref": "#/components/schemas/CodeItemDto"}, "nullable": true}}, "additionalProperties": false}, "CodeTypeDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/CodeTypeDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "Employee": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "mainType": {"type": "string", "nullable": true}, "subType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "positionId": {"type": "string", "nullable": true}, "jobTitle": {"type": "string", "nullable": true}, "businessEmail": {"type": "string", "nullable": true}, "workScheduleId": {"type": "string", "nullable": true}, "hireDate": {"type": "string", "format": "date-time"}, "terminateDate": {"type": "string", "format": "date-time", "nullable": true}, "employeeStatus": {"type": "string", "nullable": true}, "userAccountId": {"type": "string", "format": "uuid", "nullable": true}, "isTenantAdmin": {"type": "boolean"}, "userAccount": {"$ref": "#/components/schemas/UserAccount"}, "organizationEmployees": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationEmployee"}, "nullable": true}, "roleAssignments": {"type": "array", "items": {"$ref": "#/components/schemas/RoleAssignment"}, "nullable": true}}, "additionalProperties": false}, "EmployeeDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "mainType": {"type": "string", "nullable": true}, "subType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "positionId": {"type": "string", "nullable": true}, "jobTitle": {"type": "string", "nullable": true}, "businessEmail": {"type": "string", "nullable": true}, "workScheduleId": {"type": "string", "nullable": true}, "hireDate": {"type": "string", "format": "date-time"}, "terminateDate": {"type": "string", "format": "date-time", "nullable": true}, "employeeStatus": {"type": "string", "nullable": true}, "userAccountId": {"type": "string", "format": "uuid", "nullable": true}, "isTenantAdmin": {"type": "boolean"}, "organizationEmployees": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationEmployeeDto"}, "nullable": true}, "roleAssignments": {"type": "array", "items": {"$ref": "#/components/schemas/RoleAssignmentDto"}, "nullable": true}}, "additionalProperties": false}, "EmployeeDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/EmployeeDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "GlobalAdminDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "loginName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "mobile": {"type": "string", "nullable": true}, "mfa": {"type": "boolean"}, "isActive": {"type": "boolean"}, "imageId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GlobalAdminDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/GlobalAdminDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "GuidRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "I18nKey": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "keyCode": {"type": "string", "nullable": true}, "keyType": {"type": "string", "nullable": true}, "defaultValue": {"type": "string", "nullable": true}, "i18NTranslations": {"type": "array", "items": {"$ref": "#/components/schemas/I18nTranslation"}, "nullable": true}}, "additionalProperties": false}, "I18nKeyDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "keyCode": {"type": "string", "nullable": true}, "keyType": {"type": "string", "nullable": true}, "defaultValue": {"type": "string", "nullable": true}, "i18NTranslations": {"type": "array", "items": {"$ref": "#/components/schemas/I18nTranslation"}, "nullable": true}}, "additionalProperties": false}, "I18nKeyDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/I18nKeyDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "I18nTranslation": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "keyId": {"type": "string", "format": "uuid"}, "locale": {"type": "string", "nullable": true}, "translation": {"type": "string", "nullable": true}, "i18nKey": {"$ref": "#/components/schemas/I18nKey"}}, "additionalProperties": false}, "I18nTranslationDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "keyId": {"type": "string", "format": "uuid"}, "locale": {"type": "string", "nullable": true}, "translation": {"type": "string", "nullable": true}}, "additionalProperties": false}, "I18nTranslationDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/I18nTranslationDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "NotificationTemplateDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "notificationType": {"type": "string", "nullable": true}, "notificationMethod": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "body": {"type": "string", "nullable": true}, "sender": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NotificationTemplateDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationTemplateDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "OrganizationEmployee": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "organizationId": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string", "format": "uuid"}, "employee": {"$ref": "#/components/schemas/Employee"}, "organization": {"$ref": "#/components/schemas/OrganizationHierarchy"}}, "additionalProperties": false}, "OrganizationEmployeeDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "organizationId": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "OrganizationEmployeeDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationEmployeeDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "OrganizationHierarchy": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "mainType": {"type": "string", "nullable": true}, "subType": {"type": "string", "nullable": true}, "superiorId": {"type": "string", "format": "uuid", "nullable": true}, "defaultLocation": {"type": "string", "nullable": true}, "defaultWorkSchedule": {"type": "string", "nullable": true}, "assignableRoles": {"type": "array", "items": {"$ref": "#/components/schemas/AssignableRole"}, "nullable": true}, "inverseSuperior": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationHierarchy"}, "nullable": true}, "organizationEmployees": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationEmployee"}, "nullable": true}, "superior": {"$ref": "#/components/schemas/OrganizationHierarchy"}}, "additionalProperties": false}, "OrganizationHierarchyDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "mainType": {"type": "string", "nullable": true}, "subType": {"type": "string", "nullable": true}, "superiorId": {"type": "string", "format": "uuid", "nullable": true}, "defaultLocation": {"type": "string", "nullable": true}, "defaultWorkSchedule": {"type": "string", "nullable": true}, "assignableRoles": {"type": "array", "items": {"$ref": "#/components/schemas/AssignableRoleDto"}, "nullable": true}, "inverseSuperior": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationHierarchyDto"}, "nullable": true}, "organizationEmployees": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationEmployeeDto"}, "nullable": true}}, "additionalProperties": false}, "OrganizationHierarchyDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationHierarchyDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "PageSort": {"required": ["sortDirection", "sortField"], "type": "object", "properties": {"sortField": {"minLength": 1, "type": "string"}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}}, "additionalProperties": false}, "Role": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "usage": {"type": "string", "nullable": true}, "assignableRoles": {"type": "array", "items": {"$ref": "#/components/schemas/AssignableRole"}, "nullable": true}, "roleAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/RoleAccess"}, "nullable": true}}, "additionalProperties": false}, "RoleAccess": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "roleId": {"type": "string", "format": "uuid"}, "resourceId": {"type": "string", "format": "uuid"}, "accessType": {"type": "string", "nullable": true}, "resource": {"$ref": "#/components/schemas/AccessResource"}, "role": {"$ref": "#/components/schemas/Role"}}, "additionalProperties": false}, "RoleAccessDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "roleId": {"type": "string", "format": "uuid"}, "resourceId": {"type": "string", "format": "uuid"}, "accessType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RoleAccessDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/RoleAccessDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "RoleAssignment": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "assignableRoleId": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string", "format": "uuid"}, "isInheritable": {"type": "boolean"}, "assignableRole": {"$ref": "#/components/schemas/AssignableRole"}, "employee": {"$ref": "#/components/schemas/Employee"}}, "additionalProperties": false}, "RoleAssignmentDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "assignableRoleId": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string", "format": "uuid"}, "isInheritable": {"type": "boolean"}, "organizationId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "RoleAssignmentDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/RoleAssignmentDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "RoleDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "usage": {"type": "string", "nullable": true}, "assignableRoles": {"type": "array", "items": {"$ref": "#/components/schemas/AssignableRoleDto"}, "nullable": true}, "roleAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/RoleAccessDto"}, "nullable": true}}, "additionalProperties": false}, "RoleDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/RoleDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "SequenceNoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "prefix": {"type": "string", "nullable": true}, "suffix": {"type": "string", "nullable": true}, "currentNo": {"type": "integer", "format": "int64"}, "length": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SequenceNoDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/SequenceNoDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "SortOperator": {"enum": [0, 1], "type": "integer", "format": "int32"}, "StringListRequest": {"type": "object", "properties": {"stringList": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "TenantDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "province": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "effectiveDate": {"type": "string", "format": "date-time"}, "expireDate": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "domain": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}, "timeZone": {"type": "string", "nullable": true}, "contactName": {"type": "string", "nullable": true}, "contactPhone": {"type": "string", "nullable": true}, "contactFax": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TenantDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/TenantDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "UserAccount": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "name": {"type": "string", "nullable": true}, "passwordHash": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "mobile": {"type": "string", "nullable": true}, "mfa": {"type": "boolean"}, "isActive": {"type": "boolean"}, "description": {"type": "string", "nullable": true}, "imageId": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}, "employees": {"type": "array", "items": {"$ref": "#/components/schemas/Employee"}, "nullable": true}}, "additionalProperties": false}, "UserAccountDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createBy": {"type": "string", "nullable": true}, "createAt": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "updateAt": {"type": "string", "format": "date-time"}, "name": {"type": "string", "nullable": true}, "passwordHash": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "mobile": {"type": "string", "nullable": true}, "mfa": {"type": "boolean"}, "isActive": {"type": "boolean"}, "description": {"type": "string", "nullable": true}, "imageId": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}, "employees": {"type": "array", "items": {"$ref": "#/components/schemas/EmployeeDto"}, "nullable": true}}, "additionalProperties": false}, "UserAccountDtoBaseQuery": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "idList": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "noList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dtoList": {"type": "array", "items": {"$ref": "#/components/schemas/UserAccountDto"}, "nullable": true}, "filterString": {"type": "string", "nullable": true}, "fieldValues": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "nullable": true}, "uniqueFromField": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/PageSort"}, "nullable": true}, "sortField": {"type": "string", "nullable": true, "readOnly": true}, "sortDirection": {"$ref": "#/components/schemas/SortOperator"}, "orderBy": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "UserJwtDto": {"type": "object", "properties": {"jwtToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid"}, "userName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserLoginRequest": {"required": ["password", "username"], "type": "object", "properties": {"username": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Authorization header.\r\nExample:'Bearer 123456abcdef'", "name": "Authorization", "in": "header"}}}, "security": [{"Authorization": []}]}