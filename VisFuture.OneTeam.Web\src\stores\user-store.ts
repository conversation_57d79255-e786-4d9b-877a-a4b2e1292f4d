import { useLocalStorage } from '@vueuse/core'
import { defineStore } from 'pinia'
import { baseUrls } from '../services/api/requests'
import { EntityResponse } from '../services/api/entityResponse'

export const useUserStore = defineStore('user', {
  state: () => {
    return {
      userName: '<PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      memberSince: '8/12/2020',
      pfp: 'https://picsum.photos/id/22/200/300',
      is2FAEnabled: false,
      jwtToken: useLocalStorage('jwtToken', ''),
      refreshToken: useLocalStorage('refreshToken', ''),
      currentTenantId: useLocalStorage('currentTenantId', ''), // persist tenant
      tenantList: [] as { id: string; name: string }[], // optional: store tenant list
    }
  },

  actions: {
    async login(userName: string, password: string): Promise<boolean> {
      const res = await fetch(baseUrls.BaseBiz + '/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username: userName, password }),
      })

      const json = (await res.json()) as EntityResponse
      if (!json.isSuccess) return false

      const data = json.entity
      this.jwtToken = data.jwtToken
      this.refreshToken = data.refreshToken
      this.userName = userName
      return true
    },

    //  Add this for setting tenant
    async setTenant(tenantId: string): Promise<boolean> {
      const res = await fetch(baseUrls.BaseBiz + '/SetTenant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.jwtToken}`,
        },
        body: JSON.stringify({ id: tenantId }),
      })

      if (res.ok) {
        this.currentTenantId = tenantId
        return true
      }

      return false
    },

    //  (Optional) logout method
    logout() {
      this.jwtToken = ''
      this.refreshToken = ''
      this.currentTenantId = ''
      this.tenantList = []
      this.userName = ''
    },
  },
})
