﻿using System.Security.Cryptography;
using System.Text;

namespace Visfuture.OneTeam.Core.Common.Helpers;

public static class CryptoUtils
{
    private static readonly AesCcm _aes = new(
        [0x2F, 0x42, 0x3F, 0x45, 0x28, 0x48, 0x2B, 0x4D, 0x62, 0x51, 0x65, 0x53, 0x68, 0x56, 0x6D, 0x59, 0x71, 0x33, 0x74, 0x36, 0x77, 0x39, 0x7A, 0x24, 0x43, 0x26, 0x46, 0x29, 0x4A, 0x40, 0x4E, 0x63]
    );

    public static string Encrypt(string plainText)
    {
        var plainBytes = new ReadOnlySpan<byte>(Encoding.UTF8.GetBytes(plainText));

        int nonceSize = AesGcm.NonceByteSizes.MaxSize;
        int tagSize = AesGcm.TagByteSizes.MaxSize;


        var cipherBytes = new byte[nonceSize + tagSize + plainBytes.Length];

        var nonce = cipherBytes.AsSpan(0, nonceSize);
        var tag = cipherBytes.AsSpan(nonceSize, tagSize);
        var cipher = cipherBytes.AsSpan(nonceSize + tagSize);

        RandomNumberGenerator.Fill(nonce);
        _aes.Encrypt(nonce, plainBytes, cipher, tag);

        return Convert.ToBase64String(cipherBytes);
    }

    public static string Decrypt(string cipherText)
    {
        int nonceSize = AesGcm.NonceByteSizes.MaxSize;
        int tagSize = AesGcm.TagByteSizes.MaxSize;

        var cipher = Convert.FromBase64String(cipherText);
        var plainBytes = new byte[cipher.Length - nonceSize - tagSize];

        var nonce = cipher.AsSpan(0, nonceSize);
        var tag = cipher.AsSpan(nonceSize, tagSize);
        var cipherBytes = cipher.AsSpan(nonceSize + tagSize);

        _aes.Decrypt(nonce, cipherBytes, tag, plainBytes);

        return Encoding.UTF8.GetString(plainBytes);
    }

}
