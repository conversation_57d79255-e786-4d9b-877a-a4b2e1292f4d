﻿using System.Security.Cryptography;
using System.Text;

namespace Visfuture.OneTeam.Core.Common.Helpers;
public class RsaHelper : IDisposable
{
    private readonly RSA _rsa;

    /// <summary>  
    /// Create a new instance of <see cref="RsaHelper"/>.  
    /// </summary>  
    public RsaHelper(bool isPrivate)
    {
        _rsa = RSA.Create();
        string filename = isPrivate ? "private_key.pem" : "public_key.pem";
        // hardcoded path to the public key  
        byte[] key = ReadKey(Path.Combine(Directory.GetCurrentDirectory(), "Keys", filename));
        _rsa.ImportFromPem(Encoding.UTF8.GetString(key));
    }

    /// <summary>  
    /// Verifies the specified file using the specified <see cref="RSA"/> signature. The digest  
    /// used is <see cref="HashAlgorithmName.SHA256"/>.  
    /// </summary>  
    /// <param name="file">The file to verify.</param>  
    /// <param name="signature">  
    /// The signature used to verify the specified file.  
    /// </param>  
    /// <returns>Whether the file was verified successfully.</returns>  
    public bool Verify(string file, string signature)
    {
        return _rsa.VerifyData(
            Encoding.UTF8.GetBytes(file),
            Convert.FromBase64String(signature),
            HashAlgorithmName.SHA256,
            RSASignaturePadding.Pkcs1
        );
    }

    /// <summary>  
    /// Reads a PEM encoded key and returns the corresponding binary key.  
    /// </summary>  
    /// <param name="keyPath">The path to the PEM encoded key.</param>  
    /// <returns>The corresponding binary key.</returns>  
    private static byte[] ReadKey(string keyPath)
    {
        string encodedKey = File
            .ReadAllText(keyPath)
            .Trim();
        return Encoding.UTF8.GetBytes(encodedKey);
    }

    public string Sign(string file)
    {
        return Convert.ToBase64String(_rsa.SignData(
            Encoding.UTF8.GetBytes(file),
            HashAlgorithmName.SHA256,
            RSASignaturePadding.Pkcs1
        ));
    }

    public void Dispose()
    {
        _rsa.Dispose();
        GC.SuppressFinalize(this);
    }
}
