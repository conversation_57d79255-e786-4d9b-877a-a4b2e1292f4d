using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Project.BusinessLogic.DTOs;
using Visfuture.OneTeam.Project.BusinessLogic.ListItemDtos;

namespace Visfuture.OneTeam.Project.BusinessLogic.Interfaces;

public interface ICompanyManager
{
    #region Company
    Task<EntityResponse<CompanyDto>> GetCompanyByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<CompanyDto>> GetCompanyByCodeAsync(string code, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteCompanyAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddCompanyAsync(CompanyDto companyDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateCompanyAsync(CompanyDto companyDto, CancellationToken cancellationToken = default);
    Task<EntityResponsePaged<CompanyListItemDto>> QueryCompanyAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddCompaniesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateCompaniesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteCompaniesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    #endregion

    #region Contact
    Task<EntityResponsePaged<ContactListItemDto>> QueryContactAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<ContactDto>> GetContactByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteContactAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddContactAsync(ContactDto contactDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateContactAsync(ContactDto contactDto, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddContactsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateContactsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteContactsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<List<ContactDto>>> GetContactsByEmailAsync(string email, CancellationToken cancellationToken = default);
    #endregion

    Task<EntityResponse<B64File>> ExportCompanyExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<B64File>> ExportContactExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportCompanyExcel(B64File file, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportContactExcel(B64File file, CancellationToken cancellationToken = default);
}
