{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "BackendsList": {"BaseBiz": "https://oneteampro-qa.vfqa.ca/basebiz", "Project": "https://oneteampro-qa.vfqa.ca/project", "Ticket": "https://oneteampro-qa.vfqa.ca/ticket"}, "Minio": {"BucketName": "oneteam-qa"}, "AllowedHosts": "*", "ConnectionStrings": {"SqlConnection": "Server=************;Database=OneTeam.BaseBiz;User Id=oneteampro.webuser;Password=********;MultipleActiveResultSets=True;TrustServerCertificate=True;", "Redis": "*************:6379"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": "Information", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/applog-.txt", "rollingInterval": "Day"}}], "Enrich": ["FromLogContext", "WithMachineName"], "Properties": {"ApplicationName": "Your ASP.NET Core App"}}, "JwtSettings": {"validIssuer": "OneTeam", "validAudience": "https://localhost:7250", "expires": 5}, "JWT": {"key": "C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4C1CF4B7DC4C4175B6618DE4F55CA4", "Issuer": "SecureApi", "Audience": "SecureApiUser", "DurationInMinutes": 1}}