using Azure.Identity;
using MapsterMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Graph;
using Newtonsoft.Json.Linq;
using Stubble.Core.Builders;
using System.Linq.Expressions;
using System.Text.Json;
using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Core.Common.Emailer;
using Visfuture.OneTeam.Core.Common.Helpers;
using Visfuture.OneTeam.Ticket.BusinessLogic.DTOs;
using Visfuture.OneTeam.Ticket.BusinessLogic.Interfaces;
using Visfuture.OneTeam.Ticket.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.Ticket.DataAccess.DbContext;
using Visfuture.OneTeam.Ticket.DataAccess.Entities;

namespace Visfuture.OneTeam.Ticket.BusinessLogic;

public class TicketManager(
    IConfiguration configuration,
    IHttpClientFactory httpClientFactory,
    IHttpContextAccessor httpContextAccessor,
    AppDataContext appDataContext,
    IMapper mapper) : TicketBaseManager(httpContextAccessor, appDataContext, mapper), ITicketManager
{
    private readonly CrudHelper<TicketBilling, TicketBillingDto, TicketBillingListItemDto> _ticketBillingCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.TicketBillings);

    private readonly CrudHelper<TicketBillingDelivery, TicketBillingDeliveryDto, TicketBillingDeliveryListItemDto>
        _ticketBillingDeliveryCrudHelper =
            new(configuration, httpClientFactory, appDataContext, appDataContext.TicketBillingDeliveries);

    private readonly CrudHelper<TicketBillingPayment, TicketBillingPaymentDto, TicketBillingPaymentListItemDto>
        _ticketBillingPaymentCrudHelper =
            new(configuration, httpClientFactory, appDataContext, appDataContext.TicketBillingPayments);

    private readonly CrudHelper<DataAccess.Entities.Ticket, TicketDto, TicketListItemDto> _ticketCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.Tickets);

    private readonly CrudHelper<TicketDevOpsLink, TicketDevOpsLinkDto, TicketDevOpsLinkListItemDto>
        _ticketDevOpsLinkCrudHelper =
            new(configuration, httpClientFactory, appDataContext, appDataContext.TicketDevOpsLinks);

    private readonly CrudHelper<TicketDiscussion, TicketDiscussionDto, TicketDiscussionListItemDto>
        _ticketDiscussionCrudHelper =
            new(configuration, httpClientFactory, appDataContext, appDataContext.TicketDiscussions);

    private readonly CrudHelper<TicketLink, TicketLinkDto, TicketLinkListItemDto> _ticketLinkCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.TicketLinks);

    private readonly CrudHelper<TicketReview, TicketReviewDto, TicketReviewListItemDto> _ticketReviewCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.TicketReviews);

    private readonly CrudHelper<TicketDocument, TicketDocumentDto, TicketDocumentListItemDto> _ticketDocumentCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.TicketDocuments);

    #region Ticket

    private readonly Func<BaseQuery, Expression<Func<DataAccess.Entities.Ticket, bool>>> exactMatch = request => t =>
        request.IsActive == null || (request.IsActive == true && t.EndDate == null) ||
        (t.EndDate != null && request.IsActive == CurrentDateTime < t.EndDate);

    public async Task<EntityResponse<TicketDto>> GetTicketByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        EntityResponse<TicketDto> response = await _ticketCrudHelper.GetEntity(id, cancellationToken);
        
        HttpContent httpResponse =
            await ExternalRequestUtils.Send(httpClientFactory,
                configuration["BackendsList:Project"]! + "/External/GetWorkTimeByTicketId?ticketId=" + id,
                "GET", "");
        JToken json = JObject.Parse(await httpResponse.ReadAsStringAsync())["entity"]!;
        response.Entity.WorkMinutes = short.Parse(json.ToString());

        return response;
    }

    public async Task<EntityResponse<TicketDto>> GetTicketByNoAsync(string ticketNo,
        CancellationToken cancellationToken = default)
    {
        return await _ticketCrudHelper.GetEntityBy(t => t.Code == ticketNo, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _ticketCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketAsync(TicketDto ticketDto,
        CancellationToken cancellationToken = default)
    {
        return await _ticketCrudHelper.AddEntity(GetCurrentUser(), ticketDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketFromEmailScannerAsync(EmailMessageDto emailMessage,
        CancellationToken cancellationToken = default)
    {
        var httpResponseContacts = await ExternalRequestUtils.Send(
            httpClientFactory,
            configuration["BackendsList:Project"]! + "/GetContactsByEmail?email=" + Uri.EscapeDataString(emailMessage.From!),
            "GET",
            ""
            );
        var entityResponseContacts = JsonSerializer.Deserialize<EntityResponse<List<ContactDto>>>(
            await httpResponseContacts.ReadAsStringAsync(),
            new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

        if (entityResponseContacts?.Entity == null || entityResponseContacts.Entity.Count() == 0)
        {
            await SendEmailReplyOnBadFromAddress(emailMessage, "04", "Email address does not exist in the system.");
            return EntityResponse<Guid>.Success(Guid.NewGuid());
        }
        else if (entityResponseContacts.Entity.Select(p => p.CompanyId).Distinct().Count() > 1)
        {
            await SendEmailReplyOnBadFromAddress(emailMessage, "05", "Multiple accounts have the same email address.");
            return EntityResponse<Guid>.Success(Guid.NewGuid());
        }

        return EntityResponse<Guid>.Success(Guid.NewGuid());
    }

    private async Task SendEmailReplyOnBadFromAddress(
        EmailMessageDto emailMessage,
        string notificationType,
        string defaultBody
        )
    {
        var httpResponseEmailTemplate = await ExternalRequestUtils.Send(
                httpClientFactory,
                configuration["BackendsList:BaseBiz"]! + $"/GetNotificationTemplateByNotificationTypeAndNotificationMethod?notificationType={notificationType}&notificationMethod=1",
                "GET",
                ""
                );
        var entityResponseEmailTemplate = JsonSerializer.Deserialize<EntityResponse<NotificationTemplateDto?>>(
            await httpResponseEmailTemplate.ReadAsStringAsync(),
            new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

        var emailTemplate = entityResponseEmailTemplate!.Entity?.Body ?? defaultBody;
        var stubble = new StubbleBuilder().Build();
        var emailBody = await stubble.RenderAsync(
            emailTemplate,
            new
            {
                senderEmail = emailMessage.From,
            });

        await MicrosoftGraphEmailUtilities.SendReply(
            configuration["GraphMail:TenantId"]!,
            configuration["GraphMail:ClientId"]!,
            configuration["GraphMail:ClientSecret"]!,
            configuration["EmailScanner:SupportEmailAddress"]!,
            emailMessage.EmailId!,
            emailBody
            );
    }

    public async Task<EntityResponse<Guid>> UpdateTicketAsync(TicketDto ticketDto,
        CancellationToken cancellationToken = default)
    {
        return await _ticketCrudHelper.UpdateEntity(GetCurrentUser(), ticketDto, cancellationToken);
    }

    public async Task<EntityResponsePaged<TicketListItemDto>> QueryTicketAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        EntityResponsePaged<TicketListItemDto> result = await _ticketCrudHelper.GetEntityPaged(request, cancellationToken, exactMatch(request));
        if (result.Entity != null)
        {
            for(int i = 0; i < result.Entity.Count; i++)
            {
                HttpContent httpResponse =
                    await ExternalRequestUtils.Send(httpClientFactory,
                        configuration["BackendsList:Project"]! + "/External/GetWorkTimeByTicketId?ticketId=" + result.Entity[i].Id,
                        "GET", "");
                JToken json = JObject.Parse(await httpResponse.ReadAsStringAsync())["entity"]!;
                result.Entity[i].WorkMinutes = short.Parse(json.ToString());
            }
            // result.Entity.ForEach(async t =>
            // {
            //     HttpContent httpResponse =
            //         await ExternalRequestUtils.Send(httpClientFactory,
            //             configuration["BackendsList:Project"]! + "/External/GetWorkTimeByTicketId?ticketId=" + t.Id,
            //             "GET", "");
            //     //JToken json = JObject.Parse(await httpResponse.ReadAsStringAsync())["entity"]!;
            //     //t.WorkMinutes = short.Parse(json.ToString());
            //     t.WorkMinutes = short.Parse(JObject.Parse(await httpResponse.ReadAsStringAsync())["entity"]!.ToString());
            // });
        }
        return result;
    }

    public async Task<EntityResponse> AddTicketsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _ticketCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _ticketCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region TicketReview

    public async Task<EntityResponse<TicketReviewDto>> GetTicketReviewByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketReviewCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketReviewAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketReviewCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketReviewAsync(TicketReviewDto ticketReviewDto,
        CancellationToken cancellationToken = default)
    {
        return await _ticketReviewCrudHelper.AddEntity(GetCurrentUser(), ticketReviewDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketReviewAsync(TicketReviewDto ticketReviewDto,
        CancellationToken cancellationToken = default)
    {
        return await _ticketReviewCrudHelper.UpdateEntity(GetCurrentUser(), ticketReviewDto, cancellationToken);
    }

    public async Task<EntityResponsePaged<TicketReviewListItemDto>> QueryTicketReviewAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketReviewCrudHelper.GetEntityPaged(request, cancellationToken, t => true);
    }

    public async Task<EntityResponse> AddTicketReviewsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket reviews in bulk
        return await _ticketReviewCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketReviewsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket reviews in bulk
        return await _ticketReviewCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketReviewsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket reviews in bulk
        return await _ticketReviewCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketReviewExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketReviewCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketReviewExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _ticketReviewCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region TicketLink

    public async Task<EntityResponse<TicketLinkDto>> GetTicketLinkByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketLinkCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketLinkAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketLinkCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketLinkAsync(TicketLinkDto ticketLinkDto,
        CancellationToken cancellationToken = default)
    {
        return await _ticketLinkCrudHelper.AddEntity(GetCurrentUser(), ticketLinkDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketLinkAsync(TicketLinkDto ticketLinkDto,
        CancellationToken cancellationToken = default)
    {
        return await _ticketLinkCrudHelper.UpdateEntity(GetCurrentUser(), ticketLinkDto, cancellationToken);
    }

    public async Task<EntityResponsePaged<TicketLinkListItemDto>> QueryTicketLinkAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketLinkCrudHelper.GetEntityPaged(request, cancellationToken, t => true);
    }

    public async Task<EntityResponse> AddTicketLinksAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket links in bulk
        return await _ticketLinkCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketLinksAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket links in bulk
        return await _ticketLinkCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketLinksAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket links in bulk
        return await _ticketLinkCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketLinkExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketLinkCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketLinkExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _ticketLinkCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region TicketDevOpsLink

    public async Task<EntityResponse<TicketDevOpsLinkDto>> GetTicketDevOpsLinkByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDevOpsLinkCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketDevOpsLinkAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDevOpsLinkCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketDevOpsLinkAsync(TicketDevOpsLinkDto ticketDevOpsLinkDto,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDevOpsLinkCrudHelper.AddEntity(GetCurrentUser(), ticketDevOpsLinkDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketDevOpsLinkAsync(TicketDevOpsLinkDto ticketDevOpsLinkDto,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDevOpsLinkCrudHelper.UpdateEntity(GetCurrentUser(), ticketDevOpsLinkDto, cancellationToken);
    }

    public async Task<EntityResponsePaged<TicketDevOpsLinkListItemDto>> QueryTicketDevOpsLinkAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDevOpsLinkCrudHelper.GetEntityPaged(request, cancellationToken, t => true);
    }

    public async Task<EntityResponse> AddTicketDevOpsLinksAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket devops links in bulk
        return await _ticketDevOpsLinkCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketDevOpsLinksAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket devops links in bulk
        return await _ticketDevOpsLinkCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketDevOpsLinksAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket devops links in bulk
        return await _ticketDevOpsLinkCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketDevOpsLinkExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDevOpsLinkCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketDevOpsLinkExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDevOpsLinkCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region TicketBillingPayment

    public async Task<EntityResponse<TicketBillingPaymentDto>> GetTicketBillingPaymentByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingPaymentCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketBillingPaymentAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingPaymentCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketBillingPaymentAsync(
        TicketBillingPaymentDto ticketBillingPaymentDto, CancellationToken cancellationToken = default)
    {
        return await _ticketBillingPaymentCrudHelper.AddEntity(GetCurrentUser(), ticketBillingPaymentDto,
            cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketBillingPaymentAsync(
        TicketBillingPaymentDto ticketBillingPaymentDto, CancellationToken cancellationToken = default)
    {
        return await _ticketBillingPaymentCrudHelper.UpdateEntity(GetCurrentUser(), ticketBillingPaymentDto,
            cancellationToken);
    }

    public async Task<EntityResponsePaged<TicketBillingPaymentListItemDto>> QueryTicketBillingPaymentAsync(
        BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _ticketBillingPaymentCrudHelper.GetEntityPaged(request, cancellationToken, t => true);
    }

    public async Task<EntityResponse> AddTicketBillingPaymentsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket billing payments in bulk
        return await _ticketBillingPaymentCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketBillingPaymentsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket billing payments in bulk
        return await _ticketBillingPaymentCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketBillingPaymentsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket billing payments in bulk
        return await _ticketBillingPaymentCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketBillingPaymentExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingPaymentCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketBillingPaymentExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingPaymentCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region TicketBillingDelivery

    public async Task<EntityResponse<TicketBillingDeliveryDto>> GetTicketBillingDeliveryByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingDeliveryCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketBillingDeliveryAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingDeliveryCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketBillingDeliveryAsync(
        TicketBillingDeliveryDto ticketBillingDeliveryDto, CancellationToken cancellationToken = default)
    {
        return await _ticketBillingDeliveryCrudHelper.AddEntity(GetCurrentUser(), ticketBillingDeliveryDto,
            cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketBillingDeliveryAsync(
        TicketBillingDeliveryDto ticketBillingDeliveryDto, CancellationToken cancellationToken = default)
    {
        return await _ticketBillingDeliveryCrudHelper.UpdateEntity(GetCurrentUser(), ticketBillingDeliveryDto,
            cancellationToken);
    }

    public async Task<EntityResponsePaged<TicketBillingDeliveryListItemDto>> QueryTicketBillingDeliveryAsync(
        BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _ticketBillingDeliveryCrudHelper.GetEntityPaged(request, cancellationToken, t => true);
    }

    public async Task<EntityResponse> AddTicketBillingDeliveriesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket billing deliveries in bulk
        return await _ticketBillingDeliveryCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketBillingDeliveriesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket billing deliveries in bulk
        return await _ticketBillingDeliveryCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketBillingDeliveriesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket billing deliveries in bulk
        return await _ticketBillingDeliveryCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketBillingDeliveryExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingDeliveryCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketBillingDeliveryExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingDeliveryCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region TicketBilling

    public async Task<EntityResponse<TicketBillingDto>> GetTicketBillingByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketBillingAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketBillingAsync(TicketBillingDto ticketBillingDto,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingCrudHelper.AddEntity(GetCurrentUser(), ticketBillingDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketBillingAsync(TicketBillingDto ticketBillingDto,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingCrudHelper.UpdateEntity(GetCurrentUser(), ticketBillingDto, cancellationToken);
    }

    public async Task<EntityResponsePaged<TicketBillingListItemDto>> QueryTicketBillingAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingCrudHelper.GetEntityPaged(request, cancellationToken, t => true);
    }

    public async Task<EntityResponse> AddTicketBillingsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket billings in bulk
        return await _ticketBillingCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketBillingsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket billings in bulk
        return await _ticketBillingCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketBillingsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket billings in bulk
        return await _ticketBillingCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketBillingExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketBillingExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _ticketBillingCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region Ticket Discussion

    public async Task<EntityResponse<TicketDiscussionDto>> GetTicketDiscussionByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDiscussionCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketDiscussionAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDiscussionCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketDiscussionAsync(TicketDiscussionDto ticketDiscussionDto,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDiscussionCrudHelper.AddEntity(GetCurrentUser(), ticketDiscussionDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketDiscussionAsync(TicketDiscussionDto ticketDiscussionDto,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDiscussionCrudHelper.UpdateEntity(GetCurrentUser(), ticketDiscussionDto, cancellationToken);
    }

    public async Task<EntityResponsePaged<TicketDiscussionListItemDto>> QueryTicketDiscussionAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDiscussionCrudHelper.GetEntityPaged(request, cancellationToken, t => true);
    }

    public async Task<EntityResponse> AddTicketDiscussionsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket discussions in bulk
        return await _ticketDiscussionCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketDiscussionsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket discussions in bulk
        return await _ticketDiscussionCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketDiscussionsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket discussions in bulk
        return await _ticketDiscussionCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketDiscussionExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDiscussionCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketDiscussionExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _ticketDiscussionCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region TicketDocument
    public async Task<EntityResponse<TicketDocumentDto>> GetTicketDocumentByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _ticketDocumentCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketDocumentAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _ticketDocumentCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketDocumentAsync(TicketDocumentDto ticketDocumentDto, CancellationToken cancellationToken = default)
    {
        return await _ticketDocumentCrudHelper.AddEntity(GetCurrentUser(), ticketDocumentDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketDocumentAsync(TicketDocumentDto ticketDocumentDto, CancellationToken cancellationToken = default)
    {
        return await _ticketDocumentCrudHelper.UpdateEntity(GetCurrentUser(), ticketDocumentDto, cancellationToken);
    }

    public async Task<EntityResponsePaged<TicketDocumentListItemDto>> QueryTicketDocumentAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _ticketDocumentCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse> AddTicketDocumentsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _ticketDocumentCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketDocumentsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _ticketDocumentCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketDocumentsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _ticketDocumentCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketDocumentExcel(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _ticketDocumentCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketDocumentExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _ticketDocumentCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }
    #endregion
}