﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.Ticket.BusinessLogic.DTOs;

public partial class ProductLicenseTrackingDto : CodeDto
{
    public Guid ProductId { get; set; }

    public string ClientId { get; set; } = null!;

    public int? MaterialManager { get; set; }

    public int? MaterialManagerViewOnly { get; set; }

    public int? PollManager { get; set; }

    public int? ScaleManager { get; set; }

    public int? TouchScreen { get; set; }

    public int? TouchScreenPay { get; set; }

    public int? CraneStationManager { get; set; }

    public string? LicenseType { get; set; }

    public int? MaxUsers { get; set; }

    public int? MaxProjects { get; set; }

    public string? Status { get; set; }

    public string? ModulesEnabled { get; set; }

    public DateTime? EffectiveDate { get; set; }

    public DateTime? ExpireDate { get; set; }

    public string? Description { get; set; }
}

