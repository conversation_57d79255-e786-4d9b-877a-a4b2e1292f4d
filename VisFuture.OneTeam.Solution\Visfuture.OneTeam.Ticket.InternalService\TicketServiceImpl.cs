using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Ticket.BusinessLogic.DTOs;
using Visfuture.OneTeam.Ticket.BusinessLogic.Interfaces;
using Visfuture.OneTeam.Ticket.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.Ticket.InternalService.interfaces;

namespace Visfuture.OneTeam.Ticket.InternalService;

public class TicketServiceImpl(ITicketManager ticketManager) : ITicketService
{
    private readonly ITicketManager ticketManager = ticketManager;

    #region Ticket
    public async Task<EntityResponsePaged<TicketListItemDto>> QueryTicketAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.QueryTicketAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<TicketDto>> GetTicketByIdAsync(Guid ticketId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.GetTicketByIdAsync(ticketId, cancellationToken);
    }

    public async Task<EntityResponse<TicketDto>> GetTicketByNoAsync(string ticketNo, CancellationToken cancellationToken = default)
    {
        return await ticketManager.GetTicketByNoAsync(ticketNo, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketAsync(TicketDto ticketDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.AddTicketAsync(ticketDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketFromEmailScannerAsync(EmailMessageDto emailMessage, CancellationToken cancellationToken = default)
    {
        return await ticketManager.AddTicketFromEmailScannerAsync(emailMessage, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketAsync(TicketDto ticketDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.UpdateTicketAsync(ticketDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketAsync(Guid ticketId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.DeleteTicketAsync(ticketId, cancellationToken);
    }

    public async Task<EntityResponse> AddTicketsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.AddTicketsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.UpdateTicketsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.DeleteTicketsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketExcel(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ExportTicketExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ImportTicketExcel(file, cancellationToken);
    }
    #endregion

    #region TicketReview
    public async Task<EntityResponsePaged<TicketReviewListItemDto>> QueryTicketReviewAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.QueryTicketReviewAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<TicketReviewDto>> GetTicketReviewByIdAsync(Guid ticketReviewId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.GetTicketReviewByIdAsync(ticketReviewId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketReviewAsync(TicketReviewDto ticketReviewDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.AddTicketReviewAsync(ticketReviewDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketReviewAsync(TicketReviewDto ticketReviewDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.UpdateTicketReviewAsync(ticketReviewDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketReviewAsync(Guid ticketReviewId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.DeleteTicketReviewAsync(ticketReviewId, cancellationToken);
    }

    public async Task<EntityResponse> AddTicketReviewsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket reviews in bulk
        return await ticketManager.AddTicketReviewsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketReviewsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket reviews in bulk
        return await ticketManager.UpdateTicketReviewsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketReviewsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket reviews in bulk
        return await ticketManager.DeleteTicketReviewsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketReviewExcel(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ExportTicketReviewExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketReviewExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ImportTicketReviewExcel(file, cancellationToken);
    }
    #endregion

    #region TicketLink
    public async Task<EntityResponsePaged<TicketLinkListItemDto>> QueryTicketLinkAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.QueryTicketLinkAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<TicketLinkDto>> GetTicketLinkByIdAsync(Guid ticketLinkId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.GetTicketLinkByIdAsync(ticketLinkId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketLinkAsync(TicketLinkDto ticketLinkDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.AddTicketLinkAsync(ticketLinkDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketLinkAsync(TicketLinkDto ticketLinkDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.UpdateTicketLinkAsync(ticketLinkDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketLinkAsync(Guid ticketLinkId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.DeleteTicketLinkAsync(ticketLinkId, cancellationToken);
    }

    public async Task<EntityResponse> AddTicketLinksAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket links in bulk
        return await ticketManager.AddTicketLinksAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketLinksAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket links in bulk
        return await ticketManager.UpdateTicketLinksAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketLinksAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket links in bulk
        return await ticketManager.DeleteTicketLinksAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketLinkExcel(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ExportTicketLinkExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketLinkExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ImportTicketLinkExcel(file, cancellationToken);
    }
    #endregion

    #region TicketDevOpsLink
    public async Task<EntityResponsePaged<TicketDevOpsLinkListItemDto>> QueryTicketDevOpsLinkAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.QueryTicketDevOpsLinkAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<TicketDevOpsLinkDto>> GetTicketDevOpsLinkByIdAsync(Guid ticketDevOpsLinkId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.GetTicketDevOpsLinkByIdAsync(ticketDevOpsLinkId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketDevOpsLinkAsync(TicketDevOpsLinkDto ticketDevOpsLinkDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.AddTicketDevOpsLinkAsync(ticketDevOpsLinkDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketDevOpsLinkAsync(TicketDevOpsLinkDto ticketDevOpsLinkDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.UpdateTicketDevOpsLinkAsync(ticketDevOpsLinkDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketDevOpsLinkAsync(Guid ticketDevOpsLinkId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.DeleteTicketDevOpsLinkAsync(ticketDevOpsLinkId, cancellationToken);
    }

    public async Task<EntityResponse> AddTicketDevOpsLinksAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket devops links in bulk
        return await ticketManager.AddTicketDevOpsLinksAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketDevOpsLinksAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket devops links in bulk
        return await ticketManager.UpdateTicketDevOpsLinksAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketDevOpsLinksAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket devops links in bulk
        return await ticketManager.DeleteTicketDevOpsLinksAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketDevOpsLinkExcel(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ExportTicketDevOpsLinkExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketDevOpsLinkExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ImportTicketDevOpsLinkExcel(file, cancellationToken);
    }
    #endregion

    #region TicketBillingPayment
    public async Task<EntityResponsePaged<TicketBillingPaymentListItemDto>> QueryTicketBillingPaymentAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.QueryTicketBillingPaymentAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<TicketBillingPaymentDto>> GetTicketBillingPaymentByIdAsync(Guid ticketBillingPaymentId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.GetTicketBillingPaymentByIdAsync(ticketBillingPaymentId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketBillingPaymentAsync(TicketBillingPaymentDto ticketBillingPaymentDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.AddTicketBillingPaymentAsync(ticketBillingPaymentDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketBillingPaymentAsync(TicketBillingPaymentDto ticketBillingPaymentDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.UpdateTicketBillingPaymentAsync(ticketBillingPaymentDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketBillingPaymentAsync(Guid ticketBillingPaymentId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.DeleteTicketBillingPaymentAsync(ticketBillingPaymentId, cancellationToken);
    }

    public async Task<EntityResponse> AddTicketBillingPaymentsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket billing payments in bulk
        return await ticketManager.AddTicketBillingPaymentsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketBillingPaymentsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket billing payments in bulk
        return await ticketManager.UpdateTicketBillingPaymentsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketBillingPaymentsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket billing payments in bulk
        return await ticketManager.DeleteTicketBillingPaymentsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketBillingPaymentExcel(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ExportTicketBillingPaymentExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketBillingPaymentExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ImportTicketBillingPaymentExcel(file, cancellationToken);
    }
    #endregion

    #region TicketBillingDelivery
    public async Task<EntityResponsePaged<TicketBillingDeliveryListItemDto>> QueryTicketBillingDeliveryAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.QueryTicketBillingDeliveryAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<TicketBillingDeliveryDto>> GetTicketBillingDeliveryByIdAsync(Guid ticketBillingDeliveryId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.GetTicketBillingDeliveryByIdAsync(ticketBillingDeliveryId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketBillingDeliveryAsync(TicketBillingDeliveryDto ticketBillingDeliveryDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.AddTicketBillingDeliveryAsync(ticketBillingDeliveryDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketBillingDeliveryAsync(TicketBillingDeliveryDto ticketBillingDeliveryDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.UpdateTicketBillingDeliveryAsync(ticketBillingDeliveryDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketBillingDeliveryAsync(Guid ticketBillingDeliveryId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.DeleteTicketBillingDeliveryAsync(ticketBillingDeliveryId, cancellationToken);
    }

    public async Task<EntityResponse> AddTicketBillingDeliveriesAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket billing deliveries in bulk
        return await ticketManager.AddTicketBillingDeliveriesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketBillingDeliveriesAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket billing deliveries in bulk
        return await ticketManager.UpdateTicketBillingDeliveriesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketBillingDeliveriesAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket billing deliveries in bulk
        return await ticketManager.DeleteTicketBillingDeliveriesAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketBillingDeliveryExcel(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ExportTicketBillingDeliveryExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketBillingDeliveryExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ImportTicketBillingDeliveryExcel(file, cancellationToken);
    }
    #endregion

    #region TicketBilling
    public async Task<EntityResponsePaged<TicketBillingListItemDto>> QueryTicketBillingAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.QueryTicketBillingAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<TicketBillingDto>> GetTicketBillingByIdAsync(Guid ticketBillingId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.GetTicketBillingByIdAsync(ticketBillingId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketBillingAsync(TicketBillingDto ticketBillingDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.AddTicketBillingAsync(ticketBillingDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketBillingAsync(TicketBillingDto ticketBillingDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.UpdateTicketBillingAsync(ticketBillingDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketBillingAsync(Guid ticketBillingId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.DeleteTicketBillingAsync(ticketBillingId, cancellationToken);
    }

    public async Task<EntityResponse> AddTicketBillingsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket billings in bulk
        return await ticketManager.AddTicketBillingsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketBillingsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket billings in bulk
        return await ticketManager.UpdateTicketBillingsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketBillingsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket billings in bulk
        return await ticketManager.DeleteTicketBillingsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketBillingExcel(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ExportTicketBillingExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketBillingExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ImportTicketBillingExcel(file, cancellationToken);
    }
    #endregion

    #region Ticket Discussion
    public async Task<EntityResponsePaged<TicketDiscussionListItemDto>> QueryTicketDiscussionAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.QueryTicketDiscussionAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<TicketDiscussionDto>> GetTicketDiscussionByIdAsync(Guid ticketDiscussionId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.GetTicketDiscussionByIdAsync(ticketDiscussionId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketDiscussionAsync(TicketDiscussionDto ticketDiscussionDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.AddTicketDiscussionAsync(ticketDiscussionDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketDiscussionAsync(TicketDiscussionDto ticketDiscussionDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.UpdateTicketDiscussionAsync(ticketDiscussionDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketDiscussionAsync(Guid ticketDiscussionId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.DeleteTicketDiscussionAsync(ticketDiscussionId, cancellationToken);
    }

    public async Task<EntityResponse> AddTicketDiscussionsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for adding ticket discussions in bulk
        return await ticketManager.AddTicketDiscussionsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketDiscussionsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for updating ticket discussions in bulk
        return await ticketManager.UpdateTicketDiscussionsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketDiscussionsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        // Implementation for deleting ticket discussions in bulk
        return await ticketManager.DeleteTicketDiscussionsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketDiscussionExcel(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ExportTicketDiscussionExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketDiscussionExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ImportTicketDiscussionExcel(file, cancellationToken);
    }
    #endregion

    #region TicketDocument
    public async Task<EntityResponsePaged<TicketDocumentListItemDto>> QueryTicketDocumentAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.QueryTicketDocumentAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<TicketDocumentDto>> GetTicketDocumentByIdAsync(Guid ticketDocumentId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.GetTicketDocumentByIdAsync(ticketDocumentId, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTicketDocumentAsync(TicketDocumentDto ticketDocumentDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.AddTicketDocumentAsync(ticketDocumentDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTicketDocumentAsync(TicketDocumentDto ticketDocumentDto, CancellationToken cancellationToken = default)
    {
        return await ticketManager.UpdateTicketDocumentAsync(ticketDocumentDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTicketDocumentAsync(Guid ticketDocumentId, CancellationToken cancellationToken = default)
    {
        return await ticketManager.DeleteTicketDocumentAsync(ticketDocumentId, cancellationToken);
    }

    public async Task<EntityResponse> AddTicketDocumentsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.AddTicketDocumentsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTicketDocumentsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.UpdateTicketDocumentsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTicketDocumentsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.DeleteTicketDocumentsAsync(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTicketDocumentExcel(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ExportTicketDocumentExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTicketDocumentExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await ticketManager.ImportTicketDocumentExcel(file, cancellationToken);
    }
    #endregion
}
