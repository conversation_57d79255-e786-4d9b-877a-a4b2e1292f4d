﻿using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.InternalService.Interfaces;

public interface IMiscService
{
    Task<EntityResponse<B64File>> ExportExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportExcel(B64File file, CancellationToken cancellationToken = default);

    #region I18nKey

    Task<EntityResponse<I18nKeyDto>> GetI18nKeyByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddI18nKeyAsync(I18nKeyDto i18nKeyDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateI18nKeyAsync(I18nKeyDto i18nKeyDto, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteI18nKeyAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponsePaged<I18nKeyListItemDto>> QueryI18nKeyAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddI18nKeysAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateI18nKeysAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteI18nKeysAsync(BaseQuery request, CancellationToken cancellationToken = default);

    #endregion

    #region I18nTranslation

    Task<EntityResponse<I18nTranslationDto>> GetI18nTranslationByIdAsync(Guid id,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddI18nTranslationAsync(I18nTranslationDto i18nTranslationDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateI18nTranslationAsync(I18nTranslationDto i18nTranslationDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteI18nTranslationAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponsePaged<I18nTranslationListItemDto>> QueryI18nTranslationAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddI18nTranslationsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateI18nTranslationsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteI18nTranslationsAsync(BaseQuery request, CancellationToken cancellationToken = default);

    #endregion

    #region NotificationTemplate

    Task<EntityResponse<NotificationTemplateDto>> GetNotificationTemplateByIdAsync(Guid id,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddNotificationTemplateAsync(NotificationTemplateDto notificationTemplateDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateNotificationTemplateAsync(NotificationTemplateDto notificationTemplateDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteNotificationTemplateAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponsePaged<NotificationTemplateListItemDto>> QueryNotificationTemplateAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse>
        AddNotificationTemplatesAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse> UpdateNotificationTemplatesAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> DeleteNotificationTemplatesAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<NotificationTemplateDto?>> GetNotificationTemplateByNotificationTypeAndNotificationMethod(
        string notificationType,
        string notificationMethod,
        CancellationToken cancellationToken = default
        );

    #endregion

    #region SequenceNo

    Task<EntityResponse<SequenceNoDto>> GetSequenceNoByIdAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponse<SequenceNoDto>> GetSequenceNoByNameAsync(string name,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddSequenceNoAsync(SequenceNoDto sequenceNoDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateSequenceNoAsync(SequenceNoDto sequenceNoDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteSequenceNoAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponsePaged<SequenceNoListItemDto>> QuerySequenceNoAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddSequenceNosAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateSequenceNosAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteSequenceNosAsync(BaseQuery request, CancellationToken cancellationToken = default);

    #endregion

    #region CodeItem

    Task<EntityResponse<CodeItemDto>> GetCodeItemByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddCodeItemAsync(CodeItemDto codeItemDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateCodeItemAsync(CodeItemDto codeItemDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteCodeItemAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponsePaged<CodeItemListItemDto>> QueryCodeItemAsync(CodeItemRequest request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddCodeItemsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateCodeItemsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteCodeItemsAsync(BaseQuery request, CancellationToken cancellationToken = default);

    #endregion

    #region CodeItemAttribute

    Task<EntityResponse<CodeItemAttributeDto>> GetCodeItemAttributeByIdAsync(Guid id,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddCodeItemAttributeAsync(CodeItemAttributeDto codeItemAttributeDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateCodeItemAttributeAsync(CodeItemAttributeDto codeItemAttributeDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteCodeItemAttributeAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponsePaged<CodeItemAttributeListItemDto>> QueryCodeItemAttributeAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddCodeItemAttributesAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse>
        UpdateCodeItemAttributesAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse>
        DeleteCodeItemAttributesAsync(BaseQuery request, CancellationToken cancellationToken = default);

    #endregion

    #region CodeType

    Task<EntityResponse<CodeTypeDto>> GetCodeTypeByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EntityResponse<CodeTypeDto>> GetCodeTypeByTypeCodeAsync(string typeCode, CancellationToken cancellationToken);
    Task<EntityResponse<Guid>> AddCodeTypeAsync(CodeTypeDto codeTypeDto, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateCodeTypeAsync(CodeTypeDto codeTypeDto,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteCodeTypeAsync(Guid id, CancellationToken cancellationToken = default);

    Task<EntityResponsePaged<CodeTypeListItemDto>> QueryCodeTypeAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddCodeTypesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateCodeTypesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteCodeTypesAsync(BaseQuery request, CancellationToken cancellationToken = default);

    #endregion
}