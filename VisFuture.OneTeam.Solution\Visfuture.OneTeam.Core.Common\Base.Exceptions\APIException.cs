﻿
using System.Globalization;


namespace Visfuture.OneTeam.Core.Common.Base.Exceptions;
public class APIException : Exception
{
    public APIException() :
        base(/*Resources.Messages.ServerFailed*/)   // TODO: Uncomment this line after adding Resources
    {
    }
    public APIException(string message) :
        base(message)
    {
    }

    public APIException(string message, params object[] args) : base(String.Format(CultureInfo.CurrentCulture, message, args)) { }
}
