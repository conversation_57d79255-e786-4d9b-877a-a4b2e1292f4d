﻿using MapsterMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Interfaces;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.BaseBiz.DataAccess;
using Visfuture.OneTeam.BaseBiz.DataAccess.DbContext;
using Visfuture.OneTeam.BaseBiz.DataAccess.Entities;
using Visfuture.OneTeam.Core.Common.Base.Models;
using Visfuture.OneTeam.Core.Common.Helpers;
using Mapster;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic;

public class OrgManager(
    IConfiguration configuration,
    IHttpClientFactory httpClientFactory,
    IHttpContextAccessor httpContextAccessor,
    AppDataContext appDataContext,
    IMapper mapper) : BaseBizBaseManager(httpContextAccessor, appDataContext, mapper), IOrgManager
{
    private readonly CrudHelper<Employee, EmployeeDto, EmployeeListItemDto> _employeeCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.Employees);

    private readonly CrudHelper<GlobalAdmin, GlobalAdminDto, GlobalAdminListItemDto> _globalAdminCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.GlobalAdmins);

    private readonly CrudHelper<OrganizationEmployee, OrganizationEmployeeDto, OrganizationEmployeeListItemDto>
        _organizationEmployeeCrudHelper =
            new(configuration, httpClientFactory, appDataContext, appDataContext.OrganizationEmployees);

    private readonly CrudHelper<OrganizationHierarchy, OrganizationHierarchyDto, OrganizationHierarchyListItemDto>
        _organizationHierarchyCrudHelper =
            new(configuration, httpClientFactory, appDataContext, appDataContext.OrganizationHierarchies);

    private readonly CrudHelper<Tenant, TenantDto, TenantListItemDto> _tenantCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.Tenants);

    private readonly CrudHelper<UserAccount, UserAccountDto, UserAccountListItemDto> _userAccountCrudHelper =
        new(configuration, httpClientFactory, appDataContext, appDataContext.UserAccounts);

    #region Employee

    public async Task<EntityResponse<EmployeeDto>> GetEmployeeByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _employeeCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddEmployeeAsync(EmployeeDto employeeDto,
        CancellationToken cancellationToken = default)
    {
        return await _employeeCrudHelper.AddEntity(GetCurrentUser(), employeeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateEmployeeAsync(EmployeeDto employeeDto,
        CancellationToken cancellationToken = default)
    {
        return await _employeeCrudHelper.UpdateEntity(GetCurrentUser(), employeeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteEmployeeAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _employeeCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<EmployeeListItemDto>> QueryEmployeeAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _employeeCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse> AddEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _employeeCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _employeeCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _employeeCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportEmployeeExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _employeeCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportEmployeeExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _employeeCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region GlobalAdmin

    public async Task<EntityResponse<GlobalAdminDto>> GetGlobalAdminByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _globalAdminCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddGlobalAdminAsync(GlobalAdminDto globalAdminDto,
        CancellationToken cancellationToken = default)
    {
        return await _globalAdminCrudHelper.AddEntity(GetCurrentUser(), globalAdminDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateGlobalAdminAsync(GlobalAdminDto globalAdminDto,
        CancellationToken cancellationToken = default)
    {
        return await _globalAdminCrudHelper.UpdateEntity(GetCurrentUser(), globalAdminDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteGlobalAdminAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _globalAdminCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<GlobalAdminListItemDto>> QueryGlobalAdminAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _globalAdminCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse> AddGlobalAdminsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _globalAdminCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateGlobalAdminsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _globalAdminCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteGlobalAdminsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _globalAdminCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportGlobalAdminExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _globalAdminCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportGlobalAdminExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _globalAdminCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region OrganizationEmployee

    public async Task<EntityResponsePaged<EmployeeListItemDto>> QueryEmployeesByOrganizationAsync(BaseQuery<EmployeeDto> request, CancellationToken cancellationToken = default)
    {
        var organizationId = request.FieldValues?["organizationId"]?.FirstOrDefault();
        if (string.IsNullOrWhiteSpace(organizationId))
        {
            return new EntityResponsePaged<EmployeeListItemDto>
            {
                Entity = new List<EmployeeListItemDto>(),
                Total = 0,
                IsSuccess = false,
                Message = "organizationId is required."
            };
        }

        Guid orgGuid = Guid.Parse(organizationId);

        var allOrgEmployeeIds = await DB.OrganizationEmployees
            .Select(oe => oe.EmployeeId)
            .ToListAsync(cancellationToken);

        var unassignedEmployees = await DB.Employees
            .Where(e => !allOrgEmployeeIds.Contains(e.Id)) // Exclude all already assigned
            .ProjectToType<EmployeeListItemDto>()
            .ToListAsync(cancellationToken);

        return new EntityResponsePaged<EmployeeListItemDto>
        {
            Entity = unassignedEmployees,
            Total = unassignedEmployees.Count,
            IsSuccess = true
        };
    }
    public async Task<EntityResponse<OrganizationEmployeeDto>> GetOrganizationEmployeeByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _organizationEmployeeCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddOrganizationEmployeeAsync(
        OrganizationEmployeeDto organizationEmployeeDto, CancellationToken cancellationToken = default)
    {
        // Check if already exists
        bool exists = await DB.OrganizationEmployees
            .AnyAsync(oe => oe.EmployeeId == organizationEmployeeDto.EmployeeId, cancellationToken);

        if (exists)
        {
            return EntityResponse<Guid>.Failed("Employee already assigned to this organization.");
        }

        return await _organizationEmployeeCrudHelper.AddEntity(GetCurrentUser(), organizationEmployeeDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateOrganizationEmployeeAsync(
        OrganizationEmployeeDto organizationEmployeeDto, CancellationToken cancellationToken = default)
    {
        // Check if already exists
        bool exists = await DB.OrganizationEmployees
            .AnyAsync(oe => oe.EmployeeId == organizationEmployeeDto.EmployeeId, cancellationToken);

        if (exists)
        {
            return EntityResponse<Guid>.Failed("Employee already assigned to this organization.");
        }
        return await _organizationEmployeeCrudHelper.UpdateEntity(GetCurrentUser(), organizationEmployeeDto,
            cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteOrganizationEmployeeAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _organizationEmployeeCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<OrganizationEmployeeListItemDto>> QueryOrganizationEmployeeAsync(
    BaseQuery request, CancellationToken cancellationToken = default)
    {
        var orgIdRaw = request.FieldValues?["organizationId"]?.FirstOrDefault();
        if (string.IsNullOrEmpty(orgIdRaw) || !Guid.TryParse(orgIdRaw, out var organizationId))
        {
            return new EntityResponsePaged<OrganizationEmployeeListItemDto>
            {
                IsSuccess = false,
                Message = "Missing or invalid organizationId",
                Entity = new List<OrganizationEmployeeListItemDto>(),
                Total = 0
            };
        }

        var list = await DB.OrganizationEmployees
            .Include(oe => oe.Employee)
            .Where(oe => oe.OrganizationId == organizationId)
            .AsNoTracking()
            .Select(oe => new OrganizationEmployeeListItemDto
            {
                Id = oe.Id,
                OrganizationId = oe.OrganizationId,
                EmployeeId = oe.EmployeeId,
                TenantId = oe.TenantId,
                EmployeeName = oe.Employee.Name,
                CreateAt = oe.CreateAt,
                CreateBy = oe.CreateBy,
                UpdateAt = oe.UpdateAt,
                UpdateBy = oe.UpdateBy,
            })
            .ToListAsync(cancellationToken);

        return new EntityResponsePaged<OrganizationEmployeeListItemDto>
        {
            Entity = list,
            Total = list.Count,
            IsSuccess = true
        };
    }

    public async Task<EntityResponse> AddOrganizationEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _organizationEmployeeCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateOrganizationEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _organizationEmployeeCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteOrganizationEmployeesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _organizationEmployeeCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportOrganizationEmployeeExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _organizationEmployeeCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportOrganizationEmployeeExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _organizationEmployeeCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region OrganizationHierarchy

    public async Task<EntityResponse<OrganizationHierarchyDto>> GetOrganizationHierarchyByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _organizationHierarchyCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddOrganizationHierarchyAsync(
        OrganizationHierarchyDto organizationHierarchyDto, CancellationToken cancellationToken = default)
    {
        return await _organizationHierarchyCrudHelper.AddEntity(GetCurrentUser(), organizationHierarchyDto,
            cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateOrganizationHierarchyAsync(
        OrganizationHierarchyDto organizationHierarchyDto, CancellationToken cancellationToken = default)
    {
        return await _organizationHierarchyCrudHelper.UpdateEntity(GetCurrentUser(), organizationHierarchyDto,
            cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteOrganizationHierarchyAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _organizationHierarchyCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<OrganizationHierarchyListItemDto>> QueryOrganizationHierarchyAsync(
        BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _organizationHierarchyCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse> AddOrganizationHierarchiesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _organizationHierarchyCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateOrganizationHierarchiesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _organizationHierarchyCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteOrganizationHierarchiesAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _organizationHierarchyCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportOrganizationHierarchyExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _organizationHierarchyCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportOrganizationHierarchyExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _organizationHierarchyCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region Tenant

    public async Task<EntityResponse<TenantDto>> GetTenantByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _tenantCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddTenantAsync(TenantDto tenantDto,
        CancellationToken cancellationToken = default)
    {
        return await _tenantCrudHelper.AddEntity(GetCurrentUser(), tenantDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateTenantAsync(TenantDto tenantDto,
        CancellationToken cancellationToken = default)
    {
        return await _tenantCrudHelper.UpdateEntity(GetCurrentUser(), tenantDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteTenantAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _tenantCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<TenantListItemDto>> QueryTenantAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _tenantCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse> AddTenantsAsync(BaseQuery request, CancellationToken cancellationToken = default)
    {
        return await _tenantCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateTenantsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _tenantCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteTenantsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _tenantCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportTenantExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _tenantCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportTenantExcel(B64File file, CancellationToken cancellationToken = default)
    {
        return await _tenantCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion

    #region User Accounts

    public async Task<EntityResponse<UserAccountDto>> GetUserAccountByIdAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _userAccountCrudHelper.GetEntity(id, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> AddUserAccountAsync(UserAccountDto userAccountDto,
        CancellationToken cancellationToken = default)
    {
        return await _userAccountCrudHelper.AddEntity(GetCurrentUser(), userAccountDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> UpdateUserAccountAsync(UserAccountDto userAccountDto,
        CancellationToken cancellationToken = default)
    {
        return await _userAccountCrudHelper.UpdateEntity(GetCurrentUser(), userAccountDto, cancellationToken);
    }

    public async Task<EntityResponse<Guid>> DeleteUserAccountAsync(Guid id,
        CancellationToken cancellationToken = default)
    {
        return await _userAccountCrudHelper.DeleteEntity(id, cancellationToken);
    }

    public async Task<EntityResponsePaged<UserAccountListItemDto>> QueryUserAccountAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _userAccountCrudHelper.GetEntityPaged(request, cancellationToken);
    }

    public async Task<EntityResponse> AddUserAccountsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _userAccountCrudHelper.AddEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> UpdateUserAccountsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _userAccountCrudHelper.UpdateEntities(GetCurrentUser(), request, cancellationToken);
    }

    public async Task<EntityResponse> DeleteUserAccountsAsync(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _userAccountCrudHelper.DeleteEntities(request, cancellationToken);
    }

    public async Task<EntityResponse<B64File>> ExportUserAccountExcel(BaseQuery request,
        CancellationToken cancellationToken = default)
    {
        return await _userAccountCrudHelper.ExportExcel(request, cancellationToken);
    }

    public async Task<EntityResponse> ImportUserAccountExcel(B64File file,
        CancellationToken cancellationToken = default)
    {
        return await _userAccountCrudHelper.ImportExcel(GetCurrentUser(), file, cancellationToken);
    }

    #endregion
}