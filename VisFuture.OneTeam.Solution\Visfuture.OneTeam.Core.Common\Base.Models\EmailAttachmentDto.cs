﻿namespace Visfuture.OneTeam.Core.Common.Base.Models
{
    public class EmailAttachmentDto
    {
        public int AttachmentKey { get; set; }
        public int EmailKey { get; set; }
        public string? AttachmentId { get; set; }
        public string? ContentId { get; set; }
        public string? ContentType { get; set; }
        public bool? IsInline { get; set; }
        public DateTime? LastModifiedDateTime { get; set; }
        public string? Name { get; set; }
        public int? Size { get; set; }
        public DateTime? CreatedOn { get; set; }
    }
}
