using Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;
using Visfuture.OneTeam.BaseBiz.BusinessLogic.Requests;
using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.Interfaces;

public interface IRoleManager
{
    #region Role

    Task<EntityResponsePaged<RoleListItemDto>> QueryRoleAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<RoleDto>> GetRoleByIdAsync(Guid roleId, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> AddRoleAsync(RoleDto model, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> UpdateRoleAsync(RoleDto model, CancellationToken cancellationToken = default);
    Task<EntityResponse<Guid>> DeleteRoleAsync(Guid roleId, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddRolesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateRolesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteRolesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse<B64File>> ExportRoleExcel(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> ImportRoleExcel(B64File file, CancellationToken cancellationToken = default);

    #endregion

    #region Role Access

    Task<EntityResponsePaged<RoleAccessListItemDto>> QueryRoleAccessAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<RoleAccessDto>> GetRoleAccessByIdAsync(Guid roleAccessId,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddRoleAccessAsync(RoleAccessDto model, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>>
        UpdateRoleAccessAsync(RoleAccessDto model, CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteRoleAccessAsync(Guid roleAccessId, CancellationToken cancellationToken = default);
    Task<EntityResponse> AddRoleAccessesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateRoleAccessesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteRoleAccessesAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportRoleAccessExcel(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> ImportRoleAccessExcel(B64File file, CancellationToken cancellationToken = default);

    #endregion

    #region Assignable Role

    Task<EntityResponsePaged<AssignableRoleListItemDto>> QueryAssignableRoleAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<AssignableRoleDto>> GetAssignableRoleByIdAsync(Guid roleAccessId,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddAssignableRoleAsync(AssignableRoleDto model,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateAssignableRoleAsync(AssignableRoleDto model,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteAssignableRoleAsync(Guid roleAccessId,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddAssignableRolesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateAssignableRolesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteAssignableRolesAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportAssignableRoleExcel(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> ImportAssignableRoleExcel(B64File file, CancellationToken cancellationToken = default);

    #endregion

    #region Role Assignment

    Task<EntityResponsePaged<RoleAssignmentListItemDto>> QueryRoleAssignmentAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<RoleAssignmentDto>> GetRoleAssignmentByIdAsync(Guid roleAssignmentId,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddRoleAssignmentAsync(RoleAssignmentDto model,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateRoleAssignmentAsync(RoleAssignmentDto model,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteRoleAssignmentAsync(Guid roleAssignmentId,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddRoleAssignmentsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateRoleAssignmentsAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteRoleAssignmentsAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportRoleAssignmentExcel(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> ImportRoleAssignmentExcel(B64File file, CancellationToken cancellationToken = default);

    #endregion

    #region Access Resource

    Task<EntityResponsePaged<AccessResourceListItemDto>> QueryAccessResourceAsync(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<AccessResourceDto>> GetAccessResourceByIdAsync(Guid accessResourceId,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> AddAccessResourceAsync(AccessResourceDto model,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> UpdateAccessResourceAsync(AccessResourceDto model,
        CancellationToken cancellationToken = default);

    Task<EntityResponse<Guid>> DeleteAccessResourceAsync(Guid accessResourceId,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> AddAccessResourcesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> UpdateAccessResourcesAsync(BaseQuery request, CancellationToken cancellationToken = default);
    Task<EntityResponse> DeleteAccessResourcesAsync(BaseQuery request, CancellationToken cancellationToken = default);

    Task<EntityResponse<B64File>> ExportAccessResourceExcel(BaseQuery request,
        CancellationToken cancellationToken = default);

    Task<EntityResponse> ImportAccessResourceExcel(B64File file, CancellationToken cancellationToken = default);

    EntityResponse<List<bool>> MatchAccessResourceAsync(StringListRequest request,
        CancellationToken cancellationToken = default);

    #endregion
}