﻿"restore":{"projectUniqueName":"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Gateway.Web\\Visfuture.OneTeam.Gateway.Web.csproj","projectName":"Visfuture.OneTeam.Gateway.Web","projectPath":"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Gateway.Web\\Visfuture.OneTeam.Gateway.Web.csproj","outputPath":"D:\\Repo\\One team\\VisFuture.OneTeam.Solution\\Visfuture.OneTeam.Gateway.Web\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages","C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages","C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"],"originalTargetFrameworks":["net8.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0":{"targetAlias":"net8.0","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net8.0":{"targetAlias":"net8.0","dependencies":{"Microsoft.AspNetCore.Authentication.JwtBearer":{"target":"Package","version":"[8.0.10, )"},"Swashbuckle.AspNetCore":{"target":"Package","version":"[6.6.2, )"},"Yarp.ReverseProxy":{"target":"Package","version":"[2.2.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}