using Minio;
using Serilog;
using Visfuture.OneTeam.BaseBiz.Api.Extensions;
using Visfuture.OneTeam.BaseBiz.Api.Middlewares;
using Visfuture.OneTeam.BaseBiz.Common;

WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

//******************************************
// Add services to the container.
//******************************************
builder.Services.ConfigureRedisCache(builder.Configuration);
builder.Services.ConfigureDbContext(builder.Configuration);
builder.Services.ConfigureJWT(builder.Configuration);
builder.Services.ConfigureExceptionHandler();
builder.Services.ConfigureCustomServices();
builder.Services.Configure<AppSettings>(builder.Configuration.GetSection("AppSettings"));

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();

builder.Services.AddSwaggerExtension();

builder.Services.AddMapsterServices();

// Add support to logging with SERILOG
builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));
builder.Services.ConfigureCors();

// Add Minio using the custom endpoint and configure additional settings for default MinioClient initialization
builder.Services.AddMinio(configureClient => configureClient
    .WithEndpoint(builder.Configuration["Minio:Endpoint"])
    .WithCredentials(builder.Configuration["Minio:AccessKey"], builder.Configuration["Minio:SecretKey"])
    .Build());

//******************************************
// Tells the application to use the services
//******************************************
WebApplication app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Add support to logging request with SERILOG
app.UseSerilogRequestLogging();
app.UseStatusCodePages();
app.UseExceptionHandler(_ => { });

//app.UseHttpsRedirection();

app.UseRouting();
app.UseCors("CorsPolicy");

app.UseAuthentication();
app.UseAuthorization();

app.UseWhen(context => context.Request.Path.StartsWithSegments("/External"),
    appBuilder => { appBuilder.UseMiddleware<RsaMiddleware>(); });

//app.MapHealthChecks("/health");
app.MapControllers();

app.Run();