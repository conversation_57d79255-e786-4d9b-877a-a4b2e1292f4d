﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.DTOs;

public partial class NotificationTemplateDto : TenantBaseDto
{
    public string NotificationType { get; set; } = null!;

    public string NotificationMethod { get; set; } = null!;

    public string? Title { get; set; }

    public string Body { get; set; } = null!;

    public string? Sender { get; set; }
}
