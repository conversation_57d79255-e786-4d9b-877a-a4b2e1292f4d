﻿using Visfuture.OneTeam.Core.Common.Base.Models;

namespace Visfuture.OneTeam.BaseBiz.BusinessLogic.ListItemDtos;

public partial class TenantListItemDto : BaseDto
{
    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public string? Address1 { get; set; }

    public string? Address2 { get; set; }

    public string? City { get; set; }

    public string? Province { get; set; }

    public string? PostalCode { get; set; }

    public DateTime EffectiveDate { get; set; }

    public DateTime? ExpireDate { get; set; }

    public bool IsActive { get; set; }

    public string Domain { get; set; } = null!;

    public string? Language { get; set; }

    public string? TimeZone { get; set; }

    public string? ContactName { get; set; }

    public string? ContactPhone { get; set; }

    public string? ContactFax { get; set; }

    public string? ContactEmail { get; set; }
}
