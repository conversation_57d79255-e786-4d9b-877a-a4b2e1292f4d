﻿using System.Text;
using Microsoft.Extensions.Primitives;
using Visfuture.OneTeam.Core.Common.Helpers;

namespace Visfuture.OneTeam.BaseBiz.Api.Middlewares;

public class RsaMiddleware(RequestDelegate next)
{
    private readonly RequestDelegate _next = next ?? throw new ArgumentNullException(nameof(next));

    public async Task Invoke(HttpContext context)
    {
        string bodyStr;
        HttpRequest req = context.Request;
        req.Headers.TryGetValue("Signature", out StringValues signature);
        if (string.IsNullOrEmpty(signature))
        {
            context.Response.StatusCode = 401; // Unauthorized
            return;
        }

        // Allows using the same stream multiple times
        req.EnableBuffering();
        // Arguments: Stream, Encoding, detect encoding, buffer size 
        // AND, the most important: keep stream opened
        using (StreamReader reader = new(req.Body, Encoding.UTF8, true, 1024, true))
        {
            bodyStr = await reader.ReadToEndAsync();
        }

        // Rewind, so the core is not lost when it looks at the body for the request
        req.Body.Position = 0;
        RsaHelper rsaHelper = new(false);
        bool isVerified = rsaHelper.Verify(bodyStr, signature!);
        rsaHelper.Dispose();
        if (!isVerified)
        {
            context.Response.StatusCode = 401; // Unauthorized
            return;
        }

        await _next(context);
    }
}